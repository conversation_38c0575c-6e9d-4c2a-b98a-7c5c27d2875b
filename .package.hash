{"hash": {"@babel/core": "df1f2e1a31ef18f30b22ff8d6a5970cc", "@babel/eslint-parser": "4bc1a2dcd46baece9f128951bde4c53f", "@babel/plugin-proposal-class-properties": "5ecdf4f3fc9b04308ea2efc10762ae7e", "@babel/plugin-proposal-decorators": "757ccfbef47e7e581a93d9d8406d9bec", "@babel/plugin-proposal-do-expressions": "34e40405669f16bfeff85f1a4bf4919d", "@babel/plugin-proposal-export-default-from": "a154227633424f0a00abbb41665882e1", "@babel/plugin-proposal-function-bind": "7e4223f6b8a87f1e5fff532171431ab5", "@babel/plugin-proposal-function-sent": "bc30359370a2aa00fa38dc357052fe3e", "@babel/plugin-proposal-pipeline-operator": "9f5656bf86b377132075f690ecc56ee6", "@babel/plugin-proposal-throw-expressions": "169224f8a117e886fc76af174fe9dfbc", "@babel/plugin-syntax-jsx": "c1ef2d14f26d2e353221a8157e59a1db", "@babel/plugin-syntax-object-rest-spread": "cf8be6ffc7aec8cd8c21b564411c83a6", "@babel/plugin-transform-async-to-generator": "6fafe10847e984c8349c2b1590c11998", "@babel/plugin-transform-modules-commonjs": "1c8deafb0dc32615895b3bb74cbef792", "@babel/plugin-transform-runtime": "634d3333b5376111b055a02f7d031c21", "@babel/preset-env": "aa761a3bbd10c96065259684d08839e9", "@babel/preset-react": "46c5f02a937c15b07ce47bd6c7646e22", "@babel/runtime-corejs2": "76a8500115f96dc832240a3bd9a1ec74", "ahooks": "44a8717c597cd7a9e08e5c3986a6786e", "assets-webpack-plugin": "e0a462cd0c0d70b78d4cad696facc114", "autoprefixer": "********************************", "babel-loader": "c4b80f640f30ed34873ae8219b5c0a35", "babel-plugin-dva-hmr": "b698e31032f60e68d4c2342c2b78072e", "babel-plugin-import": "4c8987b48f04ccd37d50aa4f8bec4016", "babel-plugin-transform-react-remove-prop-types": "372dd04dd0b9f18059f478df426341ad", "babel-plugin-transform-remove-console": "b0f577d412bf67e5c125ea5ff9e0a821", "babel-plugin-ttp-try-catch": "7496414cb4123a4a2e0b358b063358b2", "browserslist": "d65e03b5712bb65b1e90391987bc67fa", "case-sensitive-paths-webpack-plugin": "5f725edd89acc9a491853f3c860a80ec", "classnames": "3ff1f97a04c73a0f657c892a319d96b6", "clean-webpack-plugin": "9dd9e389e554487a621ea6a4f33ac74f", "cross-env": "df52126d087b72cfd3a2d9b62a01dff6", "css-loader": "0251d6e51cb1ac0c64735e4d0ce845c3", "css-minimizer-webpack-plugin": "f33f2177dd0741fba623014184dfcccf", "dva": "053626354fb4e99cf3a0854be41e1c4f", "dva-loading": "ccc383e2847176a5fa33f89b83fc865b", "esbuild-loader": "e67dab24526eb74a6920a3d5f78e6841", "eslint": "9361554dd1301281b855feb351a1a471", "eslint-config-prettier": "c650eb6e406c679f79ec856354767ff8", "eslint-config-ttpai": "3f6d9a5ff98615a54cc1811d56f87676", "eslint-plugin-jsdoc": "380881029030c220b987dbf149bed77e", "eslint-plugin-prettier": "b952c1359c2ab3705fd2b4cb37bc8482", "eslint-plugin-react": "00fab611a5e552709ac1b28bc7ab0501", "eslint-plugin-ttpai": "68f3ad3493c84156323d77ccfc2140d3", "eslint-webpack-plugin": "768310a20eccc3a2b52c5b6909f128a8", "file-loader": "2ddaf73167b4a905c9276f6bb1596745", "handlebars": "6a5af54a3b03fe7ca51949142d07654a", "handlebars-loader": "07c759016ba49c2e68f8ba0b328a90b8", "history": "47bf62e8f6f34eb47dd872142d795817", "html-webpack-plugin": "9cc18d6139f08fa1553a6d8b8a64bc52", "husky": "339a1723638960c3de2903c7fa6bccf3", "isomorphic-fetch": "6a3f93d5e67d0342bff2a60dda57c805", "js-cookie": "e5a3ee112bd5d6da6bc3dad12d665853", "less": "7899b3b8310f8375de8cb0a0af369fd7", "less-loader": "1eae2f6c87dc210d4362f16fa50abc40", "lint-staged": "44f79af46e6a01e66b742e555a0e6d41", "mini-css-extract-plugin": "73073432cbb66077dc1b81c9d42622c2", "postcss": "2790a4cdbb237120b88d21a4129bf795", "postcss-loader": "bc37312655ffd6b0f13e9d211c9b5a79", "postcss-ttp-sprites": "39ad7db615d3380193a766e9209d1f0b", "prettier": "4cda3d405a3d37cca06b2af88beb7270", "progress-bar-webpack-plugin": "c5026313e5c56b069479d9c50212dd7b", "prop-types": "1803f21cda3a81c808cccb7592217498", "react": "07731d8e87ac597b5ae8cbec49cd0706", "react-dom": "fe59783f3281199c5612c5a3cdeac5e7", "react-hot-loader": "668b4a7b9b43fc0135ca534f4efca87f", "react-redux": "e3c71a38ee6fdc1d523a679b2cef1064", "react-router-dom": "a22da7aa50922c047b4796dd5b319151", "react-router-redux": "6b66ff9823c6c6e068c544258e709352", "react-slider": "74d630e41b5f071a606e9bae4d8a4ab5", "redbox-react": "********************************", "redux": "67e791a7332cc2f2a4693614aa2142a4", "style-loader": "59c7ac883e38b215db031cbf0eb01555", "terser-webpack-plugin": "b39da020f2d876fee1fcba8d8f2f39f8", "text-loader": "ca350c24d01c35938e0fcce70caaddce", "thread-loader": "660a33ea5514df6a49ca0945bf37d7fc", "ttp-library": "c37356e13b05c42ad85832dcd822569f", "ttp-utils": "0a493827b763b4fe8b23133422cc8189", "ttpai-config": "c505dd5c0d795e25e4cb23a4cec40939", "url-loader": "81cc0eb9fcdf4cb7b58425dfc3723f8c", "webpack": "40432ef7c166aaf844e923be665f2eae", "webpack-bundle-analyzer": "a534ef937d8e85f2800b8239d0c42168", "webpack-cli": "be77d164ca3d642e6ac6757ef461160d", "webpack-dev-server": "5a7435aa7f770fd3adb2b55464d11ea8", "webpack-merge": "3e99ab112b35219003259c71433392ad", "other": "605b16614542cff0ab3829d66478667f", "npmList": "8d6bc43a41e81d9ac928211a9a95cff7"}, "version": "1.3.12"}