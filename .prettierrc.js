/* eslint-disable */
module.exports = {
    // 一行的字符数，如果超过会进行换行，默认为80
    printWidth: 110,
    // 一个tab代表几个空格数，默认为80
    tabWidth: 4,
    // 是否使用tab进行缩进，默认为false，表示用空格进行缩减
    useTabs: false,
    // 行位是否使用分号，默认为true
    semi: true,
    // 字符串是否使用单引号，默认为false，使用双引号
    singleQuote: true,
    // 在JSX中使用单引号而不是双引号
    jsxSingleQuote: true,
    // 是否使用尾逗号，有三个可选值"<none|es5|all>"
    trailingComma: 'none',
    // 对象大括号直接是否有空格，默认为true，效果：{ foo: bar }
    bracketSpacing: true,
    // 将>多行JSX元素放在最后一行的末尾，而不是单独放在下一行（不适用于自闭元素）
    jsxBracketSameLine: true,
    // 在单个箭头函数参数周围加上括号。"avoid" - 尽可能省略parens | "always" - 始终包括parens
    arrowParens: 'always'
};
