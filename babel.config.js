module.exports = {
    plugins: [
        ['@babel/plugin-transform-runtime', { corejs: 2 }],
        ['@babel/plugin-proposal-decorators', { legacy: true }],
        '@babel/plugin-proposal-class-properties',
        '@babel/plugin-proposal-function-sent',
        '@babel/plugin-proposal-throw-expressions',
        '@babel/plugin-proposal-export-default-from',
        ['@babel/plugin-proposal-pipeline-operator', { proposal: 'minimal' }],
        '@babel/plugin-proposal-do-expressions',
        '@babel/plugin-proposal-function-bind'
    ],
    presets: [['@babel/preset-env', { useBuiltIns: 'usage', corejs: 2 }], '@babel/preset-react'],
    env: {
        development: {
            plugins: ['dva-hmr']
        },
        production: {
            plugins: ['transform-remove-console', 'transform-react-remove-prop-types']
        }
    }
};
