/**
 * 公共变量设置
 */
// JS
import PORT from 'port';
import { setObjectToArray } from './utils.js';
import { PAGE_URL } from 'routes/router-config.js';
// 图片
import DefPic from '../images/def-pic.png';

/**
 * 默认没有图片的车源占位图
 *
 * @type {string}
 */
export const DEF_CAR_PIC = DefPic;

/**
 * COOKIECONFIG 登录 cookie 参数
 *
 * @type {object}
 */
export const COOKIECONFIG = {
    path: '/',
    expires: 1,
    domain: location.hostname
};

/**
 * 所有请求URL统一管理
 *
 * @type {object}
 */
export const PORT_URL = PORT;

/**
 * 我的客户-TAB标签数据
 * @type  {Array}
 */
export const MY_CLIENT_TAB = [
    { title: '我的业绩', url: PAGE_URL.myWork },
    { title: '我的客户', url: PAGE_URL.myClient, icon: 'icon-info' }
];

/**
 * 我我的团队-TAB标签数据
 * @type  {Array}
 */
export const MANAGER_TAB = [
    { title: '绩效任务', url: PAGE_URL.managerKpi },
    { title: '其他任务', url: '', disabled: true }
];

/**
 * 车源智推-TAB标签数据
 * @type  {Array}
 */
export const RECOMMEND_TAB = [
    { title: '今日推荐', url: PAGE_URL.recommendToday },
    { title: '历史推荐', url: PAGE_URL.recommendHistory }
];

/**
 * 我我的团队看板-TAB标签数据
 * @type  {Array}
 */
export const MANAGER_PANEL_TAB = [
    { title: '业绩看板', url: PAGE_URL.managerPerformancePanel },
    { title: '团队绩效', url: PAGE_URL.managerPanel }
];

/**
 * 意向标签-tag-map
 * @type  {object}
 */
export const FOLLOW_UP_TAG_MAP = {
    1: { label: '有意向', type: 'info' },
    2: { label: '无意向', type: 'danger' },
    3: { label: '待定', type: 'default' }
};

/**
 * 意向标签-MAP
 * @type  {object}
 */
export const FOLLOW_UP_MAP = {
    1: '有意向',
    2: '无意向',
    3: '待定'
};

/**
 * 意向标签-OPTION
 * @type  {Array}
 */
export const FOLLOW_UP_OPTION = setObjectToArray(FOLLOW_UP_MAP);

/**
 * 是否已购权益包定金	0 未购买 1 已购买
 * @type  {object}
 */
export const BUY_VIP_KEY = {
    YES: 1,
    NO: 0
};

/**
 * 是否已购权益包定金	0 未购买 1 已购买
 * @type  {object}
 */
export const BUY_VIP_MAP = {
    [BUY_VIP_KEY.YES]: '是',
    [BUY_VIP_KEY.NO]: '否'
};

/**
 * 是否已购权益包定金	0 未购买 1 已购买
 * @type  {Array}
 */
export const BUY_VIP_OPTION = setObjectToArray(BUY_VIP_MAP);

/**
 * 竞拍结果: 1-已成交,2-已中标
 * @type  {object}
 */
export const AUCTION_RESULT_KEY = {
    traded: 1,
    winBid: 2
};

/**
 * 竞拍结果: 1-已成交,2-已中标
 * @type  {object}
 */
export const AUCTION_RESULT_MAP = {
    [AUCTION_RESULT_KEY.traded]: '已成交',
    [AUCTION_RESULT_KEY.winBid]: '已中标'
};

/**
 * 竞拍结果: 1-已成交,2-已中标
 * @type  {Array}
 */
export const AUCTION_RESULT_OPTION = setObjectToArray(AUCTION_RESULT_MAP);
