/**
 * fetchAPI封装
 *
 */
import { getFetch, postFetch } from 'ttp-utils';

/**
 * get请求API
 *
 * @param  {object} opts  请求参数
 * @returns {Promise}     返回promise
 */
export const request = (opts) => {
    const { method = 'GET', config = {}, ...other } = opts;
    const defaultConfig = {
        errorMessage: '获取数据失败！请重试',
        headers: {
            'Content-Type': 'application/json; charset=UTF-8'
        }
    };
    let ttpFetch;
    if (method === 'GET') {
        ttpFetch = getFetch;
    } else if (method === 'POST') {
        ttpFetch = postFetch;
    } else {
        return Promise.reject(`传入了不支持的方法名${method}`);
    }

    return ttpFetch({ ...other, config: { ...defaultConfig, ...config } });
};

/**
 * get feach的post请求
 *
 * @param    {object}                 options      参数
 * @param    {string}                 options.url  请求的URL
 * @param    {string}                 options.data 传入请求参数
 * @returns  {Promise}                             promise 的 Fetch 对象
 */
export const getRequest = (options) => request({ method: 'GET', ...options });

/**
 * post feach的post请求
 *
 * @param    {object}                 options      参数
 * @param    {string}                 options.url  请求的URL
 * @param    {string}                 options.data 传入请求参数
 * @returns  {Promise}                             promise 的 Fetch 对象
 */
export const postRequest = (options) => request({ method: 'POST', ...options });
