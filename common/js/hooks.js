// JS
import { useState, useRef, useCallback } from 'react';
import { throttle } from 'ttp-utils';

/**
 * @typedef  {object} MouseMoveData
 * @property {object}                position        位置信息对象
 * @property {number}                position.x      鼠标X坐标
 * @property {number}                position.x      鼠标Y坐标
 * @property {Function}              handleMouseDown 处理鼠标/触摸开始事件的函数
 */

/**
 * 鼠标/触摸拖动 Hook
 * @param {object}                options          配置选项
 * @param {boolean}               options.isMove   是否可移动，默认为true
 * @param {number}                options.initialX 初始X坐标，默认为0
 * @param {number}                options.initialY 初始Y坐标，默认为0
 * @returns {MouseMoveData}                        拖动相关的状态和处理函数
 */
export const useMouseMove = (options = {}) => {
    const { isMove = true, initialX = 0, initialY = 0 } = options;

    // 移动的坐标点
    const [position, setPosition] = useState({ x: initialX, y: initialY });
    const isDragging = useRef(false);
    const startXRef = useRef(0);
    const startYRef = useRef(0);

    /**
     * 鼠标/触摸移动事件处理
     * 使用useCallback优化事件处理函数，避免每次渲染都创建新函数
     */
    const handleMouseMove = useCallback(
        throttle(
            (moveEvent) => {
                if (isDragging.current) {
                    const clientX = moveEvent.clientX || moveEvent.touches?.[0].clientX;
                    const clientY = moveEvent.clientY || moveEvent.touches?.[0].clientY;

                    setPosition((prevPosition) => ({
                        x: prevPosition.x + (clientX - startXRef.current),
                        y: prevPosition.y + (clientY - startYRef.current)
                    }));

                    // 更新起始坐标，避免累积误差
                    if (moveEvent.touches) {
                        startXRef.current = clientX;
                        startYRef.current = clientY;
                    }
                }
            },
            30,
            1000 / 60
        ),
        []
    );

    /**
     * 鼠标/触摸抬起事件处理
     */
    const handleMouseUp = useCallback(() => {
        isDragging.current = false;
        window.removeEventListener('mousemove', handleMouseMove);
        window.removeEventListener('mouseup', handleMouseUp);
        window.removeEventListener('touchmove', handleMouseMove);
        window.removeEventListener('touchend', handleMouseUp);
        document.body.classList.remove('ttp-overflow-hidden');
    }, [handleMouseMove]);

    /**
     * 拖动事件处理
     * @param   {object}                e 事件对象
     * @returns {void}
     */
    const handleMouseDown = useCallback(
        (e) => {
            if (!isMove) {
                e.preventDefault();
                e.stopPropagation();
                return;
            }
            // 锁定页面滚动
            // ttp-overflow-hidden 是组件库（ttp-library）中暴露出来的样式类名
            document.body.classList.add('ttp-overflow-hidden');
            isDragging.current = true;
            startXRef.current = e.clientX || e.touches?.[0].clientX;
            startYRef.current = e.clientY || e.touches?.[0].clientY;

            window.addEventListener('mousemove', handleMouseMove);
            window.addEventListener('mouseup', handleMouseUp);
            window.addEventListener('touchmove', handleMouseMove);
            window.addEventListener('touchend', handleMouseUp);
        },
        [isMove, handleMouseMove, handleMouseUp]
    );

    return {
        position,
        handleMouseDown
    };
};
