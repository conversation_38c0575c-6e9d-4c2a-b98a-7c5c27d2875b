/**
 * Feach 异步接口转成  MOCK 接口
 */

/* JS */
import port from './port';

const mockHost = '/mockapi';
const host = ['//m.ttpai.cn', '//crmv2.ttpai.cn', '//store.boss.ttpai.cn'];
const mockPortData = {};
const portData = {};

for (let key in port) {
    let regExp = new RegExp(`^${host.join('|')}`);
    if (regExp.test(port[key])) {
        mockPortData[key] = port[key].replace(regExp, mockHost);
    } else {
        mockPortData[key] = mockHost + port[key];
    }
    portData[key] = mockPortData[key];
}

export default portData;
