export default {
    /**
     * 公共接口
     */
    // 获取客户跟进记录下拉框基础数据
    getFollowRecord: '/api/myCustomer/followup/getFollowRecordData',
    // 时间进度条: http://confluence.ttpai.cn/pages/viewpage.action?pageId=********
    monthpercentage: '/api/kpi/overview/monthpercentage',
    // 获取团队人员数据
    teamList: '/api/kpi/group/overview/teams',

    /**
     * 登录
     */
    // 登录
    login: '/api/login',
    // 查询登录信息
    loginInfo: '/api/login/account',
    // 企微登录，通过code换取登录信息
    exchangeCode: '/api/login/code2user',
    // 是否必須企微登录
    mustWeChat: '/api/login/mustWeChat',
    // 企业微信SDK初始化
    weixin: '/api/login/jssdk',
    // agent ticket
    workweixin: '/api/login/agentTicket',

    /**
     * 首页
     */
    // 查询团队kpi 提车量总览数据
    rankList: '/api/kpi/group/overview/deliveryVolume',

    /**
     * 我的客户
     * 接口文档：http://confluence.ttpai.cn/pages/viewpage.action?pageId=********
     */
    // 标签列表
    labelCode: '/api/myCustomer/labelCodeList',
    // 客户列表
    dealerList: '/api/myCustomer/dealerList',
    // 90天不在跟进
    saveAbandonFollow: '/api/myCustomer/followup/saveAbandonFollow',
    // 推荐车源：http://confluence.ttpai.cn/pages/viewpage.action?pageId=********
    recommendDetailList: '/api/myCustomer/recommend/ai/auctionList',
    // 确认推荐车源
    saveRecommendList: '/api/myCustomer/recommend/ai/saveAuctions',
    // 外呼
    outCall: '/api/myCustomer/outCall',

    /**
     * 我的客户-车商详情页
     * 接口文档：http://confluence.ttpai.cn/pages/viewpage.action?pageId=********
     */
    // 获取车商详情
    dealerDetail: '/api/myCustomer/dealerDetail',

    /**
     * 我的客户-跟进记录：
     * 接口文档：http://confluence.ttpai.cn/pages/viewpage.action?pageId=16913027
     */
    // 我的客户跟进列表
    recordList: '/api/myCustomer/followup/recordList',
    // 跟进详情页面
    recordDetail: '/api/myCustomer/followup/getAddFollowRecordInfo',
    // 保存跟进记录
    saveRecord: '/api/myCustomer/followup/save',
    // 获取 store.boss 的token
    getStoreToken: '/file/image/getBossStoreToken',
    // 获取七牛上传域名地址
    getDomain: '/file/qiniu/upload/domain',
    // 获取七牛上传token
    getQiniuToken: '/file/generate/qiniu/upload/token',
    // 员工列表数据
    getWorkerListPort: '/api/kpi/group/member/options',
    // 获取录音数据
    getAudioList: '/api/myCustomer/followup/getRecordUrl',

    /**
     * 员工轨迹
     * 接口文档：http://dealer-crmv2-web.ttpai.top/swagger-ui.html#/KpiController/getMyWorkingTrackUsingGET
     */
    // 获取员工工作轨迹数据：
    getWorkTracePort: '/api/kpi/my/kpi/working/track',

    /**
     * 我的工作
     * 接口文档：http://confluence.ttpai.cn/pages/viewpage.action?pageId=********
     */
    // 我的工作-查询我的绩效（更多绩效）
    kpiList: '/api/kpi/my/list',
    // 我的工作-客户分层数据
    customerOverview: '/api/kpi/my/kpi/customer/overview',
    // 我的工作-客户跟进记录(少数几条)
    customerFollow: '/api/kpi/my/kpi/customer/follow/v2',
    // 我的工作-客户跟进更多记录
    customerFollowMore: '/api/kpi/my/kpi/customer/follow/more/v2',

    /**
     * 我的团队-绩效任务（开发环境）
     * 接口文档 http://dealer-crmv2-web.ttpai.top/swagger-ui.html#/KpiController
     */
    // 团队KPI
    groupKpi: '/api/kpi/group/overview',
    // 按员工查询
    groupKpiByUser: '/api/kpi/group/member/list',
    // 按绩效查询
    groupKpiByKpi: '/api/kpi/group/indicator/list',
    // 批量导入：多个绩效
    // 文档：http://confluence.ttpai.cn/pages/viewpage.action?pageId=********
    saveKpiList: '/api/kpi/batch/import',
    // 新增：单个保存绩效
    saveKpiItem: '/api/kpi/group/kpi/create',
    // 编辑：单个保存绩效
    updateKpiItem: '/api/kpi/group/kpi/update',
    // 获取单个商拓的KPI数据
    getKpiItem: '/api/kpi/group/kpi/dialog',
    // 查询可制定的绩效枚举
    // getKpiMap: '/api/kpi/group/indicator/enums',
    getKpiMapV2: '/api/kpi/group/indicator/enums/v2',

    /**
     * 我的客户-跟进记录详情
     */
    // 我的团队的历史绩效
    groupKpiHistory: '/api/kpi/group/member/list',

    /**
     * 团队绩效-业绩看板
     */
    // 业绩看板
    kpiPanel: '/api/kpi/group/kanban/data',

    /**
     * 我的团队-团队绩效
     * http://confluence.ttpai.cn/pages/viewpage.action?pageId=********
     */
    // 查询所有团队的绩效数据
    teamKpiList: '/api/kpi/group/teams/overview',
    teamKpiListV2: '/api/kpi/group/teams/overview/v2',

    /**
     * 车源智推-今日推荐车源
     */
    // 今日推荐车源列表
    getTodayRecommendList: '/api/recommend/today/auction/list',
    // 推荐给车商
    pushToDealer: '/api/recommend/push',
    // 获取车商列表
    getDealerList: '/api/recommend/search/dealer/page',
    // 获取拍品标签
    getAuctionTag: '/api/recommend/auction/label/codes',

    /**
     * 车源智推-历史推荐车源
     * 接口文档（线下环境）：http://dealer-crmv2-web.ttpai.top/swagger-ui.html#/AuctionRecommendController
     */
    // 历史推荐列表-经销商维度
    historyDealerList: '/api/recommend/history/page',
    // 历史推荐列表-经销商名下车源维度
    historyAuctionList: '/api/recommend/history/auction/page'
};
