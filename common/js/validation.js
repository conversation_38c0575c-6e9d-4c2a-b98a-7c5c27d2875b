import { isEmpty, isEmptyObject, isFun } from 'ttp-utils';
/**
 * 验证规则
 * @type {object}
 */
export const VALID_REGEX_MAP = {
    // 手机号校验规则
    mobile: /^(1[0-9]{10}|666[0-9]{8})$/,
    // 银行卡
    accountNumber: /^([0-9]{16,19})$/,
    // 密码
    password: /^([0-9]{6})$/,
    // 账号名称
    accountName: /[A-Za-z\u4e00-\u9fa5]{2,99}$/,
    // 银行
    bankName: /(^[A-Za-z\u4e00-\u9fa5]{1,20}$)/,
    // 身份证
    idCard: /(^\d{15}$)|(^\d{17}([0-9]|X)$)/
};

/**
 * 绩效校验规则
 * @param   {string}                 key      数据校验的KEY
 * @param   {string}                 formData 所有数据
 * @returns {object}                       默认参数
 */
export const KpiDrawerMap = (key, formData) => {
    /**
     * 指标类型
     * 1:数字指标，2:百分比指标
     * @type {object}
     */
    const IndicatorTypesMap = {
        1: 'number',
        2: 'percent'
    };
    const isNumber = IndicatorTypesMap[formData.indicatorType] != 'percent';
    const data = {
        // 员工
        memberId: [
            {
                regex: /^\S+$/,
                message: '* 员工不能为空'
            }
        ],
        // 绩效时间,例如: 2025-03
        kpiTime: [
            {
                // eslint-disable-next-line jsdoc/require-jsdoc
                fun: (value) => !isEmpty(value),
                message: '* 绩效时间不能为空'
            }
        ],
        // 绩效指标
        indicatorCode: [
            {
                regex: /^\S+$/,
                message: '* 绩效指标不能为空'
            }
        ],

        // KPI目标值
        targetValue: [
            {
                regex: /^\S+$/,
                message: '* 目标值不能为空'
            },
            {
                regex: isNumber ? /^([1-9](\d{1,4})?)$/ : /^(100|[1-9]\d?)$/,
                message: isNumber ? '* 目标值必须是1-99999的整数' : '* 目标值必须是1-100之间，且只能是整数'
            }
        ],
        // KPI目标值权重
        targetValueWeight: [
            {
                regex: /^\S+$/,
                message: '* 目标值权重不能为空'
            },
            {
                regex: /^(100|[1-9]\d?)$/,
                message: '* 目标值权重必须是1-100之间，且只能是整数'
            }
        ]
    };

    return data[key] || [];
};

/**
 * 批量上传校验
 * @param   {string}                 key   数据校验的KEY
 * @returns {object}                       默认参数
 */
export const builkImputMap = (key) => {
    const data = {
        // 绩效时间
        timeInterval: [
            {
                regex: /^\S+$/,
                message: '* 绩效时间不能为空'
            }
        ],
        // 绩效时间
        businessType: [
            {
                regex: /^\S+$/,
                message: '* 业务类型不能为空'
            }
        ],
        // 上传的文件
        file: [
            {
                // eslint-disable-next-line jsdoc/require-jsdoc
                fun: (value) => !isEmpty(value) && !isEmptyObject(value),
                message: '* 上传的文件不能为空'
            }
        ]
    };
    return data[key] || [];
};

/**
 * 绩效校验规则
 * @param   {object}             formData         需要验证的值
 * @param   {object}             getValidationMap 获取验证数据
 * @returns {object}                              默认参数
 */
const ValidationFormData = (formData, getValidationMap) => {
    let validation = true;
    const msgData = Object.entries(formData).reduce((acc, [key, value]) => {
        const rules = getValidationMap(key, formData);
        const message = rules
            .filter((item) => {
                if (isFun(item.fun)) {
                    console.log('item.fun(value)', item.fun(value), value);
                    return item.fun(value) == false;
                }
                console.log('item.regex', item.regex.test(value.toString()), value.toString());
                return item.regex.test(value.toString()) == false;
            })
            .map((item) => item.message);
        if (message.length > 0) {
            validation = false;
            acc[key] = message[0];
        } else {
            acc[key] = '';
        }
        return acc;
    }, {});
    return validation ? null : msgData;
};

export default ValidationFormData;
