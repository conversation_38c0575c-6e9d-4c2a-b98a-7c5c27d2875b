/**
 * @file     企业微信SDK相关方法封装
 * @module   common/js/wx
 * <AUTHOR>
 */

// JS
import { postRequest } from 'common/js/fetch';
import { PORT_URL } from 'common/js/constant';
import { isEmpty } from 'ttp-utils/lib';

/**
 * WX_JS_API_LIST 注册企业微信的API
 *
 * @type {Array}
 */
export const WX_JS_CONFIG_API_LIST = [
    'onHistoryBack',
    'showMenuItems',
    'getLocation',
    'onMenuShareWechat',
    'onMenuShareTimeline',
    'setClipboardData',
    'onMenuShareAppMessage'
];

export const WX_JS_AGENT_CONFIG_API_LIST = [];

/**
 * isInitWxSuccess 判断微信初始化是否成功
 * @type {boolean}
 */
let isInitWxSuccess = false;

/**
 * getWxConfigOption 获取企业微信 wx.config 必要参数
 *
 * <AUTHOR>
 * @param    {string}                 url 当前URL，#前面的
 * @param    {string}                 urlType 请求接口类型 默认weixin
 * @returns  {Promise}                    返回一个Feach的Promise对象
 */
function getWxConfigOption(url = '', urlType = 'weixin') {
    let opts = {
        url: PORT_URL[urlType],
        data: {
            url
        }
    };
    return postRequest(opts).then((res) => res.result);
}

/**
 * 初始化agentConfig
 * ios必须等到页面加载完才会注入，社区官方建议先初始化完config再初始化agentConfig
 * https://developers.weixin.qq.com/community/develop/article/doc/00022417118c78d4448af86625b413
 *
 * @param {string} LOCSTION_URL 当前路径
 * @returns {Promise} Promise
 */
function initAgentConfig(LOCSTION_URL) {
    return new Promise((resolve, reject) => {
        getWxConfigOption(LOCSTION_URL, 'workweixin').then((res) => {
            wx.agentConfig({
                corpid: res.corpId, // 必填，企业微信的corpid，必须与当前登录的企业一致
                agentid: +res.agentId, // 必填，企业微信的应用id （e.g. 1000247）
                timestamp: res.timestamp, // 必填，生成签名的时间戳
                nonceStr: res.nonceStr, // 必填，生成签名的随机串
                signature: res.signature, // 必填，签名，见附录-JS-SDK使用权限签名算法
                jsApiList: WX_JS_AGENT_CONFIG_API_LIST, // 必填，传入需要使用的接口名称
                // eslint-disable-next-line jsdoc/require-jsdoc
                success: function (res) {
                    resolve(res);
                },
                // eslint-disable-next-line jsdoc/require-jsdoc
                fail: function (res) {
                    if (res.errMsg.indexOf('function not exist') > -1) {
                        reject({
                            text: '您的企业微信版本过低请升级'
                        });
                    }
                    reject(res);
                }
            });
        });
    });
}

/**
 * initWxConfig 初始化企业微信
 * <AUTHOR>
 * @param    {string}                 LOCSTION_URL 当前URL，#前面的
 * @returns  {Promise}                             返回一个Feach的Promise对象
 */
export function initWxConfig(LOCSTION_URL) {
    // 获取微信
    return new Promise((resolve, reject) => {
        getWxConfigOption(LOCSTION_URL).then((res) => {
            console.log('getWxConfigOption-OK', res);
            // 通过config接口注入权限验证配置
            // 所有需要使用JS-SDK的页面必须先注入配置信息，否则将无法调用（同一个url仅需调用一次，对于变化url的SPA的web app可在每次url变化时进行调用）。
            wx.config({
                beta: true, // 必须这么写，否则wx.invoke调用形式的jsapi会有问题
                debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                appId: res.corpId, // 必填，企业微信的corpID
                timestamp: res.timestamp, // 必填，生成签名的时间戳
                nonceStr: res.nonceStr, // 必填，生成签名的随机串
                signature: res.signature, // 必填，签名，见附录1
                jsApiList: WX_JS_CONFIG_API_LIST // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
            });
            // 通过ready接口处理成功验证
            wx.ready(() => resolve(res));

            // 通过error接口处理失败验证
            wx.error((error) => reject(error));
        });
    });
}

/**
 * 初始化微信配置
 *
 * @param {boolean} needAgentConfig 是否需要初始化agentConfig
 * @returns {Promise} Promise
 */
export function initWx(needAgentConfig = false) {
    if (isEmpty(wx)) {
        return Promise.reject('wx is not defined');
    }
    isInitWxSuccess = false;
    const LOCSTION_URL = window.location.href.split('#')[0];
    if (needAgentConfig) {
        return initWxConfig(LOCSTION_URL)
            .then(() => initAgentConfig(LOCSTION_URL))
            .then((res) => {
                isInitWxSuccess = true;
                return res;
            });
    }
    return initWxConfig(LOCSTION_URL).then((res) => {
        isInitWxSuccess = true;
        return res;
    });
}

/**
 * getWxInitSate 判断企业微信 SDK 授权是否成功
 * <AUTHOR>
 * @returns  {boolean}                             true/false
 */
export function getWxInitSate() {
    return isInitWxSuccess;
}

/**
 * setHistoryBack 设置微信返回按钮的回调函数
 * @param   {Function}  callback                  回调函数
 */
export function setHistoryBack(callback) {
    wx.onHistoryBack(function () {
        callback;
        return false;
    });
}

/**
 * getWxGPSLocation 获取微信GPS定位的经纬度
 * <AUTHOR>
 * @param    {string}                 type type 坐标类型 默认'wgs84'
 * @returns  {Promise}                     返回一个Feach的Promise对象
 */
export function getWxGPSLocation(type = 'wgs84') {
    return new Promise((resolve, reject) => {
        if (!isInitWxSuccess) {
            reject('initWx', isInitWxSuccess);
        }
        wx.getLocation({
            // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
            type,
            // eslint-disable-next-line jsdoc/require-jsdoc
            success: (res) => {
                // var latitude = res.latitude; // 纬度，浮点数，范围为90 ~ -90
                // var longitude = res.longitude; // 经度，浮点数，范围为180 ~ -180。
                // var speed = res.speed; // 速度，以米/每秒计
                // var accuracy = res.accuracy; // 位置精度
                // console.log("维度:" + latitude + ",经度:" + longitude + ",speed:" + speed + ",位置精度：" + accuracy);
                let point = {
                    lng: res.longitude,
                    lat: res.latitude
                };
                resolve(point);
            },
            // eslint-disable-next-line jsdoc/require-jsdoc
            fail: (res) => reject('wx.getLocation', res)
        });
    });
}
