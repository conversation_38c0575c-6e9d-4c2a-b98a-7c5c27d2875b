@import 'validation.less';
@import 'mixins.less';

.libraryDrawer() {
    :global {
        .Saitama-drawer-box {
            min-height: 300 / @rem;
        }
    }
}

.libraryCalendar() {
    :global {
        .Saitama-calendar .react-calendar__year-view__months__month {
            width: 30% !important;
            border-radius: 4 / @rem;
        }
    }
}

.librarySearchbar() {
    :global {
        .Saitama-searchbar-input {
            border-radius: 4 / @rem;
        }
    }
}

.librarySelectPerson() {
    :global {
        .worker-drawer {
            .Saitama-list-group-tag .Saitama-list-group-box .Saitama-tag.Saitama-checkbox {
                width: calc((100% - 0.5rem * 3) / 4);
                border-radius: 4 / @rem;
                margin-right: 0.5rem;

                &:nth-child(4n) {
                    margin-right: 0;
                }
            }

            .Saitama-tag-box {
                font-size: 12 / @rem;
            }
        }
    }
}

:global {
    .Saitama-dropdown-item .Saitama-list-group-tag .Saitama-list-group-box .Saitama-tag.Saitama-radio {
        padding: (6 / @rem) (10 / @rem);
    }
}
