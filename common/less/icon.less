@import 'validation.less';
@import 'mixins.less';

:global {
    // 小图标
    [class^='icon-'],
    [class*='icon-'] {
        vertical-align: middle;
        background-repeat: no-repeat;
        background-attachment: scroll;
        background-position: 0 0;
        background-size: cover;
        .inline-block();
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    .icon-S,
    .icon-A,
    .icon-B {
        width: 17 / @rem;
        height: 17 / @rem;
        line-height: 17 / @rem;
        text-align: center;
        border-radius: 2 / @rem;
        margin-right: 5 / @rem;
        font-size: 14 / @rem;
        font-style: normal;
    }

    .icon-S {
        color: #715103;
        background: linear-gradient(180deg, #ffdb7b 0%, #ffecb0 51%, #ffc278 100%);
    }

    .icon-A {
        color: #6b6a90;
        background: linear-gradient(146deg, #c9cbdd 0%, #f8f8f8 54%, #d1d6d8 100%);
    }

    .icon-B {
        color: #7a4226;
        background: linear-gradient(154deg, #f9e3d2 0%, #fff5e7 46%, #fad7c2 100%);
    }

    .icon-nodata {
        background-image: url('../../src/images/icons/icon-nodata.png');
        width: 107 / @rem;
        height: 107 / @rem;
        background-size: contain;
    }

    //404页面
    .icon-404 {
        background-image: url('../../src/images/icons/icon-404.png');
        width: 173 / @rem;
        height: 173 / @rem;
        background-size: contain;
    }

    .icon-person {
        background-image: url('../../src/images/icons/person.png');
        width: 16 / @rem;
        height: 15 / @rem;
        vertical-align: text-bottom;
    }

    .icon-vcode {
        background-image: url('../../src/images/icons/vcode.png');
        width: 17 / @rem;
        height: 19 / @rem;
        vertical-align: text-bottom;
    }

    .icon-user {
        background-image: url('../../src/images/icons/icon-user.png');
        width: 20 / @rem;
        height: 20 / @rem;
        vertical-align: text-bottom;
    }

    .icon-lock {
        background-image: url('../../src/images/icons/icon-lock.png');
        width: 20 / @rem;
        height: 20 / @rem;
        vertical-align: text-bottom;
    }

    .icon-already {
        background-image: url('../../src/images/icons/icon-already.png');
        width: 70 / @rem;
        height: 70 / @rem;
    }

    .icon-recommend {
        background-image: url('../../src/images/icons/icon-recommend.png');
        width: 60 / @rem;
        height: 19 / @rem;
    }

    .icon-select {
        background-image: url('../../src/images/icons/icon-select.png');
        width: 16 / @rem;
        height: 16 / @rem;
    }

    .icon-selected {
        background-image: url('../../src/images/icons/icon-selected.png');
        width: 16 / @rem;
        height: 16 / @rem;
    }

    .icon-select-disabled {
        background-image: url('../../src/images/icons/icon-select-disabled.png');
        width: 16 / @rem;
        height: 16 / @rem;
    }

    // 日常推荐车源数据tag
    .icon-recommend-tag-blue {
        background-image: url('../../src/images/icons/icon-re-tag-blue.png');
        width: 60 / @rem;
        height: 20 / @rem;
        background-size: contain;
    }

    // 日常推荐车源数据tag
    .icon-recommend-tag-red {
        background-image: url('../../src/images/icons/icon-re-tag-red.png');
        width: 60 / @rem;
        height: 20 / @rem;
        background-size: contain;
    }

    // 日常推荐车源数据tag
    .icon-recommend-tag-yellow {
        background-image: url('../../src/images/icons/icon-re-tag-yellow.png');
        width: 70 / @rem;
        height: 20 / @rem;
        background-size: contain;
    }

    .icon-recommend-tag-bluest {
        background-image: url('../../src/images/icons/icon-re-tag-bluest.png');
        width: 60 / @rem;
        height: 19 / @rem;
    }

    .icon-play-video {
        background-image: url('../../src/images/icons/icon-play-video.png');
        width: 20 / @rem;
        height: 20 / @rem;
    }

    .icon-play-audio {
        background-image: url('../../src/images/icons/icon-play-audio.png');
        width: 18 / @rem;
        height: 24 / @rem;
    }

    .icon-grow-up {
        background-image: url('../../src/images/icons/icon-grow-up.png');
        width: 10 / @rem;
        height: 6 / @rem;
    }

    .icon-grow-down {
        background-image: url('../../src/images/icons/icon-grow-down.png');
        width: 10 / @rem;
        height: 6 / @rem;
    }

    .icon-great {
        background-image: url('../../src/images/icons/icon-great.png');
        width: 50 / @rem;
        height: 56 / @rem;
    }

    .icon-panel-blue {
        background-image: url('../../src/images/icons/icon-panel-blue.png');
        width: 18 / @rem;
        height: 18 / @rem;
    }

    .icon-panel-orange {
        background-image: url('../../src/images/icons/icon-panel-orange.png');
        width: 18 / @rem;
        height: 18 / @rem;
    }

    .icon-upload-file {
        background-image: url('../../src/images/icons/icon-upload-file.png');
        width: 84 / @rem;
        height: 64 / @rem;
    }

    .icon-folder {
        background-image: url('../../src/images/icons/icon-folder.png');
        width: 84 / @rem;
        height: 64 / @rem;
    }

    .icon-home-blue {
        background-image: url('../../src/images/icons/icon-home-blue.png');
        width: 44 / @rem;
        height: 44 / @rem;
    }

    .icon-home-orange {
        background-image: url('../../src/images/icons/icon-home-orange.png');
        width: 44 / @rem;
        height: 44 / @rem;
    }

    .icon-home-recommend {
        background-image: url('../../src/images/icons/icon-home-recommend.png');
        width: 44 / @rem;
        height: 44 / @rem;
    }

    .icon-home-arrow-blue {
        background-image: url('../../src/images/icons/icon-home-arrow-blue.png');
        width: 8 / @rem;
        height: 14 / @rem;
    }

    .icon-home-arrow-orange {
        background-image: url('../../src/images/icons/icon-home-arrow-orange.png');
        width: 8 / @rem;
        height: 14 / @rem;
    }

    .icon-change {
        background-image: url('../../src/images/icons/icon-change.png');
        width: 14 / @rem;
        height: 14 / @rem;
    }

    .icon-info {
        background-image: url('../../src/images/icons/icon-info.png');
        width: 20 / @rem;
        height: 20 / @rem;
    }

    .icon-search {
        background-image: url('../../src/images/icons/icon-search.png');
        width: 16 / @rem;
        height: 17 / @rem;
    }

    .icon-rank-1 {
        background-image: url('../../src/images/icons/icon-rank-1.png');
        width: 30 / @rem;
        height: 30 / @rem;
    }

    .icon-rank-2 {
        background-image: url('../../src/images/icons/icon-rank-2.png');
        width: 30 / @rem;
        height: 30 / @rem;
    }

    .icon-rank-3 {
        background-image: url('../../src/images/icons/icon-rank-3.png');
        width: 30 / @rem;
        height: 30 / @rem;
    }

    .icon-slider-thumb {
        background-image: url('../../src/images/icons/icon-slider-thumb.png');
        width: 22 / @rem;
        height: 22 / @rem;
    }

    .icon-tostore-tag-black {
        background-image: url('../../src/images/icons/icon-tostore-black.png');
        width: 35 / @rem;
        height: 15 / @rem;
    }

    .icon-tel {
        background-image: url('../../src/images/icons/icon-tel.png');
        width: 40 / @rem;
        height: 40 / @rem;
    }

    .icon-tel-sm {
        background-image: url('../../src/images/icons/icon-tel-sm.png');
        width: 24 / @rem;
        height: 24 / @rem;
    }

    .icon-have-price {
        background-image: url('../../src/images/icons/icon-have-price.png');
        width: 37 / @rem;
        height: 16 / @rem;
    }

    .icon-arrow-next {
        .arr-next(8 / @rem, 1px, @color-gray-b);
    }
}
