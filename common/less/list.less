@import './validation.less';
@import './mixins.less';

:global {
    .Saitama-list-item-content,
    .Saitama-list-item-extra {
        font-size: (14 / @rem);
    }

    .Saitama-list-item-arrow-right {
        border-bottom: 1px solid @color-gray-9;
        border-right: 1px solid @color-gray-9;
    }

    .Saitama-list-item-extra {
        font-weight: bold;
        word-wrap: break-word;
        word-break: normal;
    }

    .Saitama-list-item--arrow {
        .Saitama-list-item-extra {
            padding-right: 0;
        }
    }

    .Saitama-list-footer {
        position: relative;
        padding: (10 / @rem) (0 / @rem);
        min-height: 40 / @rem;
        background-color: @color-white;

        &:after {
            .setTopLine(@color-border);
        }
    }
}
