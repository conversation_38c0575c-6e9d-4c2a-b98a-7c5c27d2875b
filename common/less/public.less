@import 'validation.less';
@import 'mixins.less';
@import 'setrem.less';

:global {
    // 全局重写
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        outline: none;

        /* 占位符 */
        &::-webkit-input-placeholder {
            /* WebKit browsers */
            color: #999;
        }

        &:-moz-placeholder {
            /* Mozilla Firefox 4 to 18 */
            color: #999;
        }

        &::-moz-placeholder {
            /* Mozilla Firefox 19+ */
            color: #999;
        }

        &:-ms-input-placeholder {
            /* Internet Explorer 10+ */
            color: #999;
        }
    }

    article,
    aside,
    details,
    figcaption,
    figure,
    footer,
    header,
    hgroup,
    menu,
    nav,
    section {
        display: block;
    }

    body {
        background-color: @color-white;
        background-repeat: no-repeat;
        background-attachment: scroll;
        background-position: 0 0;
        max-width: @max-width;
        margin: 0 auto;
        position: relative;

        &::-webkit-scrollbar {
            display: none;
        }
    }

    a {
        cursor: pointer;
        color: @color-gray-3;

        &:link {
            text-decoration: none;
        }

        &:visited {
            text-decoration: none;
        }

        &:hover {
            text-decoration: none;
        }

        &:active {
            text-decoration: none;
        }
    }

    th,
    td {
        font-size: @def-font-size;
        line-height: @def-line-height;
        color: @color-gray-3;
    }

    form,
    ul,
    li,
    ol {
        margin: 0;
        padding: 0;
    }

    ul,
    li,
    ol {
        list-style-type: none;
        display: block;
    }

    img {
        border: 0;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        font-size: @def-font-size;
        font-weight: 400;
    }

    input,
    textarea,
    a,
    button,
    img,
    div {
        -webkit-appearance: none;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    }
    input,
    textarea {
        font-size: @def-font-size;
        font-weight: 400;
    }

    // 按钮
    [class^='btn-'],
    [class*='btn-'] {
        cursor: pointer;
        text-align: center;
        vertical-align: middle;
        border: 0;
        .inline-block();
        .border-radius(2px);

        &.disabled,
        &:disabled {
            &,
            &:active {
                cursor: no-drop !important;
                color: @color-white !important;
                background-color: @color-disabled !important;
                background-image: none !important;
            }
        }
    }

    // 清除浮动
    .f-clearfix {
        .clearfix();
    }

    // 边距
    // margin-top
    .f-mt5 {
        margin-top: (5 / @rem);
    }

    .f-mt10 {
        margin-top: (10 / @rem);
    }

    .f-mt15 {
        margin-top: (15 / @rem);
    }

    .f-mt20 {
        margin-top: (20 / @rem);
    }

    // margin-bottom
    .f-mb5 {
        margin-bottom: (5 / @rem);
    }

    .f-mb10 {
        margin-bottom: (10 / @rem);
    }

    .f-mb15 {
        margin-bottom: (15 / @rem);
    }

    .f-mb20 {
        margin-bottom: (20 / @rem);
    }

    // margin-right
    .f-mr5 {
        margin-right: (5 / @rem);
    }

    .f-mr10 {
        margin-right: (10 / @rem);
    }

    .f-mr15 {
        margin-bottom: (10 / @rem);
    }

    .f-mr20 {
        margin-right: (20 / @rem);
    }

    // margin-left
    .f-ml5 {
        margin-left: (5 / @rem);
    }

    .f-ml10 {
        margin-left: (10 / @rem);
    }

    .f-ml15 {
        margin-left: (15 / @rem);
    }

    .f-ml20 {
        margin-left: (20 / @rem);
    }

    // 内边距
    // padding-top
    .f-pt5 {
        padding-top: (5 / @rem);
    }

    .f-pt10 {
        padding-top: (10 / @rem);
    }

    .f-pt15 {
        padding-top: (15 / @rem);
    }

    .f-pt20 {
        padding-top: (20 / @rem);
    }

    // padding-bottom
    .f-pb5 {
        padding-bottom: (5 / @rem);
    }

    .f-pb10 {
        padding-bottom: (10 / @rem);
    }

    .f-pb15 {
        padding-bottom: (15 / @rem);
    }

    .f-pb20 {
        padding-bottom: (20 / @rem);
    }

    // padding-right
    .f-pr5 {
        padding-right: (5 / @rem);
    }

    .f-pr10 {
        padding-right: (10 / @rem);
    }

    .f-pr15 {
        padding-right: (15 / @rem);
    }

    .f-pr20 {
        padding-right: (20 / @rem);
    }

    // padding-left
    .f-pl5 {
        padding-left: (5 / @rem);
    }

    .f-pl10 {
        padding-left: (10 / @rem);
    }

    .f-pl15 {
        padding-left: (15 / @rem);
    }

    .f-pl20 {
        padding-left: (20 / @rem);
    }

    // 文字对齐
    .f-text-center {
        text-align: center;
    }

    .f-text-left {
        text-align: left;
    }

    .f-text-right {
        text-align: right;
    }

    .hide {
        display: none !important;
        visibility: hidden !important;
    }

    .show {
        display: block !important;
        visibility: visible !important;
    }

    .m-gray-line {
        background: @colro-gray-f7;
        height: (10 / @rem);
    }

    //1px线

    .top-line,
    .botom-line,
    .left-line,
    .right-line,
    .all-line {
        position: relative;
    }

    .top-line:after {
        .setTopLine(@color-border);
    }

    .botom-line:after {
        .setBottomLine(@color-border);
    }

    .left-line:after {
        .setLeftLine(@color-border);
    }

    .right-line:after {
        .setRightLine(@color-border);
    }

    .all-line:after {
        .setAllBorderLine(@color-border);
    }

    .fixed-body {
        overflow: hidden;
    }
}

/* Retina 适配 */
@media only screen and (-webkit-min-device-pixel-ratio: 2),
    only screen and (min--moz-device-pixel-ratio: 2),
    only screen and (-o-min-device-pixel-ratio: 200/100),
    only screen and (min-device-pixel-ratio: 2) {
    :global {
        .top-line:after,
        .botom-line:after {
            transform: scaleY(0.5);
        }

        .left-line:after,
        .right-line:after {
            transform: scaleX(0.5);
        }
    }
}

/* 三倍屏 适配 */
@media only screen and (-webkit-min-device-pixel-ratio: 2.5),
    only screen and (min--moz-device-pixel-ratio: 2.5),
    only screen and (-o-min-device-pixel-ratio: 250/100),
    only screen and (min-device-pixel-ratio: 2.5) {
    :global {
        .top-line:after,
        .botom-line:after {
            transform: scaleY(0.33333334);
        }

        .left-line:after,
        .right-line:after {
            transform: scaleX(0.33333334);
        }
    }
}
