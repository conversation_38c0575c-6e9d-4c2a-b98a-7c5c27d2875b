@charset 'utf-8';
// 颜色
@color-white: #fff;
@color-black: #000;
@color-blue: #00a2e8;
@color-orange: #fb6345;
@color-red: #ff5b5b;
@color-gray-3: #333;
@color-gray-6: #666;
@color-gray-9: #999;
@color-gray-b: #bbbbbb;
@colro-gray-f7: #f7f7f7;
@colro-gray-ea: #eaeaea;
@colro-gray-ce: #cecece;
@color-gray-drop: #f6f6f6;
@color-border: #ececec;
// 背景色
@color-page-bg: #f6f6f6;
@color-header-bg: #1e2b39;
@color-footer-bg: #ebebeb;
@color-input-bg: #f6f6f6;
@color-up-bg: #e7e7e7;
@color-user-menu: #282828;
//:active
@color-orange-active: #f55334;
@color-white-active: #c8c8c8;
//:disabled
@color-disabled: #c8c8c8;
// placeholder
@def-placeholder-color: #999;
@rem: 20rem;
@def-placeholder-font-size: (15 / @rem);
// 字体大小
@def-font-size: (14 / @rem);
@def-line-height: (17 / @rem);
@font-24: (12 / @rem);
@line-height-24: (17 / @rem);
@font-28: (14 / @rem);
@line-height-28: (21 / @rem);
@font-30: (15 / @rem);
@line-height-30: (19 / @rem);
@font-36: (18 / @rem);
@line-height-36: (20 / @rem);
@font-42: (21 / @rem);
@line-height-42: 1.5;
// 字体
@body-font: Helvetica, Verdana, Arial, Droid Sans, Droid Sans Fallback, PingFang SC, sans-serif;
//微软雅黑
@font-yahei: 'Microsoft YaHei', '\5fae\8f6f\96c5\9ed1';
//华文系黑
@font-STXihei: STXihei, '\534e\6587\7ec6\9ed1';
//宋体
@font-simsun: SimSun, '\5b8b\4f53';
// 布局
@max-width: 750px;

@theme-color: #00a2e8;
@header-bg: #fff;
@header-color-arrow: #222;
@header-color-backTitle: #222;
