// JS
import React, { memo, useMemo } from 'react';
import { executeCB } from 'ttp-utils';
// 组件
import { Drawer, Icon, Button, Empty, Loader } from 'ttp-library';

// LESS
import styles from './index.less';

/**
 * 录音播放底部弹窗
 * @param   {object}                     props             props
 * @param   {boolean}                    props.loading     是否加载中,默认：false
 * @param   {boolean}                    props.open        是否打开，默认：false
 * @param   {string}                     props.title       标题，默认：'跟进资料（录音）'
 * @param   {number}                     props.currentIndex 当前播放的录音索引，默认：0
 * @param   {Array}                      props.list        录音列表,默认：[]
 * @param   {Function}                   props.onClose     关闭事件
 * @returns {React.ReactElement}                           jsx
 */
const AudioDrawer = memo((props) => {
    // props
    const {
        loading = false,
        open = false,
        title = '跟进资料（录音）',
        currentIndex = 0,
        list = [],
        onClose
    } = props;
    // state
    // 计算属性
    // 音频游览
    const audioList = useMemo(() => {
        if (Array.isArray(list)) {
            return list.map((url) => ({ title: '音频', type: 'audio', src: url }));
        }
        return [];
    }, [list]);

    /**
     * 关闭音频游览窗口
     */
    const handleDrawerClose = () => {
        audioList.forEach((_, index) => {
            document.getElementById(`audio-${index}`).pause();
        });
        executeCB(onClose, -1);
    };

    // 常量
    const leftNode = (
        <div className={styles['drawer-down-close']} onClick={handleDrawerClose}>
            <Icon className={styles['drawer-down-close-icon']} type='iconfont-close' />
        </div>
    );

    return (
        <Drawer
            className={styles['drawer-down']}
            hasHeader={true}
            left={leftNode}
            maskOpacity={0.2}
            mode='bottom'
            onClose={handleDrawerClose}
            open={open}
            title={title}>
            <div className={styles['drawer-down-body']}>
                <Loader maskOpacity={0.6} open={loading} type='page' />
                {audioList.length == 0 && !loading ? (
                    <Empty description='数据暂未生成，请过5分钟再尝试访问。' />
                ) : (
                    audioList.map((item, index) => {
                        return (
                            <div className={styles['audio-box']} key={item.src}>
                                <audio
                                    autoPlay={index == currentIndex}
                                    className={styles['audio-box-item']}
                                    controls
                                    id={`audio-${index}`}
                                    name='media'
                                    preload='metadata'>
                                    <source src={item.src} type='audio/mpeg' />
                                    <source src={item.src} type='audio/ogg' />
                                    <source src={item.src} type='audio/wav' />
                                    <source src={item.src} type='audio/mp3' />
                                    <source src={item.src} type='audio/x-m4a' />
                                    <source src={item.src} type='audio/aac' />
                                    您的浏览器不支持audio元素。
                                </audio>
                            </div>
                        );
                    })
                )}
            </div>
            <div className={styles['drawer-down-footer']}>
                <Button block className={styles['footer-button']} onClick={handleDrawerClose} skin='white'>
                    关闭
                </Button>
            </div>
        </Drawer>
    );
});

export default AudioDrawer;
