@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';

.drawer-down {
    :global {
        .Saitama-drawer-box {
            border-top-left-radius: 8 / @rem;
            border-top-right-radius: 8 / @rem;
        }

        .Saitama-header {
            position: relative;

            &:after {
                .setBottomLine(@color-border);
            }
        }
    }

    &-close {
        width: 20 / @rem;
        height: 20 / @rem;
        text-align: center;
        line-height: 20 / @rem;

        &-icon {
            font-size: 14 / @rem !important;
        }
    }

    &-body {
        padding: (15 / @rem) (10 / @rem) (0 / @rem);
        max-height: 70vh;
        min-height: 190 / @rem;
        overflow-y: auto;

        .audio-box {
            margin-bottom: 15 / @rem;
            width: 100%;

            &-item {
                width: 100%;
            }
        }
    }

    &-footer {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: (10 / @rem) (20 / @rem);
        position: relative;

        &:after {
            .setTopLine(@color-border);
        }

        .footer-button {
            height: 44 / @rem;
        }
    }
}
