/* JS */
import React, { memo } from 'react';
/* LESS */
import styles from './index.less';

/**
 * 我是底线
 * <AUTHOR>
 * @param   {object}                    props props
 * @param   {string}                    props.text  文本
 * @returns {React.ReactElement}              jsx
 */
const tag = memo((props) => {
    const { text = '我是有底线的' } = props;
    return <div className={styles['bottom-line']}>{text}</div>;
});

export default tag;
