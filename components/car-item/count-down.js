// JS
import React, { memo, useEffect, useState } from 'react';
import classnames from 'classnames';
import { PAI_TYPE_COUNT_DOWN_CN, PAI_TYPE } from './constant.js';
// LESS
import styles from './index.less';

/**
 * 格式化时间
 *
 * @param  {number} timeNum  时间
 * @returns {string|number} 格式化后的时间
 */
const formatTime = (timeNum) => {
    return timeNum > 9 ? timeNum : `0${timeNum}`;
};

/**
 * 倒计时
 *
 * @param  {number} timeStamp 时间戳
 * @param  {number} type      类型
 * @returns {object} 时间对象
 */
const onTimeChange = (timeStamp, type) => {
    let hours = formatTime(Math.floor(timeStamp / 60 / 60)),
        minutes = formatTime(Math.floor(timeStamp / 60 - 60 * hours)),
        seconds = formatTime(timeStamp - 60 * minutes - 60 * 60 * hours);
    if (timeStamp == 0) {
        return type == PAI_TYPE.TIMELAPSE ? '预拍结束待加价' : '已结束';
    }
    return `${PAI_TYPE_COUNT_DOWN_CN[type] || '竞拍中'} ${hours}:${minutes}:${seconds}`;
};

/**
 * 初始化倒计时
 * @param  {number} timeStamp 时间戳
 * @param  {number} type      类型
 * @returns {string} 时间
 */
const initTimeState = (timeStamp, type) => {
    let time;
    if (timeStamp) {
        time = '距结束 00:00:00';
    } else if (type == PAI_TYPE.TIMELAPSE) {
        time = '预拍结束待加价';
    } else {
        time = '已结束';
    }
    return time;
};

/**
 * 倒计时组件
 *
 * @param    {object}                 props           上层传过来的数据
 * @param    {number}                 props.timeStamp 时间戳
 * @param    {number}                 props.type      排场类型
 */
const CountDown = memo((props) => {
    // props
    const { timeStamp, type, timer } = props;
    // state
    const [timeStr, setTimeStr] = useState(initTimeState(timeStamp, type));
    // 常量
    const countDownClass = classnames(styles['count-down'], {
        [styles['count-down-notice-color']]: timeStamp - timer > 0 && timeStamp - timer < 600
    });

    // useEffect
    useEffect(() => {
        if (timeStamp && timeStamp > timer) {
            setTimeStr(onTimeChange(timeStamp - timer, type));
        }
    }, [timeStamp, timer]);

    return (
        <div className={countDownClass}>
            <span className={styles['count-down-time']}>{timeStr}</span>
        </div>
    );
});

export default CountDown;
