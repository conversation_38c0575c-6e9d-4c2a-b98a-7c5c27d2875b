// JS
import React, { memo, useState, useEffect } from 'react';
import cls from 'classnames';
import { executeCB } from 'ttp-utils';
import { replaceUrl } from 'common/js/utils';
// 组件
import { LazyLoad } from 'ttp-library';
import CountDown from './count-down.js';
// LESS
import styles from './index.less';

/**
 * 标签
 */
const ItemTag = memo(({ text }) => {
    const tagClass = cls(styles['car-item-photo-tag'], 'icon-tostore-tag-black');
    return <span className={tagClass}>{text}</span>;
});

/**
 * 车源列表数据展示
 * @param   {object}                     props props
 * @returns {React.ReactElement}               jsx
 */
const CarItem = memo((props) => {
    // props
    const { classname, itemData, onClick } = props;
    // state
    const [baseCountTime, setBaseCountTime] = useState(0);
    // 计算属性
    const carItemClass = cls(styles['car-item'], classname);

    /**
     * 选择车源
     */
    const handleClick = () => {
        executeCB(onClick, itemData);
    };

    // 初始化
    useEffect(() => {
        const baseInterval = setInterval(() => {
            setBaseCountTime((pre) => pre + 1);
        }, 1000);
        return () => clearInterval(baseInterval);
    }, []);

    return (
        <div className={carItemClass} onClick={handleClick}>
            <div className={styles['car-item-left']}>
                <LazyLoad.LazyImg
                    className={styles['car-item-photo']}
                    src={replaceUrl(itemData.leftFrontImageUrl, 320, 240)}
                />
                {itemData.onStoreFlag == 1 && <ItemTag text='到店' />}

                <CountDown
                    key={itemData.auctionId}
                    timeStamp={itemData.awayFromEnd}
                    timer={baseCountTime}
                    type={itemData.paiMode}
                />
            </div>
            <div className={styles['car-item-info']}>
                <p className={styles['title']}>
                    [{itemData.cityName} {itemData.licensePrefix}] {itemData.brandName}
                    {itemData.familyName} {itemData.modelName}
                </p>
                <p>
                    结构件{itemData.skeletonStar}星 | {itemData.registerDateStr} | {itemData.driveDistance}
                    万公里
                </p>
                {!!itemData.auctionPrice && (
                    <p className={styles['price']}>秒杀价：¥{itemData.auctionPrice}万</p>
                )}
            </div>
        </div>
    );
});

export default CarItem;
