@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';

.car-item {
    max-width: 100%;
    display: flex;
    align-items: flex-start;
    box-sizing: border-box;

    &-left {
        width: 120 / @rem;
        position: relative;
        margin-right: 10 / @rem;
    }

    &-photo {
        width: 120 / @rem;
        height: 90 / @rem;
        border-radius: 6 / @rem;

        img {
            width: 100%;
            height: 100%;
            border-radius: 4 / @rem;
        }

        &-tag {
            position: absolute;
            left: 0;
            top: 0;
            color: @color-white;
            font-size: 11 / @rem;
            z-index: 1;
            text-align: center;
        }
    }

    &-info {
        color: #808a8f;
        font-weight: 600;
        font-size: 11 / @rem;
        margin-bottom: 5 / @rem;
        word-wrap: break-word;
        word-break: normal;
        display: flex;
        flex-direction: column;

        .title {
            font-size: 14 / @rem;
            color: @color-gray-3;
            line-height: 20 / @rem;
            font-weight: 600;
            margin-bottom: 5 / @rem;
            white-space: pre-wrap;
        }

        .price {
            font-weight: 600;
            font-size: 14 / @rem;
            color: @color-orange;
            line-height: 1.5;
        }
    }
}

.count-down {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 17 / @rem;
    line-height: 13 / @rem;
    background: #222;
    opacity: 0.69;
    border-bottom-right-radius: 4 / @rem;
    border-bottom-left-radius: 4 / @rem;
    text-align: center;

    &-time {
        height: 17 / @rem;
        line-height: 17 / @rem;
        color: @color-white;
        font-size: 12 / @rem;
    }
}
