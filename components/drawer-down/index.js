// JS
import React, { memo, useEffect, useState } from 'react';
import { executeCB } from 'ttp-utils';
import cls from 'classnames';
// 组件
import { Button, Drawer, Radio, Icon, Empty, Loader } from 'ttp-library';
// LESS
import styles from './index.less';

const Group = Radio.Group;

/**
 * 从底部弹出的半屏TAG选择弹窗
 * <AUTHOR>
 * @param   {object}                     props             props
 * @param   {boolean}                    props.loading     是否加载中,默认：false
 * @param   {boolean}                    props.open        是否打开，默认：false
 * @param   {string}                     props.title       标题
 * @param   {number}                     props.maskOpacity 遮罩层透明度，默认：0.3
 * @param   {Array}                      props.dataList    数据列表
 * @param   {Array}                      props.dataList.value  值
 * @param   {Array}                      props.dataList.label  文本
 * @param   {string}                     props.value       当前选中值
 * @param   {Function}                   props.onSelect    选中事件
 * @param   {Function}                   props.onChange    确定事件
 * @param   {Function}                   props.onClose     关闭事件
 * @param   {object}                     props.children    子元素
 * @returns {React.ReactElement}                           jsx
 */
const DrawerDown = memo((props) => {
    // props
    const {
        loading = false,
        classname,
        open = false,
        title,
        maskOpacity = 0.3,
        dataList = [],
        value,
        onSelect,
        onChange,
        onClose,
        children
    } = props;
    // state
    const [selectValue, setSelectValue] = useState(value);
    /**
     * radio change
     * @param   {string}                   value value
     */
    const handelRadioChange = (value) => {
        setSelectValue(value);
        executeCB(onSelect, value);
    };

    /**
     * 关闭弹窗/取消点击事件
     */
    const handelClose = () => {
        executeCB(onClose);
        setSelectValue('');
    };

    /**
     * 跟进目标-确定点击事件
     */
    const handelSubmit = () => {
        const item = dataList.find((item) => item.value == selectValue);
        executeCB(onChange, selectValue, item);
    };

    // 常量
    const leftNode = (
        <div className={styles['drawer-down-close']} onClick={handelClose}>
            <Icon className={styles['drawer-down-close-icon']} type='iconfont-close' />
        </div>
    );

    // useEffect
    // 监听value
    useEffect(() => {
        if (open) {
            setSelectValue(value);
        }
    }, [value, open]);

    return (
        <Drawer
            className={cls(styles['drawer-down'], classname)}
            hasHeader={true}
            left={leftNode}
            maskOpacity={maskOpacity}
            mode='bottom'
            onClose={handelClose}
            open={open}
            title={title}>
            <div className={styles['drawer-down-body']}>
                <Loader maskOpacity={0.6} open={loading} type='page' />
                {(!Array.isArray(dataList) || dataList.length == 0) && !loading ? (
                    <Empty />
                ) : (
                    <Group
                        className={styles['drawer-down-group']}
                        onChange={handelRadioChange}
                        value={selectValue}>
                        {Array.isArray(dataList) &&
                            dataList.map((item) => (
                                <Radio
                                    checked={item.value == selectValue}
                                    className={styles['drawer-down-group-tag']}
                                    key={item.value}
                                    tagType='circle'
                                    type='tag'
                                    value={item.value}>
                                    {item.label}
                                </Radio>
                            ))}
                    </Group>
                )}
                {children}
            </div>
            <div className={styles['drawer-down-footer']}>
                <Button className={styles['footer-button']} onClick={handelClose} skin='white'>
                    取消
                </Button>
                <Button className={styles['footer-button']} onClick={handelSubmit}>
                    确认
                </Button>
            </div>
        </Drawer>
    );
});

export default DrawerDown;
