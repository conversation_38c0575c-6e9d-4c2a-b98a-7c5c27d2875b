@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';

.drawer-down {
    :global {
        .Saitama-drawer-box {
            border-top-left-radius: 8 / @rem;
            border-top-right-radius: 8 / @rem;
        }

        .Saitama-header {
            position: relative;

            &:after {
                .setBottomLine(@color-border);
            }
        }
    }

    &-close {
        width: 20 / @rem;
        height: 20 / @rem;
        text-align: center;
        line-height: 20 / @rem;

        &-icon {
            font-size: 14 / @rem !important;
        }
    }

    &-body {
        padding: (5 / @rem) (10 / @rem) (15 / @rem);
        min-height: 190 / @rem;
    }

    &-group {
        display: flex;
        align-items: center;
        // padding: 0 (12 / @rem);

        &-tag {
            display: inline-block;
            max-width: 100%;
            min-width: 80 / @rem;
            height: 30 / @rem;
            line-height: 30 / @rem;
            font-size: 14 / @rem;
            text-align: center;
            vertical-align: middle;
            margin-top: 10 / @rem;
            margin-right: 8 / @rem;
            padding: 0;
            box-sizing: border-box;
            padding: 0 (10 / @rem);

            &:last-child {
                margin-right: 0;
            }
        }

        :global {
            .Saitama-tag-box {
                display: block;
                max-width: 100%;
                height: 30 / @rem;
                line-height: 30 / @rem;
                text-align: center;
            }
        }
    }

    &-footer {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: (10 / @rem) (20 / @rem);
        position: relative;

        &:after {
            .setTopLine(@color-border);
        }

        .footer-button {
            width: 156 / @rem;
            height: 44 / @rem;
        }
    }
}
