/**
 * @file  全局的错误处理组件
 */

import React, { Component } from 'react';
import { executeCB } from 'ttp-utils';
// 组件
import { Icon } from 'ttp-library';
import styles from './index.less';

/**
 * 错误 页面
 * @class
 */
class ErrorBoundary extends Component {
    /**
     * getDerivedStateFromError
     * @returns {object}            object
     */
    static getDerivedStateFromError() {
        // 更新 state 使下一次渲染能够显示降级后的 UI
        return { hasError: true };
    }

    /**
     * constructor
     * @param {object} props  props
     */
    constructor(props) {
        super(props);
        this.state = { hasError: false };
    }

    /**
     * componentDidCatch
     * @param {object}  e event
     */
    componentDidCatch(e) {
        // 你同样可以将错误日志上报给服务器
        if (process.env.NODE_ENV === 'development') {
            console.error(e);
        }
        // 统一上报
        executeCB(window.reportError, e, 'ErrorBoundary', 'commonTryCatch');
    }

    /**
     * handleRefresh
     */
    handleRefresh = () => {
        window.location.reload();
    };

    /**
     * render
     * @returns {object}    JSX
     */
    render() {
        if (this.state.hasError) {
            // 你可以自定义降级后的 UI 并渲染      return <h1>Something went wrong.</h1>;    }
            return (
                <div className={styles['error-box']}>
                    <div className={styles.blockContainer}>
                        <Icon type='icon-404' />
                    </div>
                    <p>
                        出错了，请
                        <span className={styles['refresh-btn']} onClick={this.handleRefresh}>
                            刷新
                        </span>
                        或联系我们！
                    </p>
                </div>
            );
        }
        return this.props.children;
    }
}

export default ErrorBoundary;
