// JS
import React, { memo, useMemo, useState } from 'react';
import { useToggle } from 'ahooks';
// 组件
import { LazyLoad, ViewerPicture } from 'ttp-library';
import AudioDrawer from '../audio-drawer/index.js';
// LESS
import styles from './index.less';

/**
 * 跟进资料列表
 * @param   {object}                     props props
 * @returns {React.ReactElement}               jsx
 */
const FileList = memo((props) => {
    // props
    const { image, video, audio } = props;
    // state
    // 图片游览
    const [currentViewIndex, setCurrentViewIndex] = useState(0);
    const [openView, { setLeft: setCloseView, setRight: setOpenView }] = useToggle(false);
    // 音频游览
    const [currentAudioIndex, setCurrentAudioIndex] = useState(0);
    const [openDrawer, { setLeft: setCloseDrawer, setRight: setOpenDrawer }] = useToggle(false);
    // 计算属性
    // 小图标展示
    const pictureList = useMemo(() => {
        const imgList = image.map((url) => ({ title: '图片', type: 'image', src: url }));
        const videoList = video.map((item) => ({
            title: '视频',
            type: 'video',
            src: item.fileUrl,
            thumbnail: item.thumbnailFileUrl
        }));
        const audioList = audio.map((url, index) => ({
            title: '音频',
            type: 'audio',
            src: url,
            audioIndex: index
        }));
        return [...imgList, ...videoList, ...audioList];
    }, [image, video, audio]);

    // 图片视频游览
    const viewerPictureList = useMemo(() => {
        return pictureList.filter((item) => item.type !== 'audio');
    }, [pictureList]);

    /**
     * 点击跟进资料(图片/视频)
     * @param   {number}                     index 数据下标
     */
    const handleViewPicture = (index) => {
        setCurrentViewIndex(index);
        setOpenView();
    };

    /**
     * 关闭视屏(图片/视频)
     */
    const handleViewPictureClose = () => {
        setCurrentViewIndex(-1);
        setCloseView();
    };

    /**
     * 点击跟进资料(音频)
     * @param   {number}                     index 数据下标
     */
    const handleViewAudio = (index) => {
        setCurrentAudioIndex(index);
        setOpenDrawer();
    };

    /**
     * 关闭音频游览窗口
     */
    const handleDrawerClose = () => {
        setCurrentAudioIndex(-1);
        setCloseDrawer();
    };

    if (!Array.isArray(pictureList) || !pictureList.length) {
        return null;
    }

    return (
        <>
            <div className={styles['list-box-image-list']}>
                {pictureList.map((item, index) => {
                    if (item.type == 'image') {
                        return (
                            <div
                                className={styles['img-item']}
                                key={item.src}
                                onClick={() => handleViewPicture(index)}>
                                <LazyLoad.LazyImg height={54} src={item.src} width={72} />
                            </div>
                        );
                    }
                    if (item.type == 'video') {
                        return (
                            <div
                                className={styles['video-item']}
                                key={item.src}
                                onClick={() => handleViewPicture(index)}>
                                {item.thumbnail && (
                                    <LazyLoad.LazyImg height={54} src={item.thumbnail} width={72} />
                                )}
                                <i className={'icon-play-video'} />
                            </div>
                        );
                    }

                    if (item.type == 'audio') {
                        return (
                            <div
                                className={styles['audio-item']}
                                key={item.src}
                                onClick={() => handleViewAudio(item.audioIndex)}>
                                <i className={'icon-play-audio'} />
                            </div>
                        );
                    }
                })}
            </div>
            {/* 图片游览窗口 */}
            <ViewerPicture
                current={currentViewIndex}
                imgList={viewerPictureList}
                onClose={handleViewPictureClose}
                open={openView}
                showTab={false}
                title='跟进资料（图片/视频）'
            />
            {/* 音频游览 */}
            <AudioDrawer
                currentIndex={currentAudioIndex}
                list={audio}
                onClose={handleDrawerClose}
                open={openDrawer}
                title='跟进资料（音频）'
            />
        </>
    );
});

export default FileList;
