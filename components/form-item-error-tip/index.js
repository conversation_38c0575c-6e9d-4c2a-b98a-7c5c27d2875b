/**
 * FormItemErrorTip 表单错误提示
 *
 * @requires module:react
 * @requires module:dva
 */

import React, { memo } from 'react';
import cls from 'classnames';

/* LESS */
import style from './index.less';

const FormItemErrorTip = memo((props) => {
    const { classname, border = false, message } = props;
    const tipsClass = cls(
        style['error-message'],
        { [style['no-message']]: !message && border },
        { [style['border']]: message && border },
        classname
    );
    if (message) {
        return <p className={tipsClass}>{message}</p>;
    }
    if (border) return <p className={tipsClass} />;
});

export default FormItemErrorTip;
