/**
 * @file  全局的404页面
 */
// JS
import React, { memo } from 'react';
import { Link } from 'dva/router';
import { PAGE_URL } from 'routes/router-config.js';
// 组件
import { Button } from 'ttp-library';
import { Picture403, Picture404 } from './picture.js';
// LESS
import styles from './index.less';

/**
 * NoMatch默认页
 * @returns {object} jsx
 */
const NoMatch = memo((props) => {
    const { type = 404 } = props;
    return (
        <div className={styles['error-box']}>
            <div className={styles['error-box-img']}>{type == 403 ? <Picture403 /> : <Picture404 />}</div>
            <h2 className={styles['error-box-title']}>{type}</h2>
            <div className={styles['error-box-subtitle']}>
                {type == 403 ? '对不起，您没有权限访问此页面。' : '对不起，您访问的页面不存在。'}
            </div>
            <Link to={PAGE_URL.home}>
                <Button className='f-mt10' size='sm'>
                    回到首页
                </Button>
            </Link>
        </div>
    );
});

export default NoMatch;
