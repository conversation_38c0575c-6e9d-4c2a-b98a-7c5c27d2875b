@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';

.error-box {
    position: fixed;
    text-align: center;
    top: 50%;
    left: 50%;
    transform: translate3d(-50%, -50%, 0);
    font-size: (14 / @rem);

    &-img {
        text-align: center;
        margin-bottom: 20 / @rem;
    }

    &-title {
        text-align: center;
        font-size: (24 / @rem);
        font-weight: bold;
        line-height: 1.5;
        margin-bottom: 10 / @rem;
        color: @color-gray-3;
    }

    &-subtitle {
        color: @color-gray-9;
        font-size: (14 / @rem);
        line-height: 1.5;
        text-align: center;
        margin-bottom: 10 / @rem;
    }

    .refresh-btn {
        line-height: 2;
        color: @theme-color;
    }
}
