// JS
import React, { memo } from 'react';
import { executeCB, isEmpty } from 'ttp-utils';
// 组件
import { List, Empty, Button, Icon } from 'ttp-library';
import ProgressBar from 'components/progress-bar';
// LESS
import styles from './index.less';
// img
import NoLog from 'src/images/no-log.png';

const PannelItem = memo((props) => {
    // props
    const { itemData, onViewDetails, showDetailButton = true } = props;
    /**
     * 编辑
     * @param   {object}                     item  item
     */
    const handelViewDetails = (item) => {
        executeCB(onViewDetails, item);
    };

    return (
        <>
            {Array.isArray(itemData.kpiAchievementList) && itemData.kpiAchievementList.length > 0 ? (
                <List className={styles['panel-list-box']}>
                    {itemData.kpiAchievementList.map((item) => {
                        const achievementRate = item.achievementRate;
                        return (
                            <div className={styles['item']} key={item.indicatorCode + item.indicatorName}>
                                <div className={styles['item-extra']}>
                                    <span className={styles['item-title']}>{item.indicatorName}</span>
                                    <span className={styles['item-rate']}>
                                        {'完成率 '}
                                        <span className={styles['item-rate-bold']}>
                                            {isEmpty(achievementRate) ? '0%' : achievementRate}
                                        </span>
                                    </span>
                                    <span className={styles['item-detail']}>
                                        {item.achievementValue}/{item.targetValue}
                                    </span>
                                </div>
                                <ProgressBar
                                    className={styles['item-rate-progress']}
                                    progress={achievementRate}
                                />
                            </div>
                        );
                    })}
                </List>
            ) : (
                <Empty className={styles['no-item-data']} description='尚未添加绩效指标。' image={NoLog} />
            )}
            {showDetailButton && (
                <Button
                    block
                    className={styles['panel-data-more']}
                    onClick={() => handelViewDetails(itemData)}
                    skin='white'>
                    <span>查看详情</span>
                    <Icon className={styles['panel-data-more-icon']} type='iconfont-forward' />
                </Button>
            )}
        </>
    );
});

export default PannelItem;
