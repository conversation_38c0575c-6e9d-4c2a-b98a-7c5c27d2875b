@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';
@import '../../common/less/list.less';

.no-item-data {
    padding: 0 0 (20 / @rem);

    :global {
        .Saitama-empty-image {
            max-width: 90 / @rem;
            max-height: 95 / @rem;
        }
    }
}

.panel-list-box {
    .item {
        position: relative;
        overflow: inherit;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        background-color: white;

        &-extra {
            display: flex;
            align-items: center;
            justify-content: start;
            width: 100%;
            font-size: 12 / @rem;
            color: #222;
            margin-top: 10 / @rem;
            margin-left: 5 / @rem;
        }

        &-title {
            font-weight: 400;
            font-size: 14 / @rem;
            color: @color-black;
            flex: 1 0 30%;
            /* 等分一行，减去左右 margin 的宽度 */
        }

        &-detail {
            font-size: 14 / @rem;
            color: #666;
            text-align: right;
            flex: 1 0 35%;
            /* 等分一行，减去左右 margin 的宽度 */
        }

        &-rate-progress {
            margin: 0;
            width: 100%;
            display: flex;
            flex: 1 0 calc(100% - 10 / @rem);
            /* 等分一行，减去左右 margin 的宽度 */
            margin: (10 / @rem) (5 / @rem);
        }

        &-rate {
            font-size: 12 / @rem;
            color: #00a2e8;
            flex: 1 0 35%;
            /* 等分一行，减去左右 margin 的宽度 */

            &-bold {
                font-size: 14 / @rem;
                color: #00a2e8;
                font-weight: 600;
            }
        }
    }
}

.panel-data-more {
    appearance: none;
    -webkit-appearance: none;
    color: @color-black;
    font-size: 14 / @rem;
    border: none;
    padding: 0;
    height: 30 / @rem;

    &-icon {
        font-size: 12 / @rem;
        margin-left: 5 / @rem;
        color: @color-gray-9;
    }
}
