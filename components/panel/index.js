/* JS */
import React, { memo } from 'react';
import cls from 'classnames';
import { isEmpty, executeCB } from 'ttp-utils';
/* LESS */
import './index.less';

/**
 * Panel 面板（UI）
 * <AUTHOR>
 * @param   {object}         props               props参数
 * @param   {string}         props.classname     样式名称，默认：''
 * @param   {string}         props.title         标题，支持组件
 * @param   {string}         props.subtitle      副标题，支持组件
 * @param   {boolean}        props.open          是否展开，默认：false
 * @param   {boolean}        props.button        是否显示按钮，默认：true
 * @param   {number}         props.bodyMinHeight body最小高度，默认：0
 * @param   {Function}       props.onClick       点击事件
 * @param   {object}         props.children      子元素
 * @returns {object}                             jsx
 */
const Panel = memo((props) => {
    const {
        classname = '',
        children,
        title,
        subtitle,
        open = false,
        button = true,
        bodyMinHeight = 0,
        onClick
    } = props;
    const boxClass = cls('white-box', classname);
    const headerClass = cls('white-box-header');
    const arrowClass = cls(
        'white-box-arrow',
        { ['white-box-arrow-up']: open },
        { ['white-box-arrow-down']: !open }
    );
    const bodyClass = cls('white-box-body', { ['white-box-body-show']: open });
    const bodyStyle = {
        minHeight: open ? 'auto' : bodyMinHeight
    };

    /**
     * 点击事件，展开收起Panel
     */
    const handleClick = () => {
        executeCB(onClick, open);
    };

    return (
        <div className={boxClass}>
            {!isEmpty(title) && (
                <div className={headerClass} onClick={handleClick}>
                    <h2 className={'white-box-title'}>{title}</h2>
                    <div className={'white-box-header-right'}>
                        {!isEmpty(subtitle) && <span className={'white-box-subtitle'}>{subtitle}</span>}
                        {button && <i className={arrowClass} />}
                    </div>
                </div>
            )}
            <div className={bodyClass} style={bodyStyle}>
                {children}
            </div>
        </div>
    );
});

export default Panel;
