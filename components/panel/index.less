@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';

:global {
    .white-box {
        margin: 0 auto;
        border-radius: 8 / @rem;
        width: 350 / @rem;
        height: auto;
        background-color: @color-white;
        padding: 0 (10 / @rem);
        box-sizing: border-box;

        &-header {
            padding: (6 / @rem) (5 / @rem);
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;

            &-right {
                display: flex;
                align-items: center;
                justify-content: flex-end;
            }
        }

        &-title {
            font-size: 17 / @rem;
            line-height: 30 / @rem;
            font-weight: bold;
            vertical-align: middle;
        }

        &-subtitle {
            font-size: 14 / @rem;
            line-height: 30 / @rem;
            color: #222222;
            margin-right: 6 / @rem;
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        &-arrow {
            display: inline-block;
            line-height: 0;
            font-size: 0px;
        }

        &-arrow-down {
            .triangle-down(@color-black, 8 / @rem);
        }

        &-arrow-up {
            .triangle-up(@color-black, 8 / @rem);
        }

        &-body {
            display: block;
            overflow: hidden;
            height: 0;

            &-show {
                height: auto;
            }
        }
    }
}
