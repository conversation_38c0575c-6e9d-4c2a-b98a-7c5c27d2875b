// JS
import React, { memo } from 'react';
import { getRequest } from 'common/js/fetch.js';
import { PORT_URL } from 'common/js/constant.js';
import { useRequest } from 'ahooks';
// 组件
import { ProgressCircle } from './index.js';

/**
 * 请求时间进度数据
 * @param   {object}               data 请求参数
 * @returns {Array}                     团队数据
 */
const getProgress = (data) => {
    const opts = {
        url: PORT_URL.monthpercentage,
        data
    };
    return getRequest(opts).then((res) => res.result);
};

/**
 * 时间进度 BC组件
 * <AUTHOR>
 * @returns {React.ReactElement} jsx
 */
const Circle = memo(() => {
    const { data: progress } = useRequest(getProgress, {
        cacheKey: 'cachekey-progress-circle',
        staleTime: 30 * 60 * 1000
    });

    return <ProgressCircle progress={progress} />;
});

export default Circle;
