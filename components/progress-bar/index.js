// JS
import React, { memo, useMemo } from 'react';
import { useSize } from 'ahooks';
import cls from 'classnames';
import { isEmpty, isNumber } from 'ttp-utils';
// 自定义 Hook
import { useMouseMove } from 'common/js/hooks.js';
// LESS
import styles from './index.less';

/**
 * 格式化进度条
 * <AUTHOR>
 * @param   {number}                    progress 进度条
 * @returns {string}                            进度条
 */
const formatProgress = (progress) => {
    if (isEmpty(progress)) {
        return '0%';
    }
    if (isNumber(progress)) {
        return `${Math.min(progress, 100)}%`;
    }
    if (/^\d+(\.\d+)?%$/.test(progress)) {
        return progress;
    }
    return '0%';
};

/**
 * 进度条
 * <AUTHOR>
 * @param   {object}                    props           父组件传入的状态
 * @param   {number}                    props.progress  进度条，数字，默认：0
 * @param   {string}                    props.className 样式名称
 * @returns {React.ReactElement}                        jsx
 */
const ProgressBar = memo((props) => {
    const { progress = 0, className = '' } = props;
    const progressClass = cls(styles['progress-bar'], className);
    const progressWidth = formatProgress(progress);

    return (
        <div className={progressClass}>
            <div className={styles.progress} style={{ width: progressWidth }} />
        </div>
    );
});

/**
 * 进度条（圆形）
 * <AUTHOR>
 * @param   {object}                    props           父组件传入的状态
 * @param   {number}                    props.progress  进度条，数字，默认：0
 * @param   {boolean}                   props.isMove    是否可拖动，默认：true
 * @param   {string}                    props.className 样式名称
 * @param   {object}                    props.children  子元素
 * @returns {React.ReactElement}                        jsx
 */
export const ProgressCircle = memo((props) => {
    const { progress = 0, isMove = true, className = '', children } = props;
    // state
    const size = useSize(document.querySelector('body'));

    // 计算属性
    // 获取视口的宽度
    const viewportWidth = useMemo(() => Math.min(size.width, 750), [size]);
    const remRate = useMemo(() => viewportWidth / 375, [viewportWidth]);
    // 使用自定义 hook 处理拖动逻辑
    const { position, handleMouseDown } = useMouseMove({
        isMove,
        initialY: Math.floor(window.innerHeight / 2)
    });
    // 常量
    const progressClass = cls(styles['progress-circle'], className);

    // 常量 - 使用useMemo优化计算
    const radius = useMemo(() => Math.floor((54 / 2) * remRate), [remRate]);
    const radiusWidth = useMemo(() => Math.floor((58 / 2) * remRate), [remRate]);
    const circleWidth = useMemo(() => Math.floor(4 * remRate), [remRate]);

    // 完成进度条样式
    const circleStyle = useMemo(() => {
        const circumference = 2 * Math.PI * radius;
        const offset = circumference - (progress / 100) * circumference;
        return {
            strokeDasharray: `${circumference} ${circumference}`,
            strokeDashoffset: offset
        };
    }, [progress, radius]);

    // 背景进度条样式 - 添加依赖数组
    const circleBgStyle = useMemo(() => {
        const circumference = 2 * Math.PI * radius;
        const offset = circumference - 1 * circumference;
        return {
            strokeDasharray: `${circumference} ${circumference}`,
            strokeDashoffset: offset
        };
    }, [radius]);

    // 进度条容器样式
    const positionStyle = useMemo(() => {
        if (isMove) {
            const maxButtom = window.innerHeight - (58 + 20) * remRate;
            const maxtop = 20 * remRate;
            return {
                top:
                    position.y > maxButtom
                        ? `${maxButtom}px`
                        : position.y < maxtop
                        ? `${maxtop}px`
                        : `${position.y}px`
            };
        }
        return {};
    }, [position, remRate, isMove]);

    return (
        <div
            className={progressClass}
            onMouseDown={handleMouseDown}
            onTouchStart={handleMouseDown}
            style={positionStyle}>
            <svg className={styles['progress-ring']} height='100%' width='100%'>
                <defs>
                    <linearGradient id='gradient' x1='0%' x2='100%' y1='0%' y2='100%'>
                        <stop offset='0%' style={{ stopColor: '#00CFF5', stopOpacity: 1 }} />
                        <stop offset='100%' style={{ stopColor: '#00A2E8', stopOpacity: 1 }} />
                    </linearGradient>
                </defs>
                <circle
                    className={styles['progress-ring__circle']}
                    cx={radiusWidth}
                    cy={radiusWidth}
                    fill='transparent'
                    r={radius}
                    stroke='url(#gradient)'
                    strokeWidth={circleWidth}
                    style={circleStyle}
                />
            </svg>
            <svg className={styles['progress-ring-bg']} height='100%' width='100%'>
                <circle
                    className={styles['progress-ring-bg__circle']}
                    cx={radiusWidth}
                    cy={radiusWidth}
                    fill='transparent'
                    r={radius}
                    stroke='#ECF9FF'
                    strokeWidth={circleWidth}
                    style={circleBgStyle}
                />
            </svg>
            {isEmpty(children) ? (
                <div className={styles['progress-text']}>
                    <span className={styles['progress-text__title']}>{formatProgress(progress)}</span>
                    <span className={styles['progress-text__subtitle']}>本月进度</span>
                </div>
            ) : (
                <div className={styles['progress-text']}>{children}</div>
            )}
        </div>
    );
});

export default ProgressBar;
