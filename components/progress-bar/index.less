@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';

.progress-bar {
    background: #e5e5e5;
    border-radius: (3 / @rem);
    width: 100 / @rem;
    height: 4 / @rem;
    overflow: hidden;

    .progress {
        max-height: 100%;
        height: 4 / @rem;
        background: #00a2e8;
    }
}

.progress-circle {
    position: fixed;
    right: 5 / @rem;
    top: calc(100vh - 58 / @rem);
    z-index: 110;
    width: 58 / @rem;
    height: 58 / @rem;
    border-radius: 50%;
    background-color: @color-white;

    .progress-ring,
    .progress-ring-bg {
        box-sizing: border-box;
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        z-index: 4;
    }

    .progress-ring-bg {
        z-index: 2;
    }

    .progress-ring__circle,
    .progress-ring-bg__circle {
        transition: stroke-dashoffset 0.35s;
        transform: rotate(-90deg);
        transform-origin: 50% 50%;
    }

    .progress-text {
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 6;
        transform: translate(-50%, -50%);
        color: @color-blue;
        display: flex;
        flex-direction: column;
        align-content: center;
        justify-content: center;

        &__title,
        &__subtitle {
            display: block;
            width: 58 / @rem;
            line-height: 1.25;
            text-align: center;
        }

        &__title {
            font-size: 14 / @rem;
            font-weight: bold;
            color: @color-blue;
        }

        &__subtitle {
            font-size: 10 / @rem;
            color: @color-black;
        }
    }
}
