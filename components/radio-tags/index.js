// JS
import React, { memo } from 'react';
import { executeCB, isEmpty } from 'ttp-utils';
import cls from 'classnames';
// LESS
import styles from './index.less';

/**
 * tag筛选TAG
 * @param   {object}         props props
 * @returns {object}               jsx
 */
const RadioTags = memo((props) => {
    // props
    const { classname, list, onChange, value } = props;

    // 常量
    const tagClasss = cls(styles['radio-tags'], classname);

    /**
     * 标签切换点击
     * @param {object} row 标签数据
     */
    const handleChange = (row) => {
        console.log('handleChange', row, `value: ${value}`);
        if (!isEmpty(value) && row.value == value) {
            return;
        }
        executeCB(onChange, row.value, row);
    };

    return (
        <div className={tagClasss}>
            <ul className={styles['main']}>
                {list.map((item) => {
                    const itemClass = cls(styles['main-item'], {
                        [styles['main-item--active']]: String(item.value) === String(value)
                    });
                    return (
                        <li className={itemClass} key={item.value} onClick={() => handleChange(item)}>
                            {item.label}
                        </li>
                    );
                })}
            </ul>
        </div>
    );
});

export default RadioTags;
