@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';

.radio-tags {
    width: 375 / @rem;

    .main {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        background: @color-white;
        padding: 0 (10 / @rem) 10 / @rem;

        &-item {
            min-width: 80 / @rem;
            height: 28 / @rem;
            line-height: 28 / @rem;
            background: @color-white;
            border-radius: 14 / @rem;
            border: 1px solid #f2f2f2;
            color: @color-gray-9;
            font-size: 13 / @rem;
            text-align: center;
            margin: 5 / @rem (3 / @rem);

            &--active {
                border: 1px solid @color-blue;
                color: @color-blue;
                background-color: #e5f5fc;
            }

            &:last-child {
                margin-right: 0;
            }
        }
    }
}
