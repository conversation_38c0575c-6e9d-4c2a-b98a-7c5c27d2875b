// JS
import React, { memo, useRef, useEffect, useState } from 'react';
import { executeCB } from 'ttp-utils';
import { Input, Button } from 'ttp-library';
import cls from 'classnames';
// LESS
import styles from './index.less';

/**
 * 我的客户-搜索组件
 * @param   {object}         props props
 * @returns {object}               jsx
 */
const SearchBox = memo((props) => {
    // props
    const {
        classname,
        searchStyle = {},
        search,
        onSearch,
        getHeight,
        isFixed = false,
        placeholder = '请输入车商姓名/ID/11位手机号'
    } = props;
    // state
    const [searchKeyword, setSearchKeyword] = useState(search);
    // 常量
    const searchRef = useRef(null);
    const searchClasss = cls(styles['search-box'], { [styles['search-box-fixed']]: isFixed }, classname);

    /**
     * 搜索框内容改变事件
     * @param {event}         e event
     */
    const handleChange = (e) => {
        console.log('e.target.value', e.target.value);
        setSearchKeyword(e.target.value);
    };

    /**
     * 搜索回调事件
     */
    const handleSearch = () => {
        executeCB(onSearch, searchKeyword);
    };

    // useEffect：获取标签栏高度
    useEffect(() => {
        if (searchRef) {
            const { height } = searchRef.current.getBoundingClientRect();
            executeCB(getHeight, height);
        }
    }, []);
    useEffect(() => {
        console.log('useEffect-search', search);
        if (search !== searchKeyword) {
            setSearchKeyword(search);
        }
    }, [search]);

    return (
        <div className={searchClasss} ref={searchRef} style={searchStyle}>
            <Input
                className={styles['search-box-input']}
                maxLength={50}
                name='searchKeyword'
                onChange={handleChange}
                placeholder={placeholder}
                prefix={<i className='iconfont icon-search' />}
                suffix={
                    <Button mode='link' onClick={handleSearch} size='small' skin='blue'>
                        搜索
                    </Button>
                }
                type='text'
                value={searchKeyword}
            />
        </div>
    );
});

export default SearchBox;
