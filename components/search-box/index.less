@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';

.search-box {
    width: 100%;
    max-width: 375 / @rem;
    padding: (10 / @rem) (15 / @rem) (5 / @rem);
    background-color: @color-white;

    &-fixed {
        position: fixed;
        z-index: 20;
        left: 50%;
        margin-left: -187 / @rem;
    }

    &-input {
        height: 34 / @rem;
        border-radius: 17 / @rem;
        border: 1px solid #e8e8e8;
        overflow: hidden;

        input {
            height: 34 / @rem;
            line-height: 34 / @rem;
        }
    }
}
