// JS
import React, { memo, useCallback, useState, useRef, useMemo } from 'react';
import { useMutationObserver } from 'ahooks';
import { executeCB } from 'ttp-utils';
import { setRemToPx } from 'common/js/utils';
// 组件
import { Icon } from 'ttp-library';
import Tag from 'components/tag/index.js';
// LESS
import styles from './index.less';

/**
 * 标签列表
 * @typedef  {object} TagListArray
 * @property {string}                   key      标签key
 * @property {string}                   label    标签label
 * @property {Array<string>}            clearKey 清除key的集合
 */

/**
 * 我的客户
 * <AUTHOR>
 * @param   {object}                    props         父组件传入的状态
 * @param   {TagListArray}              props.tagList 标签列表
 * @param   {Function}                  props.onClose 清除标签回调函数
 * @returns {React.ReactElement}                      jsx
 */
const SearchTagBox = memo((props) => {
    // props
    const { tagList, onClose } = props;
    // state
    const [scrollStyle, setScrollStyle] = useState({});
    // ref
    const scrollRef = useRef(null);
    // tag-bar 样式计算
    const tagBarStyle = useMemo(() => {
        return { display: tagList.length == 0 ? 'none' : 'flex' };
    }, [tagList]);

    // 函数
    /**
     * 清除所有标签
     */
    const handleClearAll = useCallback(() => {
        const allKeys = tagList.reduce((keys, item) => [...keys, ...item.clearKey], []);
        executeCB(onClose, allKeys);
    }, [onClose, tagList]);

    // useEffect: 监听dome变化
    useMutationObserver(
        (mutationList) => {
            let scrollWidth = 0;
            const scrollDome = mutationList[0].target;
            scrollDome.childNodes.forEach((tagDome) => {
                const { width } = tagDome.getBoundingClientRect();
                scrollWidth = Math.ceil(scrollWidth + width + setRemToPx(10));
            });
            console.log('useMutationObserver', scrollWidth);
            setScrollStyle({ width: scrollWidth });
        },
        scrollRef,
        { subtree: true, childList: true }
    );

    return (
        <div className={styles['search-tag-bar']} style={tagBarStyle}>
            <div className={styles['tag-list']}>
                <div className={styles['tag-list-scroll']} ref={scrollRef} style={scrollStyle}>
                    {tagList.map((item) => (
                        <Tag
                            classname={styles['tag-item']}
                            id={item.key}
                            key={item.label}
                            themeColor='#ffffff'>
                            {item.label}
                            <span onClick={() => executeCB(onClose, item.clearKey)}>
                                <Icon className={styles['tag-icon']} type='iconfont-clear' />
                            </span>
                        </Tag>
                    ))}
                </div>
            </div>
            <div className={styles['clear-btn']} onClick={handleClearAll}>
                <Icon className={styles['clear-btn-icon']} type='iconfont-delete' />
                清空
            </div>
        </div>
    );
});

export default SearchTagBox;
