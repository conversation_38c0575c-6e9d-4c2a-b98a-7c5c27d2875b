@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';

.search-tag-bar {
    padding: 0 (10 / @rem);
    background-color: @color-page-bg;
    height: 40 / @rem;
    display: flex;

    .tag-list {
        width: 280 / @rem;
        height: 40 / @rem;
        overflow-x: auto;
    }

    .tag-list-scroll {
        min-width: 100%;
        max-width: 1000%;
        height: 40 / @rem;
    }

    .tag-item {
        float: left;
        color: @color-gray-3;
        border-radius: 4 / @rem;
        margin-top: 8 / @rem;
    }

    .tag-icon {
        margin-left: 5 / @rem;
        width: 16 / @rem;
        height: 16 / @rem;
    }

    .clear-btn {
        width: 73 / @rem;
        height: 40 / @rem;
        background-color: @color-page-bg;
        text-align: center;
        line-height: 40 / @rem;
        text-align: right;

        &-icon {
            font-size: 20 / @rem;
            line-height: 40 / @rem;
            margin-right: 5 / @rem;
        }
    }
}
