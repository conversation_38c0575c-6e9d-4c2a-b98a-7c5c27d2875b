/**
 * 公共设置页面title组件
 *
 * @module components/set-global-title/global
 * @requires module:react
 */
import React, { useEffect } from 'react';

// less
import './global.less';

/**
 * 设置页面title组件
 * @param   {object}           props props
 * @returns {React.ReactDOM}         jsx
 */
function SetTitle(props) {
    const { title } = props;
    useEffect(() => {
        document.title = title;
        return () => (document.title = '天天拍车');
    }, []);

    return <div>{props.children}</div>;
}

export default SetTitle;
