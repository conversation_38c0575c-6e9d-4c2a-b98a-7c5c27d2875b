// JS
import React, { memo, useMemo } from 'react';
import { executeCB, throttle, isEmpty } from 'ttp-utils';
import cls from 'classnames';
// 组件
import ReactSlider from 'react-slider';
// LESS
import styles from './index.less';

/**
 * 滑块滚动输入条
 * @param   {object}         props props
 * @returns {object}               jsx
 */
const SliderBar = memo((props) => {
    // props
    const {
        className,
        // 默认值（初始化）
        defaultValue,
        // 步长
        step = 1,
        // 起点和终点之间的间距
        minDistance = 1,
        // 当前值
        value,
        // 最小值/最大值
        minAndMax = [0, 50],
        // change回调函数
        onChange,
        // 左侧标题
        preTitle,
        // 右侧标题
        sufTitle,
        // 是否展示[负无限，正无限]
        infinite = [false, true]
    } = props;

    // 常量
    const sliderClass = cls(styles['slider-box'], className);
    const thumbClass = cls(styles['thumb'], 'icon-slider-thumb');
    // 计算属性
    // 最小值-文本展示
    const minText = useMemo(() => {
        return infinite[0] == true ? `${minAndMax[0]}以下` : minAndMax[0];
    }, [minAndMax, infinite]);
    // 最大值-文本展示
    const maxText = useMemo(() => {
        return infinite[1] == true ? `${minAndMax[1]}以上` : minAndMax[1];
    }, [minAndMax, infinite]);
    // 最小值
    const min = useMemo(() => {
        return infinite[0] == true ? minAndMax[0] - step : minAndMax[0];
    }, [minAndMax, step, infinite]);
    // 最大值
    const max = useMemo(() => {
        return infinite[1] == true ? minAndMax[1] + step : minAndMax[1];
    }, [minAndMax, step, infinite]);

    /**
     * 标签切换点击
     * @param {Array<number>}         data  新值
     * @param {number}         index 是 thumb index
     */
    const handleChange = throttle(
        (data, index) => {
            // console.log('handleChange', data, index);
            executeCB(onChange, data, index);
        },
        30,
        1000 / 60
    );

    // useEffect(() => {
    //     console.log('SliderBar', props.value);
    // }, [props]);
    console.log('SliderBar preTitle', preTitle);
    return (
        <div className={sliderClass}>
            <h2 className={styles['title']}>
                {isEmpty(preTitle) ? null : <span className={styles['title-left']}>{preTitle}</span>}
                {isEmpty(sufTitle) ? null : <span className={styles['title-right']}>{sufTitle}</span>}
            </h2>
            <div className={styles['slider-value']}>
                <span className={styles['slider-value-text']}>{minText}</span>
                <span className={styles['slider-value-text']}>{maxText}</span>
            </div>
            <ReactSlider
                className={styles['slider-bar']}
                defaultValue={defaultValue || [min, max]}
                max={max}
                min={min}
                minDistance={minDistance}
                onChange={handleChange}
                pearling
                // renderThumb={(props, state) => <div {...props}>{state.valueNow}</div>}
                step={step}
                thumbClassName={thumbClass}
                trackClassName='track'
                value={[value[0] || min, value[1] || max]}
            />
        </div>
    );
});

export default SliderBar;
