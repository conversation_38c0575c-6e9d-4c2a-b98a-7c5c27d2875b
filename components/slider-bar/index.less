@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';
@import '../../common/less/icon.less';

.slider-box {
    padding: 0 (15 / @rem);

    .title {
        display: flex;
        justify-content: space-between;

        &-left {
            font-size: 14 / @rem;
            font-weight: bold;
            line-height: 30 / @rem;
            color: @color-gray-3;
        }

        &-right {
            font-size: 14 / @rem;
            line-height: 30 / @rem;
            color: @color-gray-6;
            text-align: right;
        }
    }

    .slider-value {
        display: flex;
        justify-content: space-between;

        &-text {
            font-size: 13 / @rem;
            line-height: 30 / @rem;
            color: @color-gray-3;
            display: block;
            text-align: center;
            min-width: 22 / @rem;
        }
    }

    .slider-bar {
        height: 22 / @rem;

        :global {
            /* 自定义滑动条轨道样式 */
            .track {
                margin-top: 9 / @rem;
                height: 4 / @rem;
                border-radius: 4 / @rem;
            }

            /* 未选中样式 */
            .track-1 {
                background: @color-blue;
            }

            /* 激活部分轨道样式 */
            .track-0,
            .track-2 {
                background: @color-gray-drop;
            }
        }

        /* 自定义滑块样式 */
        .thumb {
            background-color: @color-white;
            border-radius: 50%;
            cursor: pointer;
            outline: none;
            line-height: 22 / @rem;
            text-align: center;
        }
    }
}
