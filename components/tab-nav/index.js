// JS
import React, { memo } from 'react';
import { executeCB } from 'ttp-utils';
import cls from 'classnames';
// 组件
import { TabNav, Icon } from 'ttp-library';
// LESS
import styles from './index.less';
const NavItem = TabNav.Item;
/**
 * 页面顶部TAB组件
 * @param   {object}         props props
 * @returns {object}               jsx
 */
const TabNavBar = memo((props) => {
    // props
    const { className, list, current, maxLength = 2, onChange, getHeight, onIconClick } = props;

    // 常量
    const navClasss = cls(styles['crm-tabnav'], className);

    /**
     * 获取ref
     * @param {object} ref ref
     */
    const getRef = (ref) => {
        if (ref) {
            const { height } = ref.getBoundingClientRect();
            executeCB(getHeight, height);
        }
    };

    /**
     * 标签切换点击
     * @param {number} activeIndex 当前索引
     */
    const handleChange = (activeIndex) => {
        if (activeIndex === current) {
            return;
        }
        executeCB(onChange, list[activeIndex]);
    };
    /**
     * icon点击事件
     * @param {event} e event
     */
    const handleIconClick = (e) => {
        e.stopPropagation();
        executeCB(onIconClick);
    };

    return (
        <TabNav
            className={navClasss}
            current={current}
            getRef={getRef}
            onChange={handleChange}
            position='top'
            showLength={maxLength}>
            {Array.isArray(list) &&
                list.map((item, index) => (
                    <NavItem disabled={item.disabled} key={item.url}>
                        {item.title}
                        {item.icon && current == index && (
                            <Icon
                                className={`${styles['icon-detail']} ${item.icon}`}
                                onClick={handleIconClick}
                            />
                        )}
                    </NavItem>
                ))}
        </TabNav>
    );
});

export default TabNavBar;
