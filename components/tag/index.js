/* JS */
import React, { memo, useMemo } from 'react';
import cls from 'classnames';
import { isString, isFun } from 'ttp-utils';
/* LESS */
import style from './index.less';

const TYPE_COLOR = {
    success: '#68cc4e',
    danger: '#ff4e4e',
    info: '#00a2e8',
    warning: '#ffcc39',
    recommend: '#43d0cf',
    fault: '#ebaa16',
    severity: '#7736d2',
    default: '#666666'
};

/**
 * 16进制颜色正则表达式
 */
const HEX_REG = /^#([0-9a-zA-Z]{3}|[0-9a-zA-Z]{6})$/;

/**
 * 是否是16进制颜色值
 *
 * @param {string} val 颜色值字符串
 * @returns {boolean} 是否是16进制颜色值
 */
const isHexColor = (val) => HEX_REG.test(val);

/**
 * 将16进制颜色值转换为rgb的形式
 *
 * @param   {string}                 hex 以#开头的16进制颜色值
 * @returns {Array<number>}              rgb元组
 */
const hex2Rgb = (hex = '#FFF') => {
    let rgb = [255, 255, 255];
    if (!isString(hex) || !HEX_REG.test(hex)) {
        return rgb;
    }
    const hexStr = HEX_REG.exec(hex)[1].toLowerCase();
    switch (hexStr.length) {
        case 3:
            rgb[0] = parseInt(`0x${hexStr[0]}${hexStr[0]}`);
            rgb[1] = parseInt(`0x${hexStr[1]}${hexStr[1]}`);
            rgb[2] = parseInt(`0x${hexStr[2]}${hexStr[2]}`);
            break;
        case 6:
            rgb[0] = parseInt(`0x${hexStr[0]}${hexStr[1]}`);
            rgb[1] = parseInt(`0x${hexStr[2]}${hexStr[3]}`);
            rgb[2] = parseInt(`0x${hexStr[4]}${hexStr[5]}`);
            break;
        default:
            break;
    }
    return rgb;
};

/**
 * 规范化颜色值 0<=value<=255
 *
 * @param   {number}                value 颜色值
 * @returns {number}                      颜色值
 */
const roundChannel = (value) => {
    const v = Math.round(Number(value));
    if (v > 255) {
        return 255;
    }
    if (v < 0) {
        return 0;
    }
    return v;
};

/**
 * 在底色上覆盖一层透明色后应该显示的颜色值
 *
 * @param   {Array<number>}           background rgb形式的底色
 * @param   {Array<number>}           themeColor rgba形式的透明色
 * @returns {Array<number>}                      最终显示的rgb颜色
 */
const alphaBlend = (background, themeColor) => {
    const alpha = themeColor[3];
    return [
        (1 - alpha) * themeColor[0] + alpha * background[1],
        (1 - alpha) * themeColor[1] + alpha * background[2],
        (1 - alpha) * themeColor[2] + alpha * background[2]
    ].map((item) => roundChannel(item));
};

/**
 * 组件的主题色
 *
 * @param   {string}                themeColor 类型/颜色
 * @returns {string}                           组件的主题色
 */
const getThemeColor = (themeColor) => {
    if (TYPE_COLOR[themeColor]) {
        return TYPE_COLOR[themeColor];
    } else if (isHexColor(themeColor)) {
        return themeColor;
    } else {
        return TYPE_COLOR.default;
    }
};

/**
 * tag组件
 * <AUTHOR>
 * @param   {object}         props            参数
 * @param   {string}         props.className  样式名称
 * @param   {string}         props.id         id
 * @param   {string}         props.style      style
 * @param   {boolean}        props.primary    是否深色，默认：false
 * @param   {string}         props.themeColor 颜色，可选：success,danger,info,warning,recommend,fault,severity,default,'#666666'
 * @param   {Function}       props.onClick    点击事件
 * @param   {Array}          props.children   chileren
 * @returns {React.ReactDOM}                  返回值
 */
const Tag = memo((props) => {
    const { id, classname, themeColor = 'info', primary = false, children, onclick } = props;

    /**
     * 获取css变量
     * @type {object}
     */
    const tagStyle = useMemo(() => {
        const [red, green, blue] = hex2Rgb(getThemeColor(themeColor));
        let backgroundColor = `rgb(${red}, ${green}, ${blue})`;
        if (!primary) {
            const [bgR, bgG, bgB] = alphaBlend([255, 255, 255], [red, green, blue, 0.92]);
            backgroundColor = `rgb(${bgR}, ${bgG}, ${bgB})`;
        }
        return {
            '--red': red,
            '--green': green,
            '--blue': blue,
            '--background-color': backgroundColor
        };
    }, [themeColor, primary]);

    /**
     * tag 点击事件
     * <AUTHOR>
     * @param   {object}                       event event
     */
    const handleClick = (event) => {
        if (isFun(onclick)) {
            onclick(event);
        }
    };

    /**
     * 样式
     * @type {string}
     */
    const tagClass = useMemo(() => {
        const primaryClass = primary ? 'tag-primary' : 'tag-secondary';
        return cls(style['tag'], style[primaryClass], classname);
    }, [primary, classname]);

    return (
        <div className={tagClass} id={id} onClick={handleClick} style={tagStyle}>
            {children}
        </div>
    );
});

/**
 * 标签列表
 * <AUTHOR>
 * @param   {object}         props                     cprops
 * @param   {Array<object>}  props.dataList            数据列表
 * @param   {Array<object>}  props.dataList.id         id
 * @param   {Array<object>}  props.dataList.text       文本
 * @param   {Array<object>}  props.dataList.onclick    点击事件
 * @param   {Array<object>}  props.dataList.primary    是否是主色
 * @param   {Array<object>}  props.dataList.themeColor 主题色
 * @param   {Array<object>}  props.dataList.key        key
 * @param   {string}         props.classname           样式名称
 * @returns {React.ReactDOM}                           返回值
 */
export const TagList = memo((props) => {
    const { dataList = [], classname } = props;
    if (!dataList.length) {
        return null;
    }

    return (
        <div className={cls(style['tag-list'], classname)}>
            {dataList.map((item) => {
                return (
                    <Tag
                        id={item.id || item.key}
                        key={item.key}
                        onclick={item.onclick}
                        primary={item.primary}
                        themeColor={item.themeColor}>
                        {item.text}
                    </Tag>
                );
            })}
        </div>
    );
});

export default Tag;
