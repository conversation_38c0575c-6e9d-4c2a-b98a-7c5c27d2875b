@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';

.tag {
    --red: 0;
    --green: 0;
    --blue: 0;
    border-radius: 2 / @rem;
    background-color: @color-white;
    min-height: 22 / @rem;
    min-width: 20 / @rem;
    margin-right: 8 / @rem;
    font-size: 12 / @rem;
    line-height: 14 / @rem;
    border-radius: 2 / @rem;
    white-space: nowrap;
    padding: (4 / @rem);
    display: inline-flex;
    justify-content: center;
    align-items: center;

    &-primary {
        background-color: var(--background-color);
        color: @color-white;
    }
    &-secondary {
        border: 1px solid;
        padding: (3 / @rem);
        border-color: rgba(var(--red), var(--green), var(--blue), 0.2);
        background-color: var(--background-color);
        color: rgba(var(--red), var(--green), var(--blue), 1);
    }
}
