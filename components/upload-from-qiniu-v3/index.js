// JS
import React, { useState, memo, useRef } from 'react';
import { getUrlQueryObject, executeCB, guid, isEmpty, getFetch } from 'ttp-utils';
import * as qiniuV3 from 'plugins/qiniuv3.min.js';
import { PORT_URL } from 'common/js/constant.js';
// 组件
import { message } from 'ttp-library';
// LESS
import styles from './index.less';

/**
 * 获取dealerId
 * @type {string}
 */
const { dealerId } = getUrlQueryObject();

/**
 * 默认域名
 * @type {string}
 */
const DEFAULT_DOMAIN_URL = 'https://testvideo.ttpaicdn.com/';

/**
 * 七牛上传
 * @returns   {Function}     function
 */
const QiniuUpload = memo((props) => {
    const {
        multiple = false,
        maxSize = 10 * 1024,
        domain = DEFAULT_DOMAIN_URL,
        accept,
        fileType,
        uploadedList = [],
        limit
    } = props;
    const fileInput = useRef(null);
    const [isUploading, setLoading] = useState(false);
    console.log('domain:', domain, uploadedList);
    let tokenInfo = '';

    /**
     * 校验文件大小
     *
     * @param {File} file 待上传的文件对象
     * @returns {boolean} 是否通过文件格式校验
     */
    function checkFileSize(file) {
        const fileSize = file.size;
        if (maxSize) {
            return fileSize <= maxSize * 1024;
        }
        return true;
    }

    // eslint-disable-next-line jsdoc/require-jsdoc
    function getTokenAndKey() {
        const data = {
            bizType: 'addFollowRecord',
            bizNo: dealerId,
            fileType: fileType
        };
        return getFetch({
            url: PORT_URL.getQiniuToken,
            data
        }).then((res) => {
            const key = res.result?.key ?? guid();
            const token = res.result?.token ?? '';
            // tokenInfo = { ...res, xhr };
            console.log('tokenInfo:', res);
            tokenInfo = res.result;
            return {
                key,
                token
            };
        });
    }

    /**
     * next 接收上传进度信息的回调函数
     *
     * @param {object} root0 参数对象
     * @param {object} root0.total 上传信息
     */
    function next({ total }) {
        const file = {
            percent: total.percent
        };
        setLoading(true);
        executeCB(props.onProgress, { file });
    }

    /**
     * error 上传错误后触发的函数
     *
     * @param {object} err 错误对象
     */
    function error(err) {
        message(err.message);
        executeCB(props.onError, { err });
    }

    /**
     * 上传完成后的回调函数
     *
     * @param {object} res 后端返回信息
     */
    function complete(res) {
        setLoading(false);
        const url = tokenInfo.url;
        console.log('tokenInfo:', tokenInfo);
        const thumbnailFileUrl = tokenInfo.thumbnailUrl;
        let successList =
            fileType == 'video'
                ? {
                      fileUrl: url,
                      thumbnailFileUrl
                  }
                : url;
        console.log('fileUploaded:', successList);
        // setFileList(successList);
        executeCB(props.onSuccess, successList);
    }

    // eslint-disable-next-line jsdoc/require-jsdoc
    function upload(file) {
        console.log('upload:', file);
        if (!checkFileSize(file)) {
            message('文件大小超出限制');
            executeCB(props.onExceededSize, file);
            return;
        }
        getTokenAndKey()
            .then(({ key, token }) => {
                const observable = qiniuV3.upload(file, key, token);
                observable.subscribe({
                    next: next,
                    error: error,
                    complete: complete
                }); // 上传开始
            })
            .catch((err) => executeCB(props.onError, { error: err }));
    }

    /**
     * uploadFiles multiple过滤
     *
     * @param    {Array} files 文件对象列表
     * @returns  {undefined}
     */
    function uploadFiles(files) {
        let postFiles = Array.prototype.slice.call(files);
        const file = postFiles[0];
        if (isEmpty(file)) {
            return;
        }
        upload(file);
    }

    /**
     * 删除图片
     * @param  {number} index 图片索引
     * @param  {object} file 图片对象
     */
    const onDeleteFile = (index, file) => {
        // const list = uploadedList.filter((item) => item.fileUrl !== file.fileUrl);
        // setFileList(list);
        executeCB(props.onDelete, index, file);
    };

    /**
     * handleClick 点击事件
     */
    function handleClick() {
        fileInput.current.click();
    }

    /**
     * handleChange change事件
     *
     * @param {object} e 事件对象
     */
    function handleChange(e) {
        const files = e.target.files;
        if (!files) {
            return;
        }
        uploadFiles(files);
        fileInput.current.value = null;
    }
    const isOverLimit = uploadedList.length >= limit;

    console.log('isUploading:', isUploading);
    return (
        <div className={styles['v-qiniu-upload-container']}>
            {Array.isArray(uploadedList) &&
                uploadedList.map((item, index) => {
                    return (
                        <div className={styles['v-qiniu-upload-pics']} key={item.fileUrl}>
                            {item.thumbnailFileUrl ? (
                                <>
                                    <img className={styles['v-upload-img']} src={item.thumbnailFileUrl} />
                                    <i className={`${styles['icon-video']} icon-play-video`} />
                                </>
                            ) : (
                                <i className='icon-play-audio' />
                            )}
                            <i
                                className='remove-btn iconfont2 iconfont-close'
                                onClick={() => onDeleteFile(index, item)}
                            />
                        </div>
                    );
                })}

            <div
                className={`${styles['v-qiniu-upload']} ${isOverLimit ? styles['hide'] : ''}`}
                onClick={handleClick}>
                <div className={styles['v-qiniu-upload__browse_button']}>
                    <input
                        accept={accept}
                        className={styles['v-qiniu-upload__file-input']}
                        multiple={multiple}
                        onChange={handleChange}
                        ref={fileInput}
                        type='file'
                    />
                    <div className={styles['v-qiniu-upload__default_button']}>
                        <i className='iconfont2 iconfont-upload' />
                    </div>
                    {isUploading && <p className={styles['v-qiniu-upload__progress']}>上传中...</p>}
                </div>
            </div>
        </div>
    );
});

export default QiniuUpload;
