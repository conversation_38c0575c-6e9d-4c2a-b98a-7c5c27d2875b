@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';
.v-qiniu-upload-container {
    display: inline-flex;
    flex-wrap: wrap;
    .v-qiniu-upload-pics {
        display: flex;
        width: 80 / @rem;
        height: 80 / @rem;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        line-height: 80 / @rem;
        border: 1px dashed #ddd;
        text-align: center;
        border-radius: 4 / @rem;
        margin-right: 5 / @rem;
        position: relative;
        margin-bottom: 5 / @rem;
    }

    .v-upload-img {
        width: 100%;
        height: 100%;
        display: block;
    }
    :global {
        .remove-btn {
            position: absolute;
            right: 0;
            top: 0;
            z-index: 20;
            color: #fff;
            background: rgba(53, 53, 53, 0.4);
            font-size: 12 / @rem;
            width: 16 / @rem;
            height: 16 / @rem;
            line-height: 16 / @rem;
            text-align: center;
        }
    }
    .hide {
        visibility: hidden;
    }

    .icon-video {
        position: absolute;
        z-index: 22;
    }
}
.v-qiniu-upload {
    display: flex;
    position: relative;
    width: 80 / @rem;

    &__file-input {
        visibility: hidden;
        width: 0px;
        height: 0px;
    }
    &__browse_button {
        display: inline-flex;
    }
    &__default_button {
        display: inline-flex;
        width: 80 / @rem;
        height: 80 / @rem;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        line-height: 80 / @rem;
        border: 1px dashed #ddd;
        text-align: center;
        position: relative;
        overflow: hidden;
        color: #aaa;
    }
    &__progress {
        position: absolute;
        left: 0;
        width: 100%;
        bottom: 0;
        text-align: center;
        padding: 2 / @rem 0;
        background-color: rgba(17, 17, 17, 0.6);
        color: #fff;
        z-index: 12;
        font-size: 11 / @rem;
    }
    .icon-upload {
        font-size: 20 / @rem;
        line-height: 90 / @rem;
        text-align: center;
    }
    :global {
        .iconfont-upload {
            color: #ddd;
        }

        .Saitama-fileUpload-upload-item {
            margin-right: 5 / @rem;
        }
    }
}
