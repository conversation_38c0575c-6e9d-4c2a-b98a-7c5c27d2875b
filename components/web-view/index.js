// JS
import React, { memo } from 'react';
import { Drawer } from 'ttp-library';
// LESS
import style from './index.less';

/**
 * web view 弹窗
 */
const DrawerWebView = memo((props) => {
    const { open, iframeURL, onClose, title = '隐私条款', hash = 'drawer-web-view' } = props;
    return (
        <Drawer destroy={false} full hash={hash} mode='right' onClose={onClose} open={open} title={title}>
            <iframe className={style['drawer-iframe']} key={iframeURL} src={iframeURL} />
        </Drawer>
    );
});

export default DrawerWebView;
