// JS
import React, { memo, useEffect } from 'react';
import { useSetState } from 'ahooks';
import { PORT_URL } from 'common/js/constant.js';
import { executeCB } from 'ttp-utils';
// 组件
import { ModalSelect } from 'ttp-library';
// 员工接口mapping
const workerMapping = {
    label: 'label',
    value: 'value'
};

/**
 * 获取员工列表
 * <AUTHOR>
 * @returns {object}                    接口参数
 */
const defaultPort = () => ({
    url: PORT_URL.getWorkerListPort,
    data: { teamId: '' },
    time: 300
});

/**
 * 我的客户-跟进记录详情
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const WorkerDrawer = memo((props) => {
    // props
    const { open, teamId, value, multiple, onChange, onClose } = props;
    // state
    const [port, setPort] = useSetState(defaultPort());

    /**
     * 关闭弹框
     */
    const handleClose = () => {
        executeCB(onClose, false);
    };

    /**
     * 确定选择
     * @param {object} checkedData   data
     * @param {object}  rowData 行数据
     */
    const handleChange = (checkedData, rowData) => {
        executeCB(onChange, checkedData, rowData);
        handleClose();
    };

    const searchBarProps = {
        placeholder: '请输入搜索关键词',
        hasCancelButton: false
    };

    useEffect(() => {
        setPort({ data: { teamId } });
    }, [teamId]);

    return (
        <ModalSelect
            className='worker-drawer'
            destroy
            filterOption
            mapping={workerMapping}
            multiple={multiple}
            onClear={handleClose}
            onClose={handleClose}
            onOk={handleChange}
            open={open}
            port={port}
            searchBarProps={searchBarProps}
            title='请选择员工'
            type='tag'
            value={value}
        />
    );
});

export default WorkerDrawer;
