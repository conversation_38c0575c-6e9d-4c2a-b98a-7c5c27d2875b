import { setLoginCookie, removeLoginCookie } from 'common/js/utils';

/**
 * 功能说明
 *
 * @returns    {object} object
 */
function initState() {
    return {
        // 是否登录
        isLogin: false,
        // 菜单权限
        menuList: [],
        // 用户名
        realName: '',
        // 企业微信ID
        ttpaiQywxId: ''
    };
}

export default {
    namespace: 'app',

    state: initState(),

    reducers: {
        /**
         * 重置状态
         * @returns {object}                     更新过的state
         */
        resetState() {
            return initState();
        },

        /**
         * 更新状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateState(state, action) {
            return {
                ...state,
                ...action.payload
            };
        }
    },

    effects: {
        /**
         * 用户登出
         * @param   {object}               _              入参
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.put     put
         */
        *logout(_, { put }) {
            // 登出后 清除cookie
            removeLoginCookie();
            yield put({ type: 'resetState' });
        },
        /**
         * 登录成功回调
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *loginSuccess({ payload }, { put }) {
            const { token, menuList, ...other } = payload;
            // 写入 cookie
            if (token) {
                setLoginCookie(payload);
            }
            console.log('app/updateState', menuList);

            yield put({
                type: 'updateState',
                payload: {
                    ...other,
                    menuList: Array.isArray(menuList) ? menuList.map((item) => item.url) : []
                }
            });
            return true;
        },

        /**
         * 获取商家数据
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *updateLoginState({ payload }, { put }) {
            console.log('app/updateState', payload);
            const { menuList } = payload;
            console.log('app/updateState', menuList);
            yield put({
                type: 'updateState',
                payload: {
                    ...payload,
                    menuList: Array.isArray(menuList) ? menuList.map((item) => item.url) : []
                }
            });
            return true;
        }
    },
    subscriptions: {}
};
