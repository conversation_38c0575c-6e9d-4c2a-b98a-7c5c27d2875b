{"author": "ttpai.cn", "browserslist": ["last 4 version", "Chrome >= 60", "Firefox >= 60", "safari >= 11", "iOS >= 11"], "description": "企业微信-经销商CRM", "devDependencies": {"@babel/core": "~7.22.9", "@babel/eslint-parser": "~7.22.9", "@babel/plugin-proposal-class-properties": "~7.18.6", "@babel/plugin-proposal-decorators": "~7.22.7", "@babel/plugin-proposal-do-expressions": "~7.22.5", "@babel/plugin-proposal-export-default-from": "~7.22.5", "@babel/plugin-proposal-function-bind": "~7.22.5", "@babel/plugin-proposal-function-sent": "~7.22.5", "@babel/plugin-proposal-pipeline-operator": "~7.22.5", "@babel/plugin-proposal-throw-expressions": "~7.22.5", "@babel/plugin-syntax-jsx": "~7.22.5", "@babel/plugin-syntax-object-rest-spread": "~7.8.3", "@babel/plugin-transform-async-to-generator": "~7.22.5", "@babel/plugin-transform-modules-commonjs": "~7.22.5", "@babel/plugin-transform-runtime": "~7.22.9", "@babel/preset-env": "~7.22.9", "@babel/preset-react": "~7.22.5", "@babel/runtime-corejs2": "~7.22.6", "ahooks": "~3.8.4", "assets-webpack-plugin": "~7.1.1", "autoprefixer": "~10.4.21", "babel-loader": "~10.0.0", "babel-plugin-dva-hmr": "~0.4.2", "babel-plugin-import": "~1.13.8", "babel-plugin-transform-react-remove-prop-types": "~0.4.24", "babel-plugin-transform-remove-console": "~6.9.4", "babel-plugin-ttp-try-catch": "~1.2.1", "browserslist": "~4.24.4", "case-sensitive-paths-webpack-plugin": "~2.4.0", "classnames": "~2.5.1", "clean-webpack-plugin": "~4.0.0", "cross-env": "~7.0.3", "css-loader": "~5.2.7", "css-minimizer-webpack-plugin": "~5.0.0", "dva": "~2.6.0-beta.22", "dva-loading": "~3.0.25", "esbuild-loader": "~2.21.0", "eslint": "~8.44.0", "eslint-config-prettier": "~8.8.0", "eslint-config-ttpai": "~0.1.11", "eslint-plugin-jsdoc": "~47.0.2", "eslint-plugin-prettier": "~4.2.1", "eslint-plugin-react": "~7.37.4", "eslint-plugin-ttpai": "~0.0.3", "eslint-webpack-plugin": "~4.2.0", "file-loader": "~6.2.0", "handlebars": "~4.7.8", "handlebars-loader": "~1.7.3", "history": "~4.10.1", "html-webpack-plugin": "~5.6.3", "husky": "~8.0.3", "isomorphic-fetch": "~3.0.0", "js-cookie": "~3.0.5", "less": "~4.2.2", "less-loader": "~11.1.3", "lint-staged": "~13.3.0", "mini-css-extract-plugin": "~2.9.2", "postcss": "~8.5.4", "postcss-loader": "~7.3.3", "postcss-ttp-sprites": "^4.3.1", "prettier": "~2.8.8", "progress-bar-webpack-plugin": "~2.1.0", "prop-types": "~15.8.1", "react": "~16.14.0", "react-dom": "~16.14.0", "react-hot-loader": "~4.13.1", "react-redux": "~7.2.6", "react-router-dom": "~4.3.1", "react-router-redux": "~4.0.8", "react-slider": "~2.0.6", "redbox-react": "~1.6.0", "redux": "~4.1.2", "style-loader": "~3.3.2", "terser-webpack-plugin": "~5.3.14", "text-loader": "0.0.1", "thread-loader": "~4.0.4", "ttp-library": "~2.23.2", "ttp-utils": "~2.5.4", "ttpai-config": "~3.6.2", "url-loader": "~4.1.1", "webpack": "~5.98.0", "webpack-bundle-analyzer": "~4.10.2", "webpack-cli": "~5.1.4", "webpack-dev-server": "~4.15.1", "webpack-merge": "~5.9.0"}, "lint-staged": {"*.{js,jsx,vue}": ["prettier --config .prettierrc.js --write", "eslint --fix"], "**/*.{less,css}": ["prettier --config .prettierrc.js --write"]}, "main": "app.js", "name": "frontwxdealercrm", "private": true, "scripts": {"build:dev": "cross-env NODE_ENV_PACK=pack npm run dll:dev && cross-env NODE_ENV_PACK=pack npm run dev", "build:prod": "cross-env NODE_ENV_PACK=pack npm run dll && cross-env NODE_ENV_PACK=pack npm run dist", "dev": "cross-env NODE_ENV=development webpack", "dist": "cross-env NODE_ENV=production webpack", "dll": "cross-env NODE_ENV=production webpack --config webpack.config.dll.js", "dll:dev": "cross-env NODE_ENV=development webpack --config webpack.config.dll.js", "eslint": "eslint --fix --ext .js,jsx common components models routes src", "lint": "npm run prettier && npm run eslint", "prepare": "husky install", "preserver": "ttpai cleani", "prettier": "prettier --config .prettierrc.js --write {common,components,config,models,routes,src}/**/*{.js,.jsx,.json,.less,.css}", "server": "npm run dll:dev && cross-env NODE_ENV=development webpack server --config webpack.config.server.js", "start": "npm run server"}, "ttpai-config": {"projectType": "react", "projectName": "frontwxdealercrm", "pid": "wx-dealer-crm", "hbsData": {"title": "车商CRM系统", "comcdn": "//cdn01.ttpaicdn.com"}}, "version": "1.0.0"}