const postcss = require('postcss');
const autoprefixer = require('autoprefixer');
const postcssSprites = require('postcss-ttp-sprites');
// x = 375 / 320 * 20
const rem = 20;
/**
 * postcss配置
 * @param   {string}           env process.env.
 * @returns {Array}                plugins
 */
const PostcssOpt = (env) => {
    // console.error("===== PostcssOpt - process.env.NODE_ENV =======:" + env);

    let plugins = [autoprefixer({ remove: false })];

    if (env === 'production') {
        plugins.push(
            postcssSprites({
                // 相对路径，将让你的输出样式的文件夹
                stylesheetPath: './src/css/',
                //  雪碧图合并后存放地址
                spritePath: './common/images/',
                // 支持retina，可以实现合并不同比例图片
                retina: true,
                spritesmith: {
                    padding: 10
                },
                hooks: {
                    // eslint-disable-next-line jsdoc/require-jsdoc
                    onUpdateRule: (rule, token, image) => {
                        let backgroundSizeX = image.spriteWidth / 2;
                        let backgroundSizeY = image.spriteHeight / 2;
                        let backgroundPositionX = -image.coords.x / 2;
                        let backgroundPositionY = -image.coords.y / 2;

                        backgroundSizeX = isNaN(backgroundSizeX) ? 0 : backgroundSizeX;
                        backgroundSizeY = isNaN(backgroundSizeY) ? 0 : backgroundSizeY;
                        backgroundPositionX = isNaN(backgroundPositionX) ? 0 : backgroundPositionX;
                        backgroundPositionY = isNaN(backgroundPositionY) ? 0 : backgroundPositionY;

                        let backgroundImage = postcss.decl({
                            prop: 'background-image',
                            value: 'url(' + image.spriteUrl + ')'
                        });

                        let backgroundSize = postcss.decl({
                            prop: 'background-size',
                            value: backgroundSizeX / rem + 'rem ' + backgroundSizeY / rem + 'rem'
                        });

                        let backgroundPosition = postcss.decl({
                            prop: 'background-position',
                            value: backgroundPositionX / rem + 'rem ' + backgroundPositionY / rem + 'rem'
                        });

                        rule.insertAfter(token, backgroundImage);
                        rule.insertAfter(backgroundImage, backgroundPosition);
                        rule.insertAfter(backgroundPosition, backgroundSize);
                    }
                },
                // eslint-disable-next-line jsdoc/require-jsdoc
                filterBy: (image) => {
                    if (image.url.indexOf('src/images/icons/') === -1) {
                        return Promise.reject('');
                    }
                    return Promise.resolve();
                }
            })
        );
    }
    return plugins;
};

/**
 * postcss配置
 * @param   {object}           ctx  上下文
 * @returns {object}                postcss配置
 */
module.exports = (ctx) => ({
    parser: false,
    plugins: PostcssOpt(ctx.env)
});
