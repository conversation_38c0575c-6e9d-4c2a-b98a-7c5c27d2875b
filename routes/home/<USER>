/**
 * Login 登录页 module
 *
 * @module   routes/login/index
 * <AUTHOR>
 * @requires module:react
 * @requires module:dva
 */

/* JS */
import React, { memo, useMemo, useEffect } from 'react';
import { connect } from 'dva';
import { Link } from 'dva/router';
import { PAGE_URL } from 'routes/router-config.js';
import RankTable from './components';
// 组件
import { Icon, Loader } from 'ttp-library';
/* LESS */
import styles from './index.less';
// 图片
import LoginBanner from 'src/images/banner-login.png';

/**
 * Login 登录组件
 *
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const HomePage = memo((props) => {
    // props
    const { home, app, dispatch, loading } = props;

    const { menuList } = app;
    const { rankList, offlineDealerAdmin } = home;
    const isLoading = Boolean(loading.effects['home/getRankListData']);
    // 计算属性
    const menuListData = useMemo(() => {
        const menus = [
            {
                label: '我的工作',
                className: styles['menu-item'],
                icon: ['icon-home-blue'],
                pathname: PAGE_URL.myWork
            },
            {
                label: '我的团队',
                className: styles['menu-item'],
                icon: ['icon-home-orange'],
                pathname: PAGE_URL.managerPerformancePanel
            },
            {
                label: '车源智推',
                className: styles['menu-item'],
                icon: ['icon-home-recommend'],
                pathname: PAGE_URL.recommendToday
            }
        ];
        return menus.filter((item) => menuList.includes(item.pathname));
    }, [menuList]);

    // useEffect
    useEffect(() => {
        // 获取团队人员数据
        dispatch({ type: 'home/getRankListData' });

        return () => {
            dispatch({ type: 'home/resetState' });
        };
    }, []);

    return (
        <>
            <Loader maskOpacity={0.6} open={isLoading} />

            <div className={styles['home-page']}>
                <img alt='车商CRM' className={styles['home-banner']} src={LoginBanner} />
                <div className={styles['home-cont']}>
                    <div className={styles['home-link']}>
                        {menuListData.map((item) => (
                            <Link key={item.pathname} to={item.pathname}>
                                <div className={item.className}>
                                    <Icon className='f-mr15' type={item.icon[0]} />
                                    <p>{item.label}</p>
                                </div>
                            </Link>
                        ))}
                    </div>
                    {offlineDealerAdmin == 1 && <RankTable rankList={rankList} />}
                </div>
            </div>
        </>
    );
});

// export default connect((state) => state.app)(HomePage);
export default connect(({ home, app, loading }) => ({ home, app, loading }))(HomePage);
