@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';

.home-cont-loading {
    position: relative;
    width: 100%;
    height: calc(100vh - 220px);
}

.home-page {
    background-color: @color-white;
    position: relative;
    min-height: 100vh;

    .home-cont {
        background: linear-gradient(180deg, #ffffff 0%, #f6f6f6 100%);
        border-top-right-radius: 16 / @rem;
        border-top-left-radius: 16 / @rem;
        overflow: hidden;
        width: 100%;
        margin-top: -15 / @rem;
        position: relative;
        z-index: 10;
        min-height: 100vh;
    }

    .home-link {
        padding: (15 / @rem) (35 / @rem);
        display: flex;
        justify-content: space-between;
    }

    .home-banner {
        max-width: 100%;
        max-height: 100%;
        width: auto;
        display: block;
    }

    .home-title {
        font-size: 30 / @rem;
        color: @color-gray-6;
        padding-top: 100 / @rem;
        text-align: center;
    }

    .menu-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        // width: 158 / @rem;
        height: 80 / @rem;
        // border-radius: 8 / @rem;
        // box-sizing: border-box;
        // padding: (10 / @rem) 0;
        // font-weight: 600;
        font-size: 16 / @rem;
    }
}
