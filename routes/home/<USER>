/**
 * @file     登录页API
 * @module   api/login
 * <AUTHOR>
 * @see      API接口文档{@link http://confluence.ttpai.cn/pages/viewpage.action?pageId=6769984}
 */

/* requires JS */
import { getRequest, postRequest } from 'common/js/fetch.js';
import { PORT_URL } from 'common/js/constant.js';
import { isEmpty } from 'ttp-utils';

/**
 * 保存签到信息，POST请求
 * @see      API接口文档{@link http://confluence.ttpai.cn/pages/viewpage.action?pageId=6770358}
 * <AUTHOR>
 * @param    {object}                 data          请求参数对象
 * @param    {string}                 data.username 用户名（必填）
 * @param    {string}                 data.password 密码（必填）
 * @param    {string}                 data.qywxId   BOSS用户企业微信的ID（必填）
 * @returns  {Promise}                              返回一个Feach的Promise对象
 */
export function userLogin(data) {
    let opts = {
        url: PORT_URL.login,
        data
    };
    return postRequest(opts);
}

/**
 * 保存签到信息，POST请求
 * @see      API接口文档{@link http://confluence.ttpai.cn/pages/viewpage.action?pageId=6770358}
 * <AUTHOR>
 * @param    {object}                 data          请求参数对象
 * @returns  {Promise}                              返回一个Feach的Promise对象
 */
export function checkUserLogin(data) {
    let opts = {
        url: PORT_URL.loginInfo,
        data
    };
    return postRequest(opts)
        .then((res) => {
            console.log('checkUserLogin', res);
            if (isEmpty(res?.result?.realName)) {
                return false;
            }
            return true;
        })
        .catch(() => {
            return false;
        });
}

/**
 * 通过code换取登录信息
 * @see      API接口文档{@link http://confluence.ttpai.cn/pages/viewpage.action?pageId=6770358}
 * <AUTHOR>
 * @param    {object}                 data          请求参数对象
 * @param    {string}                 data.code     企业微信Code
 * @returns  {Promise}                              返回一个Feach的Promise对象
 */
export function exchangeCode(data) {
    let opts = {
        url: PORT_URL.exchangeCode,
        data
    };
    return postRequest(opts);
}

/**
 * 是否必须企微登录
 * @returns  {Promise}                              返回一个Feach的Promise对象
 */
export function mustWeChat() {
    let opts = {
        url: PORT_URL.mustWeChat
    };
    return getRequest(opts);
}

/**
 * 查询团队kpi 提车量总览数据
 *
 * @param   {object}         data 请求参数
 * @returns {Promise}             返回请求结果
 */
export function getRankList(data) {
    let opts = {
        url: PORT_URL.rankList,
        data
    };
    return getRequest(opts);
}
