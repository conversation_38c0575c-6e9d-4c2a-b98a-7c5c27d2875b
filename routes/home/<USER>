/**
 * 我的团队-绩效面板
 */
import * as api from './api.js';
/**
 * 默认参数
 * @returns {object}                     默认参数
 */
const initState = () => ({
    // 排名数据
    rankList: [],
    // 是否是线下商拓总监 1 是 ，0不是
    offlineDealerAdmin: 0
});

export default {
    namespace: 'home',

    state: initState(),

    reducers: {
        /**
         * 重置状态
         * @returns {object}                     更新过的state
         */
        resetState() {
            return initState();
        },

        /**
         * 更新状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateState(state, action) {
            return {
                ...state,
                ...action.payload
            };
        }
    },

    effects: {
        /**
         * 查询团队kpi 提车量总览数据
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *getRankListData({ payload }, { call, put }) {
            const { result = {} } = yield call(api.getRankList, payload);
            const { offlineDealerAdmin, deliveryVolumeList = [] } = result;

            console.log('getRankListData', result);
            yield put({
                type: 'updateState',
                payload: {
                    offlineDealerAdmin,
                    // 排名数据
                    rankList: deliveryVolumeList
                }
            });
        }
    }
};
