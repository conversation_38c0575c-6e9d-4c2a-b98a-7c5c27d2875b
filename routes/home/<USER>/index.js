import React, { memo } from 'react';
import styles from './index.less';
import RankBanner from 'src/images/rank-banner.png';
import { Icon, Empty } from 'ttp-library';
// img
import NoLog from 'src/images/no-log.png';

const RankTable = memo((props) => {
    const { rankList } = props;
    return (
        <div>
            <div className={styles['rank']}>
                <img alt='提车量排行榜' className={styles['rank-banner']} src={RankBanner} />
                <div className={styles['rank-cont']}>
                    {rankList.length > 0 ? (
                        <table className={styles['rank-table']}>
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>城市</th>
                                    <th>完成率</th>
                                </tr>
                            </thead>
                            <tbody>
                                {rankList.map((item, index) => (
                                    <tr key={item.kpiItemId + item.orgId + item.orgName}>
                                        {index <= 2 && (
                                            <td>
                                                <Icon type={`icon-rank-${index + 1}`} />
                                            </td>
                                        )}
                                        {index > 2 && <td>{index + 1}</td>}
                                        <td className={index > 2 ? styles['title'] : styles['title-bold']}>
                                            {item.orgName}
                                        </td>
                                        <td className={index > 2 ? styles['title'] : styles['title-bold']}>
                                            {item.achievementRateText}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    ) : (
                        <Empty
                            className={styles['no-item-data']}
                            description='暂未排名信息。'
                            image={NoLog}
                        />
                    )}
                </div>
            </div>
        </div>
    );
});

export default RankTable;
