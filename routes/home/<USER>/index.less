@import '../../../common/less/validation.less';
@import '../../../common/less/mixins.less';

.rank {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 345 / @rem;
    margin-left: 15 / @rem;
    flex: 1;
    margin-bottom: 30 / @rem;

    .rank-cont {
        background-color: @color-white;
        border-radius: 16 / @rem;
        overflow: hidden;
        width: 100%;
        position: relative;
        margin-top: -15 / @rem;
        z-index: 10;
        padding-top: 4 / @rem;
        padding-bottom: 10 / @rem;
    }

    &-table {
        overflow: hidden;
        border-collapse: collapse;
        justify-content: space-between;
        width: 100%;
        & > thead > tr {
            height: 34 / @rem;

            & > th {
                color: @color-gray-9;
                font-size: 14 / @rem;
                font-weight: bold;
                text-align: center;
            }
        }

        & > tbody > tr {
            background-color: #fff;
            height: 40 / @rem;

            .title {
                color: @color-gray-3;
                font-size: 14 / @rem;
                text-align: center;

                &-bold {
                    color: @color-gray-3;
                    font-weight: bold;
                    font-size: 14 / @rem;
                    text-align: center;
                }
            }

            & > td {
                text-align: center;

                &:nth-child(1) {
                    color: @color-gray-6;
                    font-weight: 600;
                    font-size: 20 / @rem;
                }
            }
        }
    }
}
