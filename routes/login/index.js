/* JS */
import React, { memo, useState } from 'react';
import { connect } from 'dva';
import { PAGE_URL } from 'routes/router-config.js';
import { useRequest } from 'ahooks';
import { isEmpty } from 'ttp-utils';
import { userLogin } from './api';
// 组件
import { message, Button, Input, Loader } from 'ttp-library';
/* LESS */
import styles from './index.less';
// 图片
import LoginBanner from 'src/images/banner-login.png';

/**
 * Login 登录组件
 *
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const LoginPage = memo((props) => {
    // props
    const { dispatch, history, location, qywxId } = props;

    // state
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');

    // 登录
    const { loading, runAsync } = useRequest(userLogin, {
        manual: true,
        debounceWait: 300
    });

    /**
     * 账号框的chang事件
     * @param   {event}                    e 事件对象
     */
    const handleUsernameChange = (e) => {
        setUsername(e.target.value.trim());
    };

    /**
     * 密码框的chang事件
     * @param   {event}                    e 事件对象
     */
    const handlePasswordChange = (e) => {
        setPassword(e.target.value.trim());
    };

    /**
     * 用户登录
     * @returns {Promise}                    Promise
     */
    const handleSubmit = () => {
        if (isEmpty(username) || isEmpty(password)) {
            message('账号或密码不正确，请重新输入！');
            return;
        }
        // 用户登录API
        return runAsync({ username, password, companyWeChatUserId: qywxId })
            .then((res) => {
                console.log('handleSubmit-ok', res.result);
                dispatch({ type: 'app/loginSuccess', payload: { isLogin: true, ...res.result } }).then(() => {
                    const defaultLocationState =
                        'history' in window ? history.state && history.state.state : {};
                    const { from = PAGE_URL.home, search = '' } =
                        location.state || defaultLocationState || {};
                    const config = { pathname: from, search };
                    return history.push(config);
                });
            })
            .catch((error) => {
                console.error('handleSubmit', error);
                message(error.message, 3);
            });
    };

    return (
        <>
            <Loader open={loading} type='tips' />
            <div className={styles['login-page']}>
                <img alt='车商CRM' className={styles['login-banner']} src={LoginBanner} />
                <div className={styles['login-cont']}>
                    <Input
                        className={styles['mb15']}
                        maxLength={20}
                        onChange={handleUsernameChange}
                        placeholder='请输入账号'
                        prefix={<i className='icon icon-person' />}
                        value={username}
                    />
                    <Input
                        className={styles['mb60']}
                        maxLength={20}
                        onChange={handlePasswordChange}
                        placeholder='请输入密码'
                        prefix={<i className='icon icon-vcode' />}
                        type='password'
                        value={password}
                    />
                    <Button block className={styles['btn']} onClick={handleSubmit} skin='blue'>
                        登录
                    </Button>
                    <p className={styles['notice']}>
                        <i className='icon icon-notice' />
                        忘记或重置密码请找运维人员
                    </p>
                </div>
            </div>
        </>
    );
});

export default connect((state) => state.app)(LoginPage);
