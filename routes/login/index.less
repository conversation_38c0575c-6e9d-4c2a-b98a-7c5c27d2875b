@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';

.login-cont-loading {
    position: relative;
    width: 100%;
    height: calc(100vh - 220px);
}

.login-page {
    background-color: @color-white;
    position: relative;
    height: 100vh;

    .login-cont {
        background-color: @color-white;
        padding: (30 / @rem) (16 / @rem);
        border-radius: 16 / @rem;
        overflow: hidden;
        width: 100%;
        position: absolute;
        top: 64 / @rem;
    }

    .login-banner {
        max-width: 100%;
        max-height: 100%;
        width: auto;
        display: block;
    }

    .login-title {
        font-size: 30 / @rem;
        color: @color-gray-6;
        padding-top: 100 / @rem;
        text-align: center;
    }

    .mb15 {
        margin-bottom: 15 / @rem;
    }

    .mb60 {
        margin-bottom: 60 / @rem;
    }

    :global {
        .Saitama-input-wrapper {
            background: #fafafa;
            border-radius: 4 / @rem;
        }

        .Saitama-input {
            background: #fafafa;
            border-radius: 0 (4 / @rem) (4 / @rem) 0;
        }
    }

    .btn {
        appearance: none;
        -webkit-appearance: none;
        font-size: 18 / @rem;
    }

    .notice {
        font-size: 13 / @rem;
        color: @color-gray-6;
        letter-spacing: 0;
        margin-top: 15 / @rem;
        text-align: center;

        :global {
            .icon-notice {
                margin-right: 4 / @rem;
                margin-top: -3 / @rem;
            }
        }
    }
}
