/**
 * @file 我的团队-绩效任务
 */

/* requires JS */
import { postRequest, getRequest } from 'common/js/fetch.js';
import { PORT_URL } from 'common/js/constant.js';

/**
 * 获取团队KPI
 * <AUTHOR>
 * @param    {object}                 data 请求参数对象
 * @returns  {Promise}                     返回一个Feach的Promise对象
 */
export function getGroupKpi(data) {
    let opts = {
        url: PORT_URL.groupKpi,
        data
    };
    return getRequest(opts);
}

/**
 * 按员工查询
 * <AUTHOR>
 * @param    {object}                 data 请求参数对象
 * @returns  {Promise}                     返回一个Feach的Promise对象
 */
export function getTroupKpiByUser(data) {
    console.log('api-getTroupKpiByUser', data);
    const { sort, worker, teamId } = data;
    let opts = {
        url: PORT_URL.groupKpiByUser,
        data: {
            teamId,
            filterMemberIds: (worker?.value || []).join(','),
            sortRule: sort?.value
        }
    };
    return getRequest(opts);
}

/**
 * 按绩效查询
 * <AUTHOR>
 * @param    {object}                 data        请求参数对象
 * @param    {object}                 data.sort   请求参数对象
 * @param    {object}                 data.worker 请求参数对象
 * @returns  {Promise}                             返回一个Feach的Promise对象
 */
export function getGroupKpiByKpi(data) {
    const { sort, worker, teamId } = data;
    console.log('api-getGroupKpiByKpi', data);
    let opts = {
        url: PORT_URL.groupKpiByKpi,
        data: {
            teamId,
            filterMemberIds: (worker?.value || []).join(','),
            sortRule: sort?.value
        }
    };
    return getRequest(opts);
}

/**
 * 保存：批量导入绩效
 * <AUTHOR>
 * @param    {object}                 data        请求参数对象
 * @returns  {Promise}                             返回一个Feach的Promise对象
 */
export function saveKpiList(data) {
    const formData = new FormData();
    Object.entries(data).forEach(([key, value]) => {
        formData.append(key, value);
    });

    let opts = {
        url: PORT_URL.saveKpiList,
        data: formData,
        config: {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        }
    };
    return postRequest(opts);
}

/**
 * 保存：单个绩效
 * <AUTHOR>
 * @param    {object}                 data        请求参数对象
 * @returns  {Promise}                             返回一个Feach的Promise对象
 */
export function saveKpiItem(data) {
    let opts = {
        url: PORT_URL.saveKpiItem,
        data
    };
    return postRequest(opts);
}

/**
 * 修改：单个绩效
 * <AUTHOR>
 * @param    {object}                 data        请求参数对象
 * @returns  {Promise}                             返回一个Feach的Promise对象
 */
export function updateKpiItem(data) {
    let opts = {
        url: PORT_URL.updateKpiItem,
        data
    };
    return postRequest(opts);
}

/**
 * 获取单个商拓的KPI数据
 * <AUTHOR>
 * @param    {object}                 data        请求参数对象
 * @returns  {Promise}                             返回一个Feach的Promise对象
 */
export function getKpiItem(data) {
    let opts = {
        url: PORT_URL.getKpiItem,
        data
    };
    return getRequest(opts);
}

/**
 * 查询可制定的绩效枚举
 * <AUTHOR>
 * @param    {object}                 data        请求参数对象
 * @returns  {Promise}                             返回一个Feach的Promise对象
 */
export function getKpiMap(data) {
    console.log('api-getKpiMap', data);
    let opts = {
        url: PORT_URL.getKpiMapV2,
        data
    };
    return getRequest(opts).then((res) => {
        if (Array.isArray(res.result)) {
            return res.result.map((item) => {
                return {
                    ...item,
                    label: item.indicatorName,
                    value: item.indicatorCode
                };
            });
        }
    });
}

/**
 * 获取团队数据
 *
 * @param   {object}               data 请求参数
 * @returns {Array}                     团队数据
 */
export const getTeamList = (data) => {
    const opts = {
        url: PORT_URL.teamList,
        data
    };
    return getRequest(opts)
        .then((res) => {
            if (Array.isArray(res?.result)) {
                return res.result.map((item) => ({ ...item, label: item.teamName, value: item.teamId }));
            }
            return [];
        })
        .catch(() => []);
};
