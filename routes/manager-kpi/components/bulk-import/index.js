// 绩效批量导入
// JS
import React, { memo, useRef, useState } from 'react';
import { connect } from 'dva';
import { useSetState, useToggle } from 'ahooks';
import { executeCB, formatTime, isEmpty, getUrlQueryStringify } from 'ttp-utils';
import { setObjectToArray } from 'common/js/utils.js';
import ValidationFormData, { builkImputMap } from 'common/js/validation.js';
// 组件
import { Drawer, Icon, Button, List, DatePicker, confirm } from 'ttp-library';
import FormItemErrorTip from 'components/form-item-error-tip/index.js';
import DrawerDown from 'components/drawer-down/index.js';
// LESS
import styles from './index.less';

const nowTimeStamp = formatTime(new Date(), 'yyyy-mm');
const now = new Date(nowTimeStamp);

/**
 * 业务类型
 * @type {object}
 */
const BUSINESS_TYPE_MAP = {
    common: '商户拓展',
    online: '线上商拓',
    offline: '线下商拓'
};

/**
 * 业务类型数据
 * @type {Array}
 */
const BUSINESS_TYPE_OPTION = setObjectToArray(BUSINESS_TYPE_MAP);

/**
 * 错误提示数据
 * @returns {object}                          errorData
 */
const defErrorData = () => ({
    timeInterval: '',
    file: ''
});

/**
 * 绩效批量导入
 * @param   {object}                     props props
 * @returns {React.ReactElement}               jsx
 */
const BulkImport = memo((props) => {
    // props
    const { dispatch, loading, open, onClose } = props;
    const saveLoading = loading.effects['managerKpi/saveKpiListData'];
    // state
    const [timeInterval, setTimeInterval] = useState(now);
    const [businessType, setBusinessType] = useState('');
    const [fileData, setFileData] = useState('');
    // 打开绩效指标弹窗
    const [openType, { setLeft: setTypeClose, setRight: setTypeOpen }] = useToggle(false);
    // 错误展示
    const [errorData, setErrorData] = useSetState(defErrorData());
    // ref
    const uploadFileRef = useRef(null);
    // state
    // 计算属性

    /**
     * 时间选择器 chaneg
     * @param   {object}                     data 时间
     */
    const handleDatePickerChaneg = (data) => {
        console.log('handleDatePickerChaneg', data);
        setTimeInterval(data);
    };

    /**
     * 选择文件点击事件
     */
    const handleSelectFile = () => {
        uploadFileRef.current.click();
    };

    /**
     * 文件选择事件
     * @param   {event}                     e event
     */
    const handleFileChange = (e) => {
        const { files } = e.target;
        if (files.length) {
            setFileData(files[0]);
        }
    };

    /**
     * 重置文件
     */
    const handleResetFile = () => {
        setFileData('');
    };

    /**
     * 关闭窗口
     */
    const handleDrawerClose = () => {
        executeCB(onClose, 'close');
        setTimeInterval(now);
        setFileData('');
    };

    /**
     * 提交
     */
    const handleSubmit = () => {
        const data = {
            timeInterval: timeInterval && formatTime(timeInterval, 'yyyy-mm'),
            businessType,
            file: fileData
        };
        const errorMsg = ValidationFormData(data, builkImputMap);
        console.log('handleSubmit-data', JSON.stringify(data));
        console.log('handleSubmit-errorMsg', JSON.stringify(errorMsg));
        if (errorMsg) {
            setErrorData(errorMsg);
            return;
        }
        setErrorData(defErrorData());
        dispatch({
            type: 'managerKpi/saveKpiListData',
            payload: data
        }).then(({ result = {} }) => {
            const { errorList = [], successList = [] } = result || {};
            confirm({
                title: '批量上传完成',
                content: (
                    <div className={styles['bulk-import-confirm-content']}>
                        <p>
                            <strong>{`上传成功：${successList.length}条，上传失败：${errorList.length}条。`}</strong>
                        </p>
                        {errorList.length > 0 && (
                            <dl className={styles['bulk-import-confirm-error']}>
                                <dt>
                                    <strong>失败数据如下：</strong>
                                </dt>
                                {errorList.map((item, index) => (
                                    <dd key={index}>
                                        {index + 1}、{item.dealerAdminName || '--'}(
                                        {item.dealerAdminId || '--'})：
                                        {item.errorMessage}
                                    </dd>
                                ))}
                            </dl>
                        )}
                    </div>
                )
            });
            executeCB(onClose, 'success');
            setTimeInterval('');
            setFileData('');
        });
    };

    /**
     * 业务类型：Change
     * @param   {string}                     value value
     */
    const handleTypeChange = (value) => {
        console.log('handleTypeChange', value);
        setBusinessType(value);
        setTypeClose();
    };

    /**
     * 模版下载
     */
    const handleDownload = () => {
        const url = getUrlQueryStringify({ businessType }, '/api/kpi/export/template');
        window.open(url);
    };

    // 组件
    const leftNode = (
        <div className={styles['bulk-import-close']} onClick={handleDrawerClose}>
            <Icon className={styles['bulk-import-close-icon']} type='iconfont-close' />
        </div>
    );

    return (
        <>
            <Drawer
                className={styles['bulk-import']}
                hasHeader={true}
                left={leftNode}
                maskOpacity={0.2}
                mode='bottom'
                onClose={handleDrawerClose}
                open={open}
                title='批量导入绩效'>
                <div className={styles['bulk-import-body']}>
                    <List className={styles['list-box']}>
                        <List.Item
                            arrow='right'
                            className={styles['list-box-item']}
                            extra={
                                <DatePicker
                                    format='YYYY-MM'
                                    minDate={now}
                                    mode='month'
                                    onChange={handleDatePickerChaneg}
                                    value={timeInterval}>
                                    <input
                                        className={styles['list-box-input']}
                                        name='month'
                                        placeholder='请选择绩效时间'
                                        readOnly
                                    />
                                </DatePicker>
                            }>
                            <span>
                                <b className={styles['red-text']}>*</b> 绩效时间
                            </span>
                        </List.Item>
                        <FormItemErrorTip
                            border={true}
                            classname={styles['list-box-error']}
                            message={errorData.timeInterval}
                        />
                        <List.Item
                            arrow='right'
                            className={styles['list-box-item']}
                            extra={
                                isEmpty(BUSINESS_TYPE_MAP[businessType]) ? (
                                    <span className={styles['select-text']}>请选业务团队</span>
                                ) : (
                                    BUSINESS_TYPE_MAP[businessType]
                                )
                            }
                            onClick={setTypeOpen}>
                            <span>
                                <b className={styles['red-text']}>*</b> 我的团队
                            </span>
                        </List.Item>
                        {!isEmpty(businessType) && (
                            <List.Item
                                arrow='right'
                                className={styles['list-box-item']}
                                extra={
                                    <Button size='sm' skin='white'>
                                        模版下载
                                    </Button>
                                }
                                key={businessType}
                                onClick={handleDownload}>
                                导入模版
                            </List.Item>
                        )}
                        <FormItemErrorTip
                            border={true}
                            classname={styles['list-box-error']}
                            message={errorData.businessType}
                        />
                    </List>

                    {isEmpty(fileData) ? (
                        <>
                            <div className={styles['upload-file']} onClick={handleSelectFile}>
                                <Icon className='f-mb10' key='upload-file' type='icon-upload-file' />
                                <p className={styles['upload-file-text']}>去导入</p>
                                <input
                                    accept='.xls,.xlsx'
                                    className={styles['upload-file-input']}
                                    multiple={false}
                                    onChange={handleFileChange}
                                    ref={uploadFileRef}
                                    type='file'
                                    value={fileData}
                                />
                            </div>
                            <FormItemErrorTip classname={styles['list-box-error']} message={errorData.file} />
                        </>
                    ) : (
                        <div className={styles['upload-file']}>
                            <Icon className='f-mb10' key='upload-folder' type='icon-folder' />
                            <p>{fileData.name}</p>
                            <Button mode='link' onClick={handleResetFile} skin='blue'>
                                重新选择
                            </Button>
                        </div>
                    )}
                </div>
                <div className={styles['bulk-import-footer']}>
                    <Button
                        block
                        className={styles['footer-btn']}
                        loading={saveLoading}
                        onClick={handleDrawerClose}
                        skin='white'>
                        取消
                    </Button>
                    <Button
                        block
                        className={styles['footer-btn']}
                        loading={saveLoading}
                        onClick={handleSubmit}>
                        确定
                    </Button>
                </div>
            </Drawer>
            {/* 业务类型 */}
            <DrawerDown
                dataList={BUSINESS_TYPE_OPTION}
                onChange={handleTypeChange}
                onClose={setTypeClose}
                open={openType}
                title={'选择绩效指标'}
                value={businessType}
            />
        </>
    );
});

export default connect(({ managerKpi, loading }) => ({ ...managerKpi, loading }))(BulkImport);
