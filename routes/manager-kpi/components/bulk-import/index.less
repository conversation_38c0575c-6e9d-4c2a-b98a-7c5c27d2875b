@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';
@import '../../../../common/less/list.less';

.bulk-import {
    :global {
        .Saitama-drawer-box {
            border-top-left-radius: 8 / @rem;
            border-top-right-radius: 8 / @rem;
        }

        .Saitama-header {
            position: relative;

            &:after {
                .setBottomLine(@color-border);
            }
        }

        .Saitama-list-item {
            &:after {
                height: 0;
                border-width: 0;
            }
        }
    }

    &-close {
        width: 20 / @rem;
        height: 20 / @rem;
        text-align: center;
        line-height: 20 / @rem;

        &-icon {
            font-size: 14 / @rem !important;
        }
    }

    &-body {
        padding: 0 (10 / @rem);
        max-height: 70vh;
        overflow-y: auto;

        .audio-box {
            margin-bottom: 15 / @rem;
            width: 100%;

            &-item {
                width: 100%;
            }
        }
    }

    .list-box {
        margin: 0;

        &-item {
            padding: 0 (5 / @rem);
            min-height: 40 / @rem;
            overflow: inherit;

            &-title {
                color: @color-blue;
            }
        }

        &-input {
            text-align: right;
            border-width: 0;
            padding: 0;
            line-height: 40 / @rem;
            width: 150 / @rem;
        }

        &-error {
            position: relative;
            left: 0;
            bottom: 0;
        }
    }

    .red-text {
        color: @color-red;
    }

    .select-text {
        color: @color-gray-9;
        font-weight: 400;
    }

    .upload-file {
        display: flex;
        flex-direction: column;
        justify-items: center;
        align-items: center;
        margin-top: 10 / @rem;
        position: relative;
        min-height: 110 / @rem;

        &-text {
            text-align: center;
            color: @color-blue;
        }

        &-input {
            visibility: hidden;
            width: 100%;
            height: 110 / @rem;
            position: absolute;
            left: 0;
            top: 0;
            z-index: 1;
        }
    }

    &-footer {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: (10 / @rem) (20 / @rem);
        position: relative;

        &:after {
            .setTopLine(@color-border);
        }

        .footer-btn {
            width: 158 / @rem;
            height: 44 / @rem;
        }
    }
}

// confirm 弹窗内容
.bulk-import-confirm {
    &-content {
        text-align: left;
        line-height: 1.7;
    }

    &-error {
        color: @color-red;
        line-height: 1.7;
    }
}
