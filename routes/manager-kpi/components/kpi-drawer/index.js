// 绩效批量导入
// JS
import React, { memo, useEffect, useState } from 'react';
import { connect } from 'dva';
import cls from 'classnames';
import { useToggle, useRequest, useSetState } from 'ahooks';
import { executeCB, formatTime, isEmpty } from 'ttp-utils';
import { getKpiMap } from '../../api.js';
import ValidationFormData, { KpiDrawerMap } from 'common/js/validation.js';
// 组件
import { Drawer, Icon, Button, List, DatePicker, message, Input, Loader } from 'ttp-library';
import WorkerDrawer from 'components/worker-drawer/index.js';
import DrawerDown from 'components/drawer-down/index.js';
import FormItemErrorTip from 'components/form-item-error-tip';
// LESS
import styles from './index.less';

const nowTimeStamp = formatTime(new Date(), 'yyyy-mm');
const now = new Date(nowTimeStamp);

/**
 * 指标类型
 * 1:数字指标，2:百分比指标
 * @type {object}
 */
const IndicatorTypesMap = {
    1: 'number',
    2: 'percent'
};

/**
 * 错误提示数据
 * @returns {object}                          errorData
 */
const defErrorData = () => ({
    // 成员id
    memberId: '',
    // 绩效时间
    kpiTime: '',
    // 指标编码
    indicatorCode: '',
    // 目标值
    targetValue: '',
    // 目标值权重
    targetValueWeight: ''
});

/**
 * 绩效批量导入
 * @param   {object}                     props props
 * @returns {React.ReactElement}               jsx
 */
const KpiDrawer = memo((props) => {
    // props
    const { dispatch, loading, open, id, onClose, kpiFormData, teamId } = props;
    const kpiLoading = loading.effects['managerKpi/getKpiItemData'];
    const saveLoading = loading.effects['managerKpi/saveKpiItemData'];
    const updateLoading = loading.effects['managerKpi/updateKpiItemData'];
    const pageLoading = Boolean(kpiLoading || saveLoading || updateLoading);
    // state
    // 弹窗类型：新增，编辑
    const [type, setType] = useState(isEmpty(id) ? 'add' : 'edit');
    // 打开人员查询弹窗
    const [openWorker, { setLeft: setWorkerClose, setRight: setWorkerOpen }] = useToggle(false);
    // 打开绩效指标弹窗
    const [openIndicatorCode, { setLeft: setIndicatorCodeClose, setRight: setIndicatorCodeOpen }] =
        useToggle(false);
    // 绩效指标数据
    // 绩效指标数据
    const {
        data: indicatorCodeOptions,
        loading: indictorLoading,
        runAsync: runAsyncKpiMap
    } = useRequest(getKpiMap, {
        manual: true,
        defaultValue: [],
        // eslint-disable-next-line jsdoc/require-jsdoc
        onError: (error) => {
            throw new Error(error);
        }
    });
    // 错误展示
    const [errorData, setErrorData] = useSetState(defErrorData());
    // 有input行的样式
    const InputItemClass = cls(styles['list-box-item'], {
        [styles['list-box-item-bold']]:
            !isEmpty(kpiFormData.kpiTime) ||
            !isEmpty(kpiFormData.targetValue) ||
            !isEmpty(kpiFormData.targetValueWeight)
    });
    const editClass = cls(InputItemClass, { [styles['list-box-item-edit']]: type === 'edit' });
    const targetValuePlaceholder =
        IndicatorTypesMap[kpiFormData.indicatorType] == 'percent' ? '填入1-100的整数' : '填入1-99999的整数';
    const targetValueSuffix = IndicatorTypesMap[kpiFormData.indicatorType] == 'percent' ? '%' : ' ';
    const targetValueMaxLength = IndicatorTypesMap[kpiFormData.indicatorType] == 'percent' ? 3 : 5;
    // 必填*的展示
    const requiredStyle = cls(styles['red-text'], { [styles['hide-required']]: type === 'edit' });
    const title = type === 'add' ? '新增绩效' : '编辑绩效';

    // 计算属性

    // 函数
    /**
     * 关闭窗口
     */
    const handleDrawerClose = () => {
        dispatch({ type: 'managerKpi/resetKpiState' });
        setErrorData(defErrorData());
        executeCB(onClose, 'close');
    };

    /**
     * 打开员工查询弹窗
     */
    const handleWorkerOpen = () => {
        if (type === 'add') {
            setWorkerOpen();
        } else {
            setWorkerClose();
        }
    };

    /**
     * 员工选择：Change
     * @param   {object}                     checkedData data
     * @param   {object}                     rowData     行数据
     */
    const handleMemberChange = (checkedData, rowData) => {
        console.log('handleOk', checkedData, rowData);
        dispatch({
            type: 'managerKpi/updateKpiState',
            payload: { memberId: checkedData, memberName: rowData.label }
        });
    };

    /**
     * 时间选择器 chaneg
     * @param   {object}                     data 时间
     */
    const handleDatePickerChaneg = (data) => {
        console.log('handleDatePickerChaneg', data, formatTime(data, 'yyyy-mm'));
        dispatch({
            type: 'managerKpi/updateKpiState',
            payload: { kpiTime: data }
        });
    };

    /**
     * 打开绩效指标弹窗
     */
    const handleIndicatorCodeOpen = () => {
        if (type === 'add') {
            // if (isEmpty(kpiFormData.memberId)) {
            //     message('请先选择考核对象');
            //     return;
            // }
            setIndicatorCodeOpen();
        } else {
            setIndicatorCodeClose();
        }
    };

    /**
     * KPI绩效指标选择：Change
     * @param   {string}                     value value
     * @param   {object}                     item  行数据
     */
    const handleIndicatorCodeChange = (value, item) => {
        console.log('handleIndicatorCodeChange', value, item);
        dispatch({
            type: 'managerKpi/updateKpiState',
            payload: { indicatorCode: value, indicatorName: item.label, indicatorType: item.indicatorType }
        });
        setIndicatorCodeClose();
    };
    /**
     * KPI目标值
     * @param   {event}                     e event
     */
    const handleTargetValueChaneg = (e) => {
        dispatch({
            type: 'managerKpi/updateKpiState',
            payload: { targetValue: e.target.value }
        });
    };

    /**
     * KPI目标值权重
     * @param   {event}                     e event
     */
    const handletargetValueWeightChaneg = (e) => {
        dispatch({
            type: 'managerKpi/updateKpiState',
            payload: { targetValueWeight: e.target.value }
        });
    };

    /**
     * 提交
     */
    const handleSubmit = () => {
        const errorMsg = ValidationFormData(kpiFormData, KpiDrawerMap);
        if (errorMsg) {
            setErrorData(errorMsg);
            return;
        }
        setErrorData(defErrorData());
        console.log('handleSubmit-type', type);
        const updateId = type == 'add' ? {} : { id };
        // 请求
        dispatch({
            type: type == 'add' ? 'managerKpi/saveKpiItemData' : 'managerKpi/updateKpiItemData',
            payload: {
                ...kpiFormData,
                kpiTime: formatTime(kpiFormData.kpiTime, 'yyyy-mm'),
                ...updateId
            }
        }).then((res) => {
            message(res.message || '绩效保存成功');
            dispatch({ type: 'managerKpi/resetKpiState' });
            executeCB(onClose, 'success');
        });
    };

    // 组件
    const leftNode = (
        <div className={styles['kpi-drawer-close']} onClick={handleDrawerClose}>
            <Icon className={styles['kpi-drawer-close-icon']} type='iconfont-close' />
        </div>
    );

    useEffect(() => {
        // 获取绩效指标数据
        if (!isEmpty(teamId)) {
            runAsyncKpiMap({ teamId });
        }
    }, [teamId]);
    // useEffect
    useEffect(() => {
        console.log('KpiDrawer-useEffect', open, id);
        setType(isEmpty(id) ? 'add' : 'edit');
        if (open && !isEmpty(id)) {
            dispatch({
                type: 'managerKpi/getKpiItemData',
                payload: { id }
            });
        }
    }, [id, open]);

    return (
        <>
            <Drawer
                className={styles['kpi-drawer']}
                hasHeader={true}
                left={leftNode}
                maskOpacity={0.2}
                mode='bottom'
                onClose={handleDrawerClose}
                open={open}
                title={title}>
                <Loader maskOpacity={0.6} open={pageLoading} type='page' />
                <div className={styles['kpi-drawer-body']}>
                    <List className={styles['list-box']}>
                        <List.Item
                            arrow='right'
                            className={editClass}
                            extra={
                                isEmpty(kpiFormData.memberName) ? (
                                    <span className={styles['select-text']}>请选择一名员工</span>
                                ) : (
                                    kpiFormData.memberName
                                )
                            }
                            onClick={handleWorkerOpen}>
                            <span>
                                <b className={requiredStyle}>*</b> 员工
                            </span>
                        </List.Item>
                        <FormItemErrorTip
                            border={true}
                            classname={styles['list-box-error']}
                            message={errorData.memberId}
                        />

                        <List.Item
                            arrow='right'
                            className={editClass}
                            extra={
                                type === 'add' ? (
                                    <DatePicker
                                        format='YYYY-MM'
                                        minDate={now}
                                        mode='month'
                                        onChange={handleDatePickerChaneg}
                                        value={kpiFormData.kpiTime}>
                                        <Input
                                            className={styles['list-box-input']}
                                            name='month'
                                            placeholder='请选择绩效时间'
                                            readOnly
                                        />
                                    </DatePicker>
                                ) : (
                                    kpiFormData.kpiTime
                                )
                            }>
                            <span>
                                <b className={requiredStyle}>*</b> 绩效时间
                            </span>
                        </List.Item>
                        <FormItemErrorTip
                            border={true}
                            classname={styles['list-box-error']}
                            message={errorData.kpiTime}
                        />

                        <List.Item
                            arrow='right'
                            className={editClass}
                            extra={
                                <div onClick={handleIndicatorCodeOpen}>
                                    {isEmpty(kpiFormData.indicatorName) ? (
                                        <span className={styles['select-text']}>请选择绩效指标</span>
                                    ) : (
                                        kpiFormData.indicatorName
                                    )}
                                </div>
                            }
                            key={kpiFormData.memberId}>
                            <span>
                                <b className={requiredStyle}>*</b> 绩效指标
                            </span>
                        </List.Item>
                        <FormItemErrorTip
                            border={true}
                            classname={styles['list-box-error']}
                            message={errorData.indicatorCode}
                        />
                        <List.Item
                            className={InputItemClass}
                            extra={
                                <Input
                                    className={styles['list-box-input']}
                                    formatType='number'
                                    key={kpiFormData.indicatorType}
                                    maxLength={targetValueMaxLength}
                                    name='targetValue'
                                    onChange={handleTargetValueChaneg}
                                    placeholder={targetValuePlaceholder}
                                    suffix={targetValueSuffix}
                                    value={kpiFormData.targetValue}
                                />
                            }>
                            <span>
                                <b className={styles['red-text']}>*</b> 目标值
                            </span>
                        </List.Item>
                        <FormItemErrorTip
                            border={true}
                            classname={styles['list-box-error']}
                            message={errorData.targetValue}
                        />

                        <List.Item
                            className={InputItemClass}
                            extra={
                                <Input
                                    className={styles['list-box-input']}
                                    formatType='number'
                                    maxLength='3'
                                    name='targetValueWeight'
                                    onChange={handletargetValueWeightChaneg}
                                    placeholder='填入1-100的整数'
                                    suffix='%'
                                    value={kpiFormData.targetValueWeight}
                                />
                            }>
                            <span>
                                <b className={styles['red-text']}>*</b> 目标权重
                            </span>
                        </List.Item>
                        <FormItemErrorTip
                            classname={styles['list-box-error']}
                            message={errorData.targetValueWeight}
                        />
                    </List>
                </div>
                <div className={styles['kpi-drawer-footer']}>
                    <Button
                        block
                        className={styles['footer-btn']}
                        loading={pageLoading}
                        onClick={handleDrawerClose}
                        skin='white'>
                        取消
                    </Button>
                    <Button
                        block
                        className={styles['footer-btn']}
                        loading={pageLoading}
                        onClick={handleSubmit}>
                        确定
                    </Button>
                </div>
            </Drawer>
            {/* 员工查询 */}
            <WorkerDrawer
                onChange={handleMemberChange}
                onClose={setWorkerClose}
                open={openWorker}
                teamId={teamId}
                value={kpiFormData.memberId}
            />
            {/* 绩效指标 */}
            <DrawerDown
                dataList={indicatorCodeOptions}
                loading={indictorLoading}
                onChange={handleIndicatorCodeChange}
                onClose={setIndicatorCodeClose}
                open={openIndicatorCode}
                title={'选择绩效指标'}
                value={kpiFormData.indicatorCode}
            />
        </>
    );
});

export default connect(({ managerKpi, loading }) => ({ ...managerKpi, loading }))(KpiDrawer);
