@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';
@import '../../../../common/less/list.less';

.kpi-drawer {
    :global {
        .Saitama-drawer-box {
            border-top-left-radius: 8 / @rem;
            border-top-right-radius: 8 / @rem;
        }

        .Saitama-header {
            position: relative;

            &:after {
                .setBottomLine(@color-border);
            }
        }

        .Saitama-list-item {
            &:after {
                height: 0;
                border-width: 0;
            }
        }
    }

    &-close {
        width: 20 / @rem;
        height: 20 / @rem;
        text-align: center;
        line-height: 20 / @rem;

        &-icon {
            font-size: 14 / @rem !important;
        }
    }

    &-body {
        padding: 0 (10 / @rem);
        max-height: 70vh;
        min-height: 190 / @rem;
        overflow-y: auto;

        .audio-box {
            margin-bottom: 15 / @rem;
            width: 100%;

            &-item {
                width: 100%;
            }
        }
    }

    .list-box {
        margin: 0;

        &-item {
            padding: 0 (5 / @rem);
            min-height: 40 / @rem;
            overflow: inherit;

            &-title {
                color: @color-blue;
            }

            &-edit {
                :global {
                    .Saitama-list-item-arrow-right {
                        display: none;
                    }
                }
            }
        }

        &-error {
            position: relative;
            left: 0;
            bottom: 0;
        }

        &-input {
            input {
                text-align: right;
                border-width: 0;
                padding: 0;
                line-height: 40 / @rem;
                width: 240 / @rem;
            }

            :global {
                .Saitama-input-addon {
                    padding: 0;
                    width: 20 / @rem;
                    text-align: center;
                }
            }
        }

        &-item-bold {
            .list-box-input {
                input {
                    font-weight: bold;
                }
            }
        }
    }

    .select-text {
        color: @color-gray-9;
        font-weight: 400;
    }

    .red-text {
        color: @color-red;
    }

    .hide-required {
        display: none;
    }

    &-footer {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: (10 / @rem) (20 / @rem);
        position: relative;

        &:after {
            .setTopLine(@color-border);
        }

        .footer-btn {
            width: 158 / @rem;
            height: 44 / @rem;
        }
    }
}
