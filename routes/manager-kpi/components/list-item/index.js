// JS
import React, { memo } from 'react';
import { useToggle } from 'ahooks';
import { executeCB, isEmpty } from 'ttp-utils';
import cls from 'classnames';
// 组件
import { List, Empty } from 'ttp-library';
import Panel from 'components/panel';
import ProgressBar from 'components/progress-bar';
// LESS
import styles from './index.less';
// img
import NoLog from 'src/images/no-log.png';

/**
 * 完成率
 * @param   {object}                     props props
 * @returns {React.ReactElement}               jsx
 */
const SubTitle = memo((props) => {
    // props
    const { progress } = props;
    const textClass = cls('f-mr5', styles['extra-item-text']);

    return (
        <div className={styles['sub-title']}>
            <span className={textClass}>完成率{isEmpty(progress) ? '0%' : progress}</span>
            <ProgressBar progress={progress} />
            <span className='f-ml5'>详情</span>
        </div>
    );
});

/**
 * 完成率
 * @param   {object}                     props props
 * @returns {React.ReactElement}               jsx
 */
const ExtraItem = memo((props) => {
    // props
    const { progress } = props;
    const textClass = cls('f-mr10', styles['extra-item-text']);

    return (
        <div className={styles['extra-item']}>
            <span className={textClass}>完成率</span>
            <ProgressBar progress={progress} />
            <strong className='f-ml10'>{isEmpty(progress) ? '0%' : progress}</strong>
        </div>
    );
});

/**
 * 按员工查询结果
 */
const WorkerItem = memo((props) => {
    // props
    const { itemData, onEdit } = props;
    const { wholeFinishRate, memberName, memberId, kpiList } = itemData;
    // state
    const [open, { toggle }] = useToggle(false);

    // 函数
    /**
     * 编辑
     * @param   {object}                     item  item
     */
    const handleEditClick = (item) => {
        executeCB(onEdit, { memberName, memberId, ...item });
    };

    return (
        <Panel
            classname={styles['list-item']}
            key='work'
            onClick={toggle}
            open={open}
            subtitle={<SubTitle progress={wholeFinishRate} />}
            title={<strong>{memberName}</strong>}>
            {Array.isArray(kpiList) && kpiList.length ? (
                kpiList.map((item, index) => (
                    <List className={styles['list-box']} key={item.indicatorCode}>
                        <List.Item className={styles['list-box-item']} extra={item.indicatorName}>
                            <span className={styles['blue-text']}>指标{index + 1}</span>
                        </List.Item>
                        <List.Item className={styles['list-box-item']} extra={item.targetValue || '--'}>
                            目标值
                        </List.Item>
                        <List.Item className={styles['list-box-item']} extra={item.targetValueWeight || '--'}>
                            目标权重
                        </List.Item>
                        <List.Item className={styles['list-box-item']} extra={item.achievementValue || '--'}>
                            完成进展
                        </List.Item>
                        <List.Item className={styles['list-box-item']} extra={item.achievementRate || '--'}>
                            完成率
                        </List.Item>
                        <List.Item
                            arrow='right'
                            className={styles['list-box-item']}
                            extra={<span className={styles['blue-text']}>修改指标</span>}
                            onClick={() => handleEditClick(item)}>
                            操作
                        </List.Item>
                    </List>
                ))
            ) : (
                <Empty className={styles['no-item-data']} description='尚未添加绩效指标。' image={NoLog} />
            )}
        </Panel>
    );
});

/*
 * 按绩效查询结果
 */
const KpiItem = memo((props) => {
    // props
    const { itemData } = props;
    const { indicatorName, wholeFinishRate, personalKpiList } = itemData;
    // state
    const [open, { toggle }] = useToggle(false);

    return (
        <Panel
            classname={styles['list-item']}
            key='kpi'
            onClick={toggle}
            open={open}
            subtitle={<SubTitle progress={wholeFinishRate} />}
            title={<strong className={styles['blue-text']}>{indicatorName}</strong>}>
            <List className={styles['list-box']}>
                {Array.isArray(personalKpiList) &&
                    personalKpiList.map((item) => (
                        <List.Item
                            className={styles['list-box-item']}
                            extra={<ExtraItem progress={item.achievementRate} />}
                            key={item.memberId}>
                            {item.memberName}
                        </List.Item>
                    ))}
            </List>
        </Panel>
    );
});

/**
 * 我的客户数据ITEM
 * @param   {object}                     props props
 * @returns {React.ReactElement}               jsx
 */
const ListItem = memo((props) => {
    // props
    const { type, itemData, onEdit } = props;

    /**
     * 查看跟进记录
     * @param   {object}                     item 数据对象
     */
    const handleEditClick = (item) => {
        executeCB(onEdit, item);
    };

    if (type == 'worker') {
        return <WorkerItem itemData={itemData} onEdit={handleEditClick} />;
    }

    if (type == 'kpi') {
        return <KpiItem itemData={itemData} />;
    }

    return null;
});

export default ListItem;
