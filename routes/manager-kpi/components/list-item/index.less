@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';
@import '../../../../common/less/list.less';

.list-item {
    margin-bottom: 10 / @rem;
    position: relative;

    :global {
        .white-box-title {
            font-size: 14 / @rem;
        }
    }

    &-title {
        display: flex;
        align-items: center;
    }

    .sub-title,
    .extra-item {
        display: flex;
        flex-direction: row;
        align-items: center;

        &-text {
            color: @color-gray-3;
            font-size: 12 / @rem;
            font-weight: 400;
        }
    }

    .no-item-data {
        padding: 0 0 (20 / @rem);

        :global {
            .Saitama-empty-image {
                max-width: 90 / @rem;
                max-height: 95 / @rem;
            }
        }
    }

    .list-box {
        margin: 0;
        position: relative;

        &:after {
            .setTopLine(@color-border);
        }

        &-item {
            padding: (10 / @rem) (5 / @rem);
            min-height: 40 / @rem;
            overflow: inherit;

            &-title {
                color: @color-blue;
            }

            &:after {
                .setBottomLine(@color-border);
            }

            &:last-child:after {
                border-bottom-width: 0px;
            }
        }
    }

    .blue-text {
        color: @color-blue;
    }
}
