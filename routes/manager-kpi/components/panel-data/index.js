// JS
import React, { memo } from 'react';
import { isEmpty } from 'ttp-utils';
import cls from 'classnames';
// 组件
import PanelItem from 'components/panel-item';
// LESS
import styles from './index.less';

/**
 * 绩效数据
 * @param   {object}                    props props
 * @returns {React.ReactElement}              jsx
 */
const PanelData = memo((props) => {
    // props
    const { classname, data, navHeight, title, children } = props;
    const { groupMemberNumber } = data || {};

    // 常量
    const panelDataClasss = cls(styles['panel-data'], classname);

    return (
        <div className={panelDataClasss} style={{ marginTop: navHeight }}>
            <h2 className={styles['panel-data-header']}>
                <span className={styles['title']}>
                    {title}
                    <span className={styles['sub-title']}>({groupMemberNumber || 0}人)</span>
                </span>
                <div className={styles['more']}>{!isEmpty(children) && children}</div>
            </h2>
            <div className={styles['panel-item-body']}>
                <PanelItem itemData={data} showDetailButton={false} />
            </div>
        </div>
    );
});

export default PanelData;
