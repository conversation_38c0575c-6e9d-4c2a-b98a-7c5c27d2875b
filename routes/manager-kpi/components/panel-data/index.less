@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';

.panel-data {
    padding: 0 (12 / @rem);
    background: linear-gradient(179deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);

    &-header {
        height: 44 / @rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        line-height: 44 / @rem;
        padding: 0 (2 / @rem);

        .title {
            color: @color-black;
            font-size: 16 / @rem;
            font-weight: bold;
            vertical-align: bottom;
        }

        .sub-title {
            padding-left: 10 / @rem;
            color: @color-gray-3;
            font-weight: lighter;
            font-size: 12 / @rem;
        }
    }

    .panel-item-body {
        padding: 0 (10 / @rem) (10 / @rem);
        background-color: white;
        border-radius: 6 / @rem;
    }

    .no-item-data {
        padding: 0 0 (20 / @rem);

        :global {
            .Saitama-empty-image {
                max-width: 90 / @rem;
                max-height: 95 / @rem;
            }
        }
    }

    &-body {
        display: grid;
        grid-template-columns: repeat(2, (170 / @rem));
        grid-row-gap: 12 / @rem;
        grid-column-gap: 10 / @rem;
    }

    &-item {
        width: 170 / @rem !important;
        box-shadow: 0px 2px 4px 0px #e3e5e8;
        padding: 0 / @rem;
        border-radius: 6 / @rem;

        &.one {
            :global {
                .white-box-header {
                    height: 31 / @rem;
                    background: linear-gradient(307deg, #f2fbff 0%, #e7f8ff 100%);
                    box-shadow: 0px 2px 4px 0px #f4f8ff;
                }
            }
        }

        &.two {
            :global {
                .white-box-header {
                    height: 31 / @rem;
                    background: linear-gradient(124deg, #fff8ef 0%, #fffef4 100%);
                    box-shadow: 0px 2px 4px 0px #f4f8ff;
                }
            }
        }

        :global {
            .white-box-title {
                font-size: 14 / @rem;
                font-weight: 400;
                color: @color-gray-3;
            }
        }

        &-body {
            height: 54 / @rem;
            padding: 5 / @rem;
            box-sizing: border-box;
            display: flex;
            justify-content: center;
            align-items: center;

            .item-left {
                width: 55 / @rem;
                text-align: center;
                font-weight: bold;
                font-size: 20 / @rem;
                color: @color-blue;

                &.font-size-16 {
                    font-size: 16 / @rem;
                }

                &.font-size-14 {
                    font-size: 14 / @rem;
                }
            }

            .item-right {
                width: 105 / @rem;

                &-box {
                    font-size: 14 / @rem;
                    color: @color-gray-3;
                    font-weight: bold;
                    display: flex;
                    flex-wrap: nowrap;
                }

                &-text,
                &-label {
                    display: block;
                }

                &-label {
                    width: 50 / @rem;
                    font-size: 12 / @rem;
                    color: @color-gray-6;
                    font-weight: 400;
                    white-space: nowrap;
                }
            }
        }
    }
}
