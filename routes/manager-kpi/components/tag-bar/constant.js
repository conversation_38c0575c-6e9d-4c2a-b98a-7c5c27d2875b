/**
 * 筛选TAG
 * @type {Array}
 */
export const TAG_LIST = [
    { label: '按绩效', value: 'kpi' },
    { label: '按员工', value: 'worker' }
];

/**
 * 下拉筛选标签默认值
 * @type {Array}
 */
export const FILTER_LABEL_LIST = [
    {
        itemKey: 'sort',
        defaultLabel: '排序'
    },
    {
        itemKey: 'worker',
        defaultLabel: '筛员工'
    }
];

/**
 * 筛选条件-排序
 * @param   {string}                type  筛选类型: worker：按员工，kpi：按绩效
 * @returns {Array}                       筛选条件
 */
export const SORT_LIST = (type) => {
    if (type === 'worker') {
        return [
            { label: '完成率升序', value: 'kpi_achievement_rate_asc' },
            { label: '完成率降序', value: 'kpi_achievement_rate_desc' },
            { label: '名字升序', value: 'member_name_asc' },
            { label: '名字降序', value: 'member_name_desc' }
        ];
    } else {
        return [
            { label: '完成率升序', value: 'kpi_achievement_rate_asc' },
            { label: '完成率降序', value: 'kpi_achievement_rate_desc' }
        ];
    }
};
