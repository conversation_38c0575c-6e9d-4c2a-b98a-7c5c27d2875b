// JS
import React, { memo, useRef, useEffect, useState } from 'react';
import { connect } from 'dva';
import { useScroll, useThrottleEffect, useSetState } from 'ahooks';
import { executeCB, getUrlQueryObject, getUrlQueryStringify } from 'ttp-utils';
import cls from 'classnames';
import { PAGE_URL } from 'routes/router-config.js';
import { TAG_LIST, FILTER_LABEL_LIST, SORT_LIST } from './constant.js';
// 组件
import { DropdownMenu } from 'ttp-library';
import WorkerDrawer from 'components/worker-drawer';
const { DropdownItem } = DropdownMenu;
// LESS
import styles from './index.less';

/**
 * 我的客户筛选TAG
 * @param   {object}         props props
 * @returns {object}               jsx
 */
const TagBar = memo((props) => {
    // props
    const { dispatch, history, loading, type, navHeight, onChange, searchData, teamId, teamName } = props;
    const panelDataLoading = Boolean(
        loading.effects['app/getTeamListData'] || loading.effects['managerKpi/getGroupKpiData']
    );
    // state
    const [tagFlex, setTagFlex] = useState('');
    const [dataSource, setdataSource] = useSetState({ sort: [] });
    // 员工筛选
    const [open, setOpen] = useState(false);
    const [workerValue, setWorkerValue] = useState([]);
    const dropRef = useRef(null);
    // scroll
    const scroll = useScroll(document);
    // 计算属性

    // 常量
    const tagRef = useRef(null);
    const tagClasss = cls(styles['tagbar']);
    const tagConClass = cls(styles['tagbar-content'], tagFlex);

    /**
     * 标签切换点击
     * @param   {number}                code 选中数据
     */
    const handleChange = (code) => {
        if (code === type) {
            return;
        }
        dispatch({
            type: 'managerKpi/updateState',
            payload: { type: code, dataList: [] }
        });
        // 标签切换，清空搜索条件
        dispatch({ type: 'managerKpi/resetSearchState' });
        // 格式化排序筛选
        setdataSource({ sort: SORT_LIST(code) });
        history.replace({
            pathname: PAGE_URL.managerKpi,
            search: getUrlQueryStringify({ id: teamId, name: teamName, tag: code }, '?')
        });
    };

    /**
     * 下拉筛选菜单 chaneg
     * @param  {object} value   选中的值
     */
    const handleMenuChange = (value) => {
        console.log('handleMenuChange', value);
        dispatch({
            type: 'managerKpi/updateSearchState',
            payload: value
        });
        executeCB(onChange, value);
    };

    /**
     * 下拉筛选菜单--打开菜单
     * @param {string} itemKey  key
     */
    const handleMenuOpenItem = (itemKey) => {
        if (itemKey === 'worker') {
            setOpen(true);
        }
    };

    /**
     * 筛选条件“员工”，change 事件
     * @param {object} checkedData   data
     * @param {object}  rowData 行数据
     */
    const handleWorkerChange = (checkedData, rowData) => {
        console.log('onOK checkedData:', checkedData, 'onOK rowData:', rowData);

        setWorkerValue(checkedData);
        const worker = {
            label: `员工（${checkedData.length}）`,
            value: checkedData
        };
        dispatch({
            type: 'managerKpi/updateSearchState',
            payload: { worker }
        });
        executeCB(onChange, { ...searchData, worker });
    };

    /**
     * 关闭弹框
     */
    const handleWorkerClose = () => {
        setOpen(false);
        dropRef.current?.closeAllDrawer();
    };

    // useEffect
    useEffect(() => {
        // 获取选中参数
        const { tag = 'kpi' } = getUrlQueryObject();
        setdataSource({ sort: SORT_LIST(tag) });
        dispatch({
            type: 'managerKpi/updateState',
            payload: { type: tag }
        });
        console.log('tag-bar-useEffect', tag);
    }, []);
    // 监听页面滚动
    useThrottleEffect(
        () => {
            if (panelDataLoading) {
                return;
            }
            const tagOffsetTop = tagRef.current.offsetTop;
            // console.log('useThrottleEffect-scroll', scroll, tagOffsetTop);
            // 获取当前滚动的位置
            const { top = 0 } = scroll || {};
            // 判断是否滚动到底部（考虑到可能的浏览器差异）
            if (top > tagOffsetTop - navHeight) {
                setTagFlex(styles['tagbar-flex']);
            } else {
                setTagFlex('');
            }
        },
        [scroll],
        { wait: 1000 / 60 }
    );

    return (
        <div className={tagClasss} ref={tagRef}>
            <div className={tagConClass} style={{ top: navHeight }}>
                <h2 className={styles['tagbar-title']}>绩效员工明细</h2>
                <ul className={styles['tagbar-main']}>
                    {TAG_LIST.map((item) => {
                        const itemClass = cls(styles['tagbar-main-item'], {
                            [styles['tagbar-main-item--active']]: item.value == type
                        });
                        return (
                            <li
                                className={itemClass}
                                key={item.value}
                                onClick={() => handleChange(item.value)}>
                                {item.label}
                            </li>
                        );
                    })}
                </ul>
                {/* 筛选条件 */}
                <DropdownMenu
                    className={styles['search-dropdown']}
                    dataSource={dataSource}
                    key={`${teamId}-${type}-dropdown-menu`}
                    labelList={FILTER_LABEL_LIST}
                    labelType='refill'
                    menuType='tag'
                    onChange={handleMenuChange}
                    onOpenItem={handleMenuOpenItem}
                    ref={dropRef}
                    value={searchData}
                    viewType='tag'>
                    <DropdownItem itemKey='worker' />
                </DropdownMenu>
                {/* 员工筛选 */}
                <WorkerDrawer
                    key={`${teamId}-${type}-worker-drawer`}
                    multiple={true}
                    onChange={handleWorkerChange}
                    onClose={handleWorkerClose}
                    open={open}
                    teamId={teamId}
                    value={workerValue}
                />
            </div>
        </div>
    );
});

export default connect(({ managerKpi, loading }) => ({ ...managerKpi, loading }))(TagBar);
