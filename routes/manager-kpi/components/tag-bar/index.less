@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';

.tagbar {
    // overflow: hidden;
    height: 120 / @rem;

    &-content {
        width: 375 / @rem;
        background: @color-page-bg;
    }

    &-flex {
        position: fixed;
        z-index: 20;
        left: 50%;
        margin-left: -187 / @rem;
        box-shadow: 0 (4 / @rem) (4 / @rem) 0 rgba(0, 0, 0, 0.05);
    }

    &-title {
        height: 44 / @rem;
        line-height: 44 / @rem;
        color: @color-black;
        font-size: 16 / @rem;
        font-weight: bold;
        padding: 0 (15 / @rem);
    }

    &-main {
        display: flex;
        align-items: center;
        box-sizing: content-box;
        height: 28 / @rem;
        padding: 0 (12 / @rem) (5 / @rem);

        &-item {
            width: 130 / @rem;
            height: 28 / @rem;
            line-height: 28 / @rem;
            background: #ffffff;
            border-radius: 14 / @rem;
            border: 1px solid #f2f2f2;
            color: @color-gray-9;
            font-size: 14 / @rem;
            text-align: center;
            margin-right: 8 / @rem;

            &--active {
                border: 1px solid @color-blue;
                color: @color-blue;
                background-color: #e5f5fc;
            }

            &:last-child {
                margin-right: 0;
            }
        }
    }

    .search-dropdown {
        :global {
            .Saitama-dropdown-menu__bar {
                padding: (4 / @rem) (15 / @rem);
            }

            .Saitama-dropdown-menu__item {
                flex: none;
                color: @color-gray-3;
            }
            .Saitama-dropdown-menu__title-arrow {
                border-color: transparent transparent #222 #222;
            }
        }
    }
}
