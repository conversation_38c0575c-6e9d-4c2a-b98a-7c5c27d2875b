// JS
import React, { memo, useEffect, useState } from 'react';
import { connect } from 'dva';
import { useDebounceEffect, useToggle } from 'ahooks';
// import { MANAGER_TAB } from 'common/js/constant.js';
import { PAGE_URL } from 'routes/router-config.js';
import { getUrlQueryObject, isEmpty, getUrlQueryStringify } from 'ttp-utils';
// 组件
import { Loader, Empty, Button } from 'ttp-library';
// import TabNav from 'components/tab-nav/index.js';
import DrawerDown from 'components/drawer-down/index.js';
import PanelData from './components/panel-data/index.js';
import TagBar from './components/tag-bar/index.js';
import ListItem from './components/list-item/index.js';
import BulkImport from './components/bulk-import/index.js';
import KpiDrawer from './components/kpi-drawer/index.js';
import ProgressCircle from 'components/progress-bar/circle.js';
// LESS
import styles from './index.less';

/**
 * 我的团队-绩效任务
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const ManagerKpi = memo((props) => {
    // props
    const { history, dispatch, loading, panelData, dataList, type, searchData, teamList, teamId, teamName } =
        props;
    const isLoading = Boolean(
        loading.effects['managerKpi/getTeamListData'] ||
            loading.effects['managerKpi/getGroupKpiData'] ||
            loading.effects['managerKpi/getTroupKpiByUserData'] ||
            loading.effects['managerKpi/getGroupKpiByKpiData']
    );

    // state
    const [navHeight, setNavHeight] = useState(0);
    const [kpiManageId, setKpiManageId] = useState('');

    // 批量导入弹窗
    const [openBulkImport, { setLeft: setBulkImportClose, setRight: setBulkImportOpen }] = useToggle(false);
    // 新增/编辑绩效弹窗
    const [openkpiDrawer, { setLeft: setkpiDrawerClose, setRight: setkpiDrawerOpen }] = useToggle(false);
    // 团队切换弹窗
    const [openTeam, { setLeft: setTeamClose, setRight: setTeamOpen }] = useToggle(false);
    // 函数定义

    /**
     * 获取团队KPI统计数据
     * @param   {string}                    teamId 团队id
     */
    const getGroupKpiData = (teamId) => {
        if (isEmpty(teamId)) {
            return;
        }
        dispatch({ type: 'managerKpi/getGroupKpiData', payload: { teamId } });
    };
    /**
     * 页面初始化,获取数据
     * @param {object}                    searchData 搜索数据
     * @param {number}                    id         团队ID
     */
    const getPageListData = (searchData, id = teamId) => {
        if (isEmpty(id)) {
            return;
        }
        if (type == 'worker') {
            dispatch({ type: 'managerKpi/getTroupKpiByUserData', payload: { ...searchData, teamId: id } });
        }
        if (type == 'kpi') {
            dispatch({ type: 'managerKpi/getGroupKpiByKpiData', payload: { ...searchData, teamId: id } });
        }
    };

    // /**
    //  * 导航tab切换点击
    //  * @param {object}                    item 当前选中数据
    //  */
    // const handleNavChange = (item) => {
    //     history.push(item.url);
    // };

    /**
     * 切换团队
     * @param {string}                    value 当前选中数据
     * @param {object}                    item  当前选中数据
     */
    const handleTeamChange = (value, item) => {
        dispatch({
            type: 'managerKpi/updateState',
            payload: { teamId: value, teamName: item.label }
        });
        // 重置搜索条件
        dispatch({
            type: 'managerKpi/resetSearchState'
        });
        setTeamClose();
        history.replace({
            pathname: PAGE_URL.managerKpi,
            search: getUrlQueryStringify({ id: item.value, name: item.label, tag: type }, '?')
        });
        getGroupKpiData(value);
    };

    /**
     * 编辑个人绩效
     * @param {object}                    item 当前选中数据
     */
    const handleEdit = (item) => {
        setKpiManageId(item.kpiManageId);
        setkpiDrawerOpen();
    };

    /**
     * 查看更多绩效（历史）
     */
    const handleGoHistry = () => {
        history.push({
            pathname: PAGE_URL.manageKpiHistory,
            search: getUrlQueryStringify({ id: teamId }, '?')
        });
    };

    /**
     * 批量导入，弹窗关闭
     * @param   {string}                    type 关闭状态，close，success
     */
    const handleBulkImportClose = (type) => {
        setBulkImportClose();
        if (type == 'success') {
            getGroupKpiData(teamId);
            getPageListData(searchData);
        }
    };

    /**
     * 编辑/新增绩效，弹窗关闭
     * @param   {string}                    type 关闭状态，close，success
     */
    const handlesetkpiDrawerClose = (type) => {
        setKpiManageId('');
        setkpiDrawerClose();
        if (type == 'success') {
            getGroupKpiData(teamId);
            getPageListData(searchData);
        }
    };

    // useEffect
    useEffect(() => {
        const { id, name } = getUrlQueryObject();
        // 获取团队人员数据
        if (teamList.length == 0) {
            dispatch({ type: 'managerKpi/getTeamListData' });
        }
        // 获取团队KPI统计数据
        if (!isEmpty(id) && !isEmpty(name)) {
            dispatch({ type: 'managerKpi/updateState', payload: { teamId: id, teamName: name } });
            getGroupKpiData(id);
        }
        return () => {
            dispatch({ type: 'managerKpi/resetState' });
        };
    }, []);

    // 根据数据类型获取数据
    useDebounceEffect(
        () => {
            console.log('useDebounceEffect-type', type);
            console.log('useDebounceEffect-teamId', teamId);
            getPageListData(searchData);
        },
        [type, teamId],
        { wait: 250 }
    );

    return (
        <>
            <Loader maskOpacity={0.6} open={isLoading} />
            <div className={styles['manager-kpi']}>
                {/* <TabNav current={0} getHeight={setNavHeight} list={MANAGER_TAB} onChange={handleNavChange} /> */}
                {/* 绩效数据 */}
                <PanelData data={panelData} navHeight={navHeight} title={`${teamName}团队绩效`}>
                    <Button
                        className={styles['panel-data-more']}
                        mode='link'
                        onClick={setTeamOpen}
                        skin='black'>
                        <span>切换团队</span>
                        {/* <Icon className='f-ml5' type='icon-change' /> */}
                    </Button>
                </PanelData>
                {/* 绩效员工明细 */}
                <TagBar history={history} navHeight={navHeight} onChange={getPageListData} />
                {/* 数据展示 */}
                <div className={styles['manager-kpi-body']}>
                    {dataList.length ? (
                        dataList.map((item) => (
                            <ListItem itemData={item} key={item.key} onEdit={handleEdit} type={type} />
                        ))
                    ) : (
                        <Empty />
                    )}
                </div>
                {/* 底部按钮 */}
                <div className={styles['manager-kpi-footer']}>
                    <Button
                        block
                        className={styles['manager-kpi-footer-btn']}
                        loading={isLoading}
                        onClick={handleGoHistry}
                        skin='white'>
                        更多绩效
                    </Button>
                    <Button
                        block
                        className={styles['manager-kpi-footer-btn']}
                        loading={isLoading}
                        onClick={setBulkImportOpen}
                        skin='white'>
                        批量导入
                    </Button>
                    <Button
                        block
                        className={styles['manager-kpi-footer-add']}
                        loading={isLoading}
                        onClick={setkpiDrawerOpen}>
                        新增绩效
                    </Button>
                </div>
                {/* 时间进度 */}
                <ProgressCircle />
            </div>
            {/* 批量导入 */}
            <BulkImport onClose={handleBulkImportClose} open={openBulkImport} />
            {/* 新增/编辑绩效 */}
            <KpiDrawer
                id={kpiManageId}
                onClose={handlesetkpiDrawerClose}
                open={openkpiDrawer}
                teamId={teamId}
            />
            {/* 切换团队 */}
            <DrawerDown
                dataList={teamList}
                onChange={handleTeamChange}
                onClose={setTeamClose}
                open={openTeam}
                title={'选择团队'}
                value={teamId}
            />
        </>
    );
});

export default connect(({ managerKpi, loading }) => ({ ...managerKpi, loading }))(ManagerKpi);
