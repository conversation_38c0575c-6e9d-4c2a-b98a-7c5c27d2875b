@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';
@import '../../common/less/dropdown.less';

.manager-kpi {
    padding-bottom: 90 / @rem;

    .panel-data-more {
        :global {
            .Saitama-button-content {
                font-size: 14 / @rem;
                display: flex;
                align-items: center;
            }
        }
    }

    &-body {
        width: 360 / @rem;
        margin: 0 auto;
    }

    &-footer {
        width: 375 / @rem;
        padding: 0 (13 / @rem);
        height: 74 / @rem;
        background-color: @color-white;
        display: flex;
        justify-content: space-between;
        align-items: center;
        align-content: center;
        position: fixed;
        z-index: 10;
        left: 50%;
        bottom: 0;
        margin-left: -187 / @rem;

        &-btn {
            width: 97 / @rem;
            height: 44 / @rem;
            border: 1px solid @color-gray-9;
            color: @color-gray-9;
        }

        &-add {
            width: 131 / @rem;
            height: 44 / @rem;
        }
    }
}

.librarySelectPerson();
