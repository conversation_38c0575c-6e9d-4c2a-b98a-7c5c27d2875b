/**
 * 我的团队-绩效任务
 */
import * as api from './api.js';

/**
 * 单个绩效保存/展示用数据
 * @returns {object}                     默认参数
 */
const defaultKpiFormData = () => ({
    // 指标编码
    indicatorCode: '',
    // 指标名称
    indicatorName: '',
    // 绩效时间,例如: 2025-03
    kpiTime: '',
    // 成员id
    memberId: '',
    // 员工名
    memberName: '',
    // KPI目标值
    targetValue: '',
    // KPI目标值权重
    targetValueWeight: ''
});

/**
 * 筛选条件
 * @returns {object}                     默认参数
 */
const defaultSearchData = () => ({
    // 排序
    sort: '',
    // 员工筛选
    worker: ''
});

/**
 * 默认参数
 * @returns {object}                     默认参数
 */
const initState = () => ({
    // 团队ID
    teamId: '',
    // 团队名称
    temaName: '',
    // 绩效统计
    panelData: {
        // 员工总数
        groupMemberNumber: 0,
        // 绩效数据
        kpiAchievementList: []
    },
    // 按员工筛选数据
    dataList: [],
    // 筛选条件
    searchData: defaultSearchData(),
    // 员工:worker、绩效:kpi
    type: 'worker',
    // 单个绩效保存/展示用数据
    kpiFormData: defaultKpiFormData(),
    // 团队成员数据
    teamList: []
});

export default {
    namespace: 'managerKpi',

    state: initState(),

    reducers: {
        /**
         * 重置状态
         * @returns {object}                     更新过的state
         */
        resetState() {
            return initState();
        },

        /**
         * 更新状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateState(state, action) {
            return {
                ...state,
                ...action.payload
            };
        },

        /**
         * 更新"kpiFormData"状态
         *
         * @param   {object}               state          当前state
         * @returns {object}                              更新过的state
         */
        resetKpiState(state) {
            return {
                ...state,
                kpiFormData: {
                    ...state.kpiFormData,
                    ...defaultKpiFormData()
                }
            };
        },

        /**
         * 更新"kpiFormData"状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateKpiState(state, action) {
            return {
                ...state,
                kpiFormData: {
                    ...state.kpiFormData,
                    ...action.payload
                }
            };
        },

        /**
         * 更新"searchData"状态
         *
         * @param   {object}               state          当前state
         * @returns {object}                              更新过的state
         */
        resetSearchState(state) {
            return {
                ...state,
                searchData: defaultSearchData()
            };
        },

        /**
         * 更新"searchData"状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateSearchState(state, action) {
            return {
                ...state,
                searchData: {
                    ...state.searchData,
                    ...action.payload
                }
            };
        }
    },

    effects: {
        /**
         * 获取团队KPI统计数据
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *getGroupKpiData({ payload }, { call, put }) {
            const { result } = yield call(api.getGroupKpi, payload);
            yield put({
                type: 'updateState',
                payload: {
                    panelData: result || {}
                }
            });
        },

        /**
         * 按员工查询数据
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *getTroupKpiByUserData({ payload }, { call, put }) {
            const { result = [] } = yield call(api.getTroupKpiByUser, payload);
            yield put({
                type: 'updateState',
                payload: {
                    dataList: (Array.isArray(result) ? result : []).map((item) => ({
                        ...item,
                        key: `${item.memberName}-${item.memberId}`
                    }))
                }
            });
        },

        /**
         * 按绩效查询
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *getGroupKpiByKpiData({ payload }, { call, put }) {
            const { result = [] } = yield call(api.getGroupKpiByKpi, payload);
            yield put({
                type: 'updateState',
                payload: {
                    dataList: (Array.isArray(result) ? result : []).map((item) => ({
                        ...item,
                        key: `${item.indicatorName}-${item.indicatorCode}`
                    }))
                }
            });
        },

        /**
         * 保存：批量导入绩效
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *saveKpiListData({ payload }, { call, put }) {
            const res = yield call(api.saveKpiList, payload);
            return res;
        },

        /**
         * 保存：批量导入绩效
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *saveKpiItemData({ payload }, { call, put }) {
            return yield call(api.saveKpiItem, payload);
        },

        /**
         * 保存：批量导入绩效
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *updateKpiItemData({ payload }, { call, put }) {
            return yield call(api.updateKpiItem, payload);
        },

        /**
         * 保存：批量导入绩效
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *getKpiItemData({ payload }, { call, put }) {
            const { result = {} } = yield call(api.getKpiItem, payload);
            yield put({
                type: 'updateKpiState',
                payload: result
            });
        },

        /**
         * 获取团队数据
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *getTeamListData({ payload }, { call, put }) {
            const teamList = yield call(api.getTeamList, payload);
            yield put({
                type: 'updateState',
                payload: {
                    teamList
                }
            });
            return true;
        }
    }
};
