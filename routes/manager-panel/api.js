/**
 * @file 我的团队-绩效任务
 */

/* requires JS */
import { getRequest } from 'common/js/fetch.js';
import { PORT_URL } from 'common/js/constant.js';

/**
 * 获取团队KPI
 * <AUTHOR>
 * @param    {object}                 data    请求参数对象
 * @param    {number}                 data.id 团队ID
 * @returns  {Promise}                        返回一个Feach的Promise对象
 */
export function getTeamKpiList(data) {
    let opts = {
        url: PORT_URL.teamKpiListV2,
        data
    };
    return getRequest(opts);
}

/**
 * 获取团队数据
 *
 * @param   {object}               data 请求参数
 * @returns {Array}                     团队数据
 */
export const getTeamList = (data) => {
    const opts = {
        url: PORT_URL.teamList,
        data
    };
    return getRequest(opts)
        .then((res) => {
            if (Array.isArray(res?.result)) {
                const teamList = res.result.map((item) => ({
                    ...item,
                    label: item.teamName,
                    value: item.teamId
                }));
                teamList.unshift({ label: '全部', value: '' });
                return teamList;
            }
            return [];
        })
        .catch(() => []);
};
