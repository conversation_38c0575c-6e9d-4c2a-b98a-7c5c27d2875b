// JS
import React, { memo, useEffect } from 'react';
import { useToggle } from 'ahooks';
// 组件
import Panel from 'components/panel';
import PanelItem from 'components/panel-item';
// LESS
import styles from '../index.less';

/**
 * 我的客户数据ITEM
 * @param   {object}                     props props
 * @returns {React.ReactElement}               jsx
 */
const ListItem = memo((props) => {
    // props
    const { itemData, onViewDetails, index } = props;
    // state
    const [isOpen, { toggle: toggleDetail }] = useToggle(false);

    // useEffect
    useEffect(() => {
        // 默认展开第一个
        if (!isOpen && index == 0) {
            toggleDetail(true);
        }
    }, []);

    return (
        <Panel
            classname={styles['list-item']}
            onClick={toggleDetail}
            open={isOpen}
            title={<span className={styles['list-item-title']}>{itemData.teamName}团队绩效</span>}>
            <PanelItem
                itemData={itemData}
                onViewDetails={onViewDetails}
                showDetailButton={itemData.teamName != '全国'}
            />
        </Panel>
    );
});

export default ListItem;
