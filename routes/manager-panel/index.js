// JS
import React, { memo, useEffect, useState } from 'react';
import { useSetState } from 'ahooks';
import { connect } from 'dva';
import { PAGE_URL } from 'routes/router-config.js';
import { getUrlQueryStringify } from 'ttp-utils';
import { MANAGER_PANEL_TAB } from 'common/js/constant.js';
// 组件
import { Loader, Empty, DropdownMenu } from 'ttp-library';
import TabNav from 'components/tab-nav/index.js';
import ListItem from './components/list-item.js';
import ProgressCircle from 'components/progress-bar/circle.js';
// styles
import styles from './index.less';

/**
 * 我的团队-团队绩效
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const ManagerPanel = memo((props) => {
    // props
    const { history, dispatch, loading, dataList, filterDataList, teamList, dataTime } = props;
    const isLoading = Boolean(
        loading.effects['managerPanel/getTeamListData'] || loading.effects['managerPanel/getTeamKpiListData']
    );

    // state
    const [navHeight, setNavHeight] = useState(0);
    const [menuValue, setMenuValue] = useSetState({
        city: {
            label: '城市',
            value: ''
        }
    });

    const labelList = [
        {
            itemKey: 'city',
            defaultLabel: '城市'
        }
    ];

    // 函数定义
    /**
     * 导航tab切换点击
     * @param {object}                    item 当前选中数据
     */
    const handleNavChange = (item) => {
        history.push(item.url);
    };

    /**
     * 下拉菜单change
     * @param  {object} value   选中的值
     */
    const onMenuChange = (value) => {
        setMenuValue({
            ...menuValue,
            ...value
        });
        // 对现有数据dataList本地筛选
        const filterDataList =
            value.city.label == '全部'
                ? dataList
                : dataList.filter((item) => {
                      return item.teamName == value.city.label;
                  });
        dispatch({ type: 'managerPanel/updateState', payload: { filterDataList } });
    };

    /**
     * 查看团队详情
     * @param  {object} item   选中的值
     */
    const onViewDetails = (item) => {
        console.log('查看详情:', item);
        history.push({
            pathname: PAGE_URL.managerKpi,
            search: getUrlQueryStringify({ id: item.teamId, name: item.teamName }, '?')
        });
    };
    // useEffect
    useEffect(() => {
        dispatch({ type: 'managerPanel/getTeamKpiListData' });
        // 获取城市数据数据
        if (teamList.length == 0) {
            dispatch({ type: 'managerPanel/getTeamListData' });
        }
    }, []);
    const dataSource = { city: teamList };
    return (
        <>
            <Loader maskOpacity={0.6} open={isLoading} />
            <div className={styles['manager-panel']}>
                <TabNav
                    current={1}
                    getHeight={setNavHeight}
                    list={MANAGER_PANEL_TAB}
                    onChange={handleNavChange}
                />
                {/* 头部 */}
                <div className={styles['manager-panel-header']} style={{ top: navHeight }}>
                    <DropdownMenu
                        className={styles['dropdown-menu-active']}
                        dataSource={dataSource}
                        key={'city-dropdown-menu'}
                        labelList={labelList}
                        menuType='tag'
                        onChange={onMenuChange}
                        value={menuValue}
                    />
                    <p className={styles['manager-panel-header-time']}>{`数据更新时间:${dataTime}`}</p>
                </div>
                {/* 绩效数据 */}
                <div style={{ height: navHeight }} />
                <div className={styles['manager-panel-body']}>
                    {Array.isArray(filterDataList) && filterDataList.length > 0 ? (
                        filterDataList.map((item, index) => (
                            <ListItem
                                index={index}
                                itemData={item}
                                key={`teamId-${item.teamId}`}
                                onViewDetails={onViewDetails}
                            />
                        ))
                    ) : (
                        <Empty />
                    )}
                </div>
                {/* 时间进度 */}
                <ProgressCircle />
            </div>
        </>
    );
});

export default connect(({ managerPanel, loading }) => ({ ...managerPanel, loading }))(ManagerPanel);
