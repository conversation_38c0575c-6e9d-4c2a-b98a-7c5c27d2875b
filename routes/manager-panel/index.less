@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';
@import '../../common/less/dropdown.less';
@import '../../common/less/list.less';

.manager-panel {
    padding-bottom: 20 / @rem;

    &-body {
        position: relative;
        margin-top: 50 / @rem;
    }
    &-header {
        position: fixed;
        z-index: 100;
        display: flex;
        align-items: center;
        padding: 10 / @rem;
        height: 50 / @rem;
        width: 375 / @rem;
        background: #f6f6f6;
        &-time {
            position: absolute;
            color: @color-gray-9;
            font-size: 12 / @rem;
            right: 0;
            margin-right: 20 / @rem;
        }
        .dropdown-menu-active {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            :global {
                .Saitama-dropdown-menu__bar {
                    padding: (10 / @rem);
                }

                .Saitama-dropdown-menu__item {
                    flex: none;
                    color: @color-gray-3;
                    height: 30 / @rem;
                }

                .Saitama-dropdown-menu__item-radius {
                    background: rgba(0, 162, 232, 0.1);
                    border: 1px solid #00a2e8;
                    width: 120 / @rem;
                    height: 30 / @rem;
                    padding: 0;
                    .Saitama-dropdown-menu__title-selected {
                        font-weight: 600;
                        font-size: 14 / @rem;
                        color: #00a2e8;
                    }
                }
            }
        }
    }

    &-item {
        background-color: @color-white;
        padding-bottom: 16 / @rem;
        margin-bottom: 8 / @rem;
    }

    .panel-data-more {
        :global {
            .Saitama-button-content {
                color: @color-black;
                font-size: 14 / @rem;
                display: flex;
                align-items: center;
            }
        }

        &-icon {
            font-size: 12 / @rem;
            margin-left: 5 / @rem;
            color: @color-gray-9;
        }
    }

    .list-item {
        margin-bottom: 10 / @rem;
        position: relative;
        border-radius: 6px 6px;
        padding: (10 / @rem);

        :global {
            .white-box-title {
                font-size: 11 / @rem;
                font-weight: 400;
                color: @color-gray-3;
                display: flex;
                flex-direction: column;
                line-height: 16 / @rem;
            }
        }

        &-title {
            font-size: 14 / @rem;
            font-weight: 600;
            color: #222;
        }
    }
}
