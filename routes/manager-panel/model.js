/**
 * 我的团队-绩效任务
 */
import * as api from './api.js';
/**
 * 默认参数
 * @returns {object}                     默认参数
 */
const initState = () => ({
    // 团队绩效原始数据
    dataList: [],
    // 列表展示的数据
    filterDataList: [],
    // 团队成员数据
    teamList: [],
    // 数据更新时间
    dataTime: ''
});

export default {
    namespace: 'managerPanel',

    state: initState(),

    reducers: {
        /**
         * 重置状态
         * @returns {object}                     更新过的state
         */
        resetState() {
            return initState();
        },

        /**
         * 更新状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateState(state, action) {
            return {
                ...state,
                ...action.payload
            };
        }
    },

    effects: {
        /**
         * 获取团队KPI统计数据
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *getTeamKpiListData({ payload }, { call, put }) {
            const { result = {} } = yield call(api.getTeamKpiList, payload);
            const { list = [], dataTime = '' } = result;
            // 处理数据
            const kpiList = Array.isArray(list)
                ? list.map(({ teamInfo, ...other }) => ({ ...teamInfo, ...other }))
                : [];
            yield put({
                type: 'updateState',
                payload: {
                    dataTime,
                    // 团队绩效原始数据
                    dataList: kpiList,
                    filterDataList: kpiList
                }
            });
        },
        /**
         * 获取团队数据
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *getTeamListData({ payload }, { call, put }) {
            const teamList = yield call(api.getTeamList, payload);
            yield put({
                type: 'updateState',
                payload: {
                    teamList
                }
            });
            return true;
        }
    }
};
