/**
 * @file 我的团队-绩效任务
 */

/* requires JS */
import { getRequest } from 'common/js/fetch.js';
import { PORT_URL } from 'common/js/constant.js';

/**
 * 获取团队数据
 *
 * @param   {object}               data 请求参数
 * @returns {Array}                     团队数据
 */
export const getTeamList = (data) => {
    const opts = {
        url: PORT_URL.teamList,
        data
    };
    return getRequest(opts)
        .then((res) => {
            if (Array.isArray(res?.result)) {
                return res.result.map((item) => ({ ...item, label: item.teamName, value: item.teamId }));
            }
            return [];
        })
        .catch(() => []);
};

/**
 * 查询可制定的绩效枚举
 * <AUTHOR>
 * @param    {object}                 data        请求参数对象
 * @returns  {Promise}                             返回一个Feach的Promise对象
 */
export function getKpiIndicatorList(data) {
    let opts = {
        url: PORT_URL.getKpiMapV2,
        data
    };
    return getRequest(opts).then((res) => {
        if (Array.isArray(res.result)) {
            return res.result.map((item) => {
                return {
                    ...item,
                    label: item.indicatorName,
                    value: item.indicatorCode
                };
            });
        }
    });
}

/**
 * 查询可制定的绩效枚举
 * <AUTHOR>
 * @param    {object}                 data        请求参数对象
 * @returns  {Promise}                             返回一个Feach的Promise对象
 */
export function getKpiPanel(data) {
    let opts = {
        url: PORT_URL.kpiPanel,
        data
    };
    return getRequest(opts);
}
