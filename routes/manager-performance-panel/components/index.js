import React, { memo } from 'react';
import styles from './index.less';
import cls from 'classnames';
import { executeCB } from 'ttp-utils';

const PanelTable = memo((props) => {
    const { dataList, type = 1, sort, onTodaySortClick, onYesterdaySortClick, onMonthSortClick } = props;

    /**
     * 获取箭头的class
     * @param {boolean} open 是否展开
     * @returns {string} 箭头的class
     */
    const getArrowUpClass = (open) => {
        return cls(styles['arrow-up'], open && styles['arrow-up-active']);
    };
    /**
     * 获取箭头的class
     * @param {boolean} open 是否展开
     * @returns {string} 箭头的class
     */
    const getArrowDownClass = (open) => {
        return cls(styles['arrow-down'], { [styles['arrow-down-active']]: open });
    };
    return (
        <>
            <table className={styles['panel-table']}>
                <thead>
                    <tr>
                        <th>
                            <span className={styles['text']}>城市</span>
                        </th>
                        {type === 1 ? (
                            <>
                                <th>
                                    <div
                                        className={styles['header']}
                                        onClick={() => executeCB(onTodaySortClick)}>
                                        <span className={styles['text']}>当日</span>
                                        <div className={styles['arrow-container']}>
                                            <i className={getArrowUpClass(sort.day == 1)} />
                                            <i className={getArrowDownClass(sort.day == 2)} />
                                        </div>
                                    </div>
                                </th>
                                <th>
                                    <div
                                        className={styles['header']}
                                        onClick={() => executeCB(onYesterdaySortClick)}>
                                        <span className={styles['text']}>昨日</span>
                                        <div className={styles['arrow-container']}>
                                            <i className={getArrowUpClass(sort.yesterday == 1)} />
                                            <i className={getArrowDownClass(sort.yesterday == 2)} />
                                        </div>
                                    </div>
                                </th>
                            </>
                        ) : (
                            <th>
                                <div className={styles['header']} onClick={() => executeCB(onMonthSortClick)}>
                                    <span className={styles['text']}>完成率</span>
                                    <div className={styles['arrow-container']}>
                                        <i className={getArrowUpClass(sort.month == 1)} />
                                        <i className={getArrowDownClass(sort.month == 2)} />
                                    </div>
                                </div>
                            </th>
                        )}
                    </tr>
                </thead>
                <tbody>
                    {dataList.length > 0 &&
                        dataList.map((item) => (
                            <tr key={item.name + item.todayDataText}>
                                <td>{item.name}</td>
                                {type === 1 && <td>{item.todayDataText}</td>}
                                {type === 1 && <td>{item.yesterdayDataText}</td>}
                                {type === 2 && <td>{item.monthDataText}</td>}
                            </tr>
                        ))}
                </tbody>
            </table>
        </>
    );
});

export default PanelTable;
