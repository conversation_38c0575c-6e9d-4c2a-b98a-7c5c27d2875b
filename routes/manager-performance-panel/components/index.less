@import '../../../common/less/validation.less';
@import '../../../common/less/mixins.less';

.panel-table {
    border-radius: 8 / @rem;
    border-collapse: collapse;
    overflow: hidden;
    margin: 0 (8 / @rem);
    justify-content: space-between;
    width: (375 - 16) / @rem;

    thead tr {
        background-color: #f1fbff;
        height: 34 / @rem;
        & th {
            border-bottom: 1px solid #00a2e880;
        }
        .text {
            color: #00a2e8;
            font-size: 12 / @rem;
            font-weight: bold;
            text-align: center;
        }
    }

    tbody tr {
        background-color: #fff;
        height: 34 / @rem;
        border-bottom: 1px solid #ededed;
        &:last-child {
            border-bottom: none;
        }

        & td {
            color: #222;
            font-weight: bold;
            font-size: 14 / @rem;
            text-align: center;
            padding: 5 / @rem;
            border-left: 1px solid #ededed;
            &:first-child {
                border-left: none;
                font-size: 12 / @rem;
                width: 90 / @rem;
                font-weight: normal;
            }
        }
    }

    .header {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .arrow-container {
        display: flex;
        flex-direction: column;
        margin-left: 3 / @rem;

        .arrow-down {
            margin-top: 2 / @rem;
            .triangle-down(@color-black, 8 / @rem);
            &-active {
                border-top-color: #00a2e8;
            }
        }

        .arrow-up {
            .triangle-up(@color-black, 8 / @rem);
            &-active {
                border-bottom-color: #00a2e8;
            }
        }
    }
}
