// JS
import React, { memo, useEffect, useState } from 'react';
import { useSetState } from 'ahooks';
import { connect } from 'dva';
import { MANAGER_PANEL_TAB } from 'common/js/constant.js';
// 组件
import { Loader, DropdownMenu } from 'ttp-library';
import TabNav from 'components/tab-nav/index.js';
import PanelTabel from './components/index.js';
import ProgressCircle from 'components/progress-bar/circle.js';
// styles
import styles from './index.less';

const panelTypeList = [
    {
        label: '当日',
        value: 1
    },
    {
        label: '当月',
        value: 2
    }
];

/**
 * 我的团队-团队绩效
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const ManagerPerformancePanel = memo((props) => {
    // props
    const { history, dispatch, loading, dataList, kpiIndicatorList, dataTime, sort } = props;
    const isLoading = Boolean(
        loading.effects['managerPerformancePanel/getKpiIndicatorList'] ||
            loading.effects['managerPerformancePanel/getKpiPanel']
    );

    // state
    const [navHeight, setNavHeight] = useState(0);
    const [menuValue, setMenuValue] = useSetState({
        indicator: {
            label: '请选择业绩指标',
            value: ''
        },
        type: panelTypeList[0]
    });

    const labelList = [
        {
            itemKey: 'indicator',
            defaultLabel: '提车量'
        },
        {
            itemKey: 'type',
            defaultLabel: '当日'
        }
    ];

    const dataSource = {
        indicator: kpiIndicatorList,
        type: panelTypeList
    };

    // 函数定义
    /**
     * 导航tab切换点击
     * @param {object}                    item 当前选中数据
     */
    const handleNavChange = (item) => {
        history.push(item.url);
    };

    /**
     * 排序
     * @param {object} newSort 排序方式
     */
    const handleSort = (newSort) => {
        dispatch({
            type: 'managerPerformancePanel/updateSortState',
            payload: newSort
        });
        // 本地筛选数据
        dispatch({
            type: 'managerPerformancePanel/kpiPanelSort',
            payload: {
                type: menuValue.type.value
            }
        });
    };

    /**
     * 当日排序
     */
    const onTodaySortClick = () => {
        handleSort({
            day: sort.day == 1 ? 2 : 1,
            yesterday: 0,
            month: sort.month || 1
        });
    };

    /**
     * 昨日排序
     */
    const onYesterdaySortClick = () => {
        handleSort({
            day: 0,
            yesterday: sort.yesterday == 1 ? 2 : 1,
            month: sort.month || 1
        });
    };
    /**
     * 当月排序
     */
    const onMonthSortClick = () => {
        handleSort({
            day: sort.day,
            yesterday: sort.day == 0 ? sort.yesterday : 0,
            month: sort.month == 1 ? 2 : 1
        });
    };

    /**
     * 下拉菜单change
     * @param  {object} value   选中的值
     */
    const onMenuChange = (value) => {
        const newMenuValue = {
            ...menuValue,
            ...value
        };
        setMenuValue(newMenuValue);
        // 请求数据
        if (newMenuValue.indicator.value.length == 0) return;
        dispatch({
            type: 'managerPerformancePanel/getKpiPanel',
            payload: { kpiCode: newMenuValue.indicator.value, type: newMenuValue.type.value, sort }
        });
    };

    // useEffect
    useEffect(() => {
        // 获取绩效指标数据
        if (kpiIndicatorList.length == 0) {
            dispatch({ type: 'managerPerformancePanel/getKpiIndicatorList', payload: { orgType: 1 } });
        }
    }, []);

    useEffect(() => {
        // 默认选中参拍商户数
        if (kpiIndicatorList.length > 0) {
            const defaultIndicators = kpiIndicatorList.filter((item) => item.label == '提车量');
            if (defaultIndicators.length > 0) {
                const defaultIndicator = defaultIndicators[0];
                onMenuChange({
                    indicator: defaultIndicator
                });
            }
        }
    }, [kpiIndicatorList]);

    return (
        <>
            <Loader maskOpacity={0.6} open={isLoading} />
            <div className={styles['manager-performance-panel']}>
                <TabNav
                    current={0}
                    getHeight={setNavHeight}
                    list={MANAGER_PANEL_TAB}
                    onChange={handleNavChange}
                />

                <div className={styles['menu-container']} style={{ top: navHeight }}>
                    <DropdownMenu
                        className={styles['dropdown-menu-active']}
                        dataSource={dataSource}
                        labelList={labelList}
                        menuType='tag'
                        onChange={onMenuChange}
                        value={menuValue}
                    />
                    <p className={styles['panel-header-time']}>{`数据更新时间:${dataTime}`}</p>
                </div>
                <div style={{ height: navHeight }} />
                {/* 绩效数据 */}
                <div className={styles['panel-container']}>
                    <PanelTabel
                        dataList={dataList}
                        onMonthSortClick={onMonthSortClick}
                        onTodaySortClick={onTodaySortClick}
                        onYesterdaySortClick={onYesterdaySortClick}
                        sort={sort}
                        type={menuValue.type.value}
                    />
                </div>
                {/* 时间进度 */}
                {menuValue.type.value == 2 && <ProgressCircle />}
            </div>
        </>
    );
});

export default connect(({ managerPerformancePanel, loading }) => ({ ...managerPerformancePanel, loading }))(
    ManagerPerformancePanel
);
