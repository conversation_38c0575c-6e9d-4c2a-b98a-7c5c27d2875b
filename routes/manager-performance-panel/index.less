@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';
@import '../../common/less/dropdown.less';

.manager-performance-panel {
    padding-bottom: 20 / @rem;

    .menu-container {
        position: fixed;
        z-index: 10;
        height: 70 / @rem;
        width: 375 / @rem;
        background: #f6f6f6;
        .panel-header-time {
            color: @color-gray-9;
            font-size: 12 / @rem;
            margin-left: 10 / @rem;
            height: 20 / @rem;
        }
    }
    .dropdown-menu-active {
        width: 100%;
        height: 50 / @rem;
        :global {
            .Saitama-dropdown-menu__bar {
                padding: (10 / @rem);
            }

            .Saitama-dropdown-menu__item {
                flex: none;
                color: @color-gray-3;
                height: 30 / @rem;
                width: calc((100% - 0.5rem * 2) / 2);
            }

            .Saitama-dropdown-menu__item-radius {
                background: rgba(0, 162, 232, 0.1);
                border: 1px solid #00a2e8;
                .Saitama-dropdown-menu__title-selected {
                    font-weight: 600;
                    font-size: 14 / @rem;
                    color: #00a2e8;
                }
            }
        }
    }
    &-item {
        background-color: @color-white;
        padding-bottom: 16 / @rem;
        margin-bottom: 8 / @rem;
    }
    .panel-container {
        width: 100%;
        margin-top: 80 / @rem;
    }
    .panel-data-more {
        :global {
            .Saitama-button-content {
                color: @color-black;
                font-size: 14 / @rem;
                display: flex;
                align-items: center;
            }
        }

        &-icon {
            font-size: 12 / @rem;
            margin-left: 5 / @rem;
            color: @color-gray-9;
        }
    }
}
