/**
 * 我的团队-绩效面板
 */
import * as api from './api.js';

/**
 * 默认排序
 * @returns {object}                     默认排序
 */
const defaultSort = () => ({
    day: 2, //  1当日升序 2当日降序
    yesterday: 0, //  1昨日升序 2昨日降序
    month: 2 //  1当月完成率升序 2当月完成率降序
});

/**
 * 默认参数
 * @returns {object}                     默认参数
 */
const initState = () => ({
    // 团队绩效原始数据
    dataList: [],
    // 列表展示的数据
    filterDataList: [],
    // 绩效指标
    kpiIndicatorList: [],
    // 排序
    sort: defaultSort()
});

export default {
    namespace: 'managerPerformancePanel',

    state: initState(),

    reducers: {
        /**
         * 重置状态
         * @returns {object}                     更新过的state
         */
        resetState() {
            return initState();
        },

        /**
         * 更新状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateState(state, action) {
            return {
                ...state,
                ...action.payload
            };
        },

        /**
         * 更新"sort"状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateSortState(state, action) {
            return {
                ...state,
                sort: {
                    ...state.sort,
                    ...action.payload
                }
            };
        },

        /**
         * 绩效面板排序
         * @param {object} state 当前state
         * @param {object} action 入参
         * @param {object} action.payload 传入需要合并的state
         * @param {object} action.payload.type 传入的时间 1: 当日 2: 当月
         * @returns {object} 排序后的列表
         */
        kpiPanelSort(state, action) {
            const { sort, dataList } = state;
            const { type = 1 } = action.payload;
            const filterList = dataList.sort((a, b) => {
                let sortRule = 0;
                if (type === 1) {
                    // 当日
                    if (sort.day === 1) {
                        sortRule = a.todayData - b.todayData;
                    } else if (sort.day === 2) {
                        sortRule = b.todayData - a.todayData;
                    } else if (sort.yesterday === 1) {
                        sortRule = a.yesterdayData - b.yesterdayData;
                    } else if (sort.yesterday === 2) {
                        sortRule = b.yesterdayData - a.yesterdayData;
                    }
                } else if (type === 2) {
                    // 当月
                    if (sort.month === 1) {
                        sortRule = a.monthData - b.monthData;
                    } else {
                        sortRule = b.monthData - a.monthData;
                    }
                }
                if (sortRule != 0) {
                    return sortRule;
                } else {
                    // 如果数据相同，则按姓氏排序
                    return a.name.localeCompare(b.name);
                }
            });
            return {
                ...state,
                filterDataList: filterList
            };
        }
    },

    effects: {
        /**
         * 获取团队业绩看板
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *getKpiPanel({ payload }, { call, put }) {
            const { result = {} } = yield call(api.getKpiPanel, payload);
            const { list = [], dataTime = '' } = result;
            yield put({
                type: 'updateState',
                payload: {
                    dataTime,
                    dataList: list
                }
            });
            // 处理数据 本地排序处理数据
            yield put({
                type: 'kpiPanelSort',
                payload: {
                    type: payload.type
                }
            });
        },
        /**
         * 获取团队绩效指标
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *getKpiIndicatorList({ payload }, { call, put }) {
            const kpiIndicatorList = yield call(api.getKpiIndicatorList, payload);
            yield put({
                type: 'updateState',
                payload: {
                    kpiIndicatorList
                }
            });
            return true;
        }
    }
};
