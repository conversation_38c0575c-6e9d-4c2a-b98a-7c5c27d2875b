/**
 * @file 我的客户-我的客户
 */

/* requires JS */
import { getRequest } from 'common/js/fetch.js';
import { PORT_URL } from 'common/js/constant.js';

/**
 * 获取商户详细信息（单个）
 * <AUTHOR>
 * @param    {object}                 data          请求参数对象
 * @param    {number}                 data.dealerId 车商ID
 * @returns  {Promise}                              返回一个Feach的Promise对象
 */
export function getDealerDetail(data) {
    let opts = {
        url: PORT_URL.dealerDetail,
        data
    };
    return getRequest(opts);
}
