// JS
import React, { memo, useMemo, useState } from 'react';
import { useToggle } from 'ahooks';
import { WX_AUTH_MAP, WX_FOLLOW_MAP, BASE_INFO } from '../../constant';
// 组件
import { List, Button, ViewerPicture } from 'ttp-library';
import Panel from 'components/panel';
import Tag from 'components/tag';
// LESS
import styles from './index.less';

/**
 * 商家详情-基本信息
 * @param   {object}                     props props
 * @returns {React.ReactElement}               jsx
 */
const BaseInfo = memo((props) => {
    // props
    const { itemData } = props;
    // state
    const [open, { toggle }] = useToggle(true);
    // 图片游览
    const [currentViewIndex, setCurrentViewIndex] = useState(0);
    const [openView, { setLeft: setCloseView, setRight: setOpenView }] = useToggle(false);
    // 计算属性
    // 图片展示
    const pictureList = useMemo(() => {
        return itemData.dealerInfoPhoto.map((url) => ({ title: '图片', type: 'image', src: url }));
    }, [itemData.dealerInfoPhoto]);

    // 回调事件
    /**
     * 商户照片：点击查看
     */
    const handleViewPicture = () => {
        setCurrentViewIndex(0);
        setOpenView();
    };

    /**
     * 关闭商户照片
     */
    const handleViewPictureClose = () => {
        setCurrentViewIndex(-1);
        setCloseView();
    };

    return (
        <div className={styles['base-info-item']}>
            <Panel onClick={toggle} open={open} title='基本信息'>
                <List className={styles['list-box']}>
                    {BASE_INFO.map((item) => {
                        let extraBox = itemData[item.key] || '--';
                        // 微信互通
                        if (item.key === 'wxStatus') {
                            extraBox = (
                                <>
                                    <Tag
                                        classname={styles['item-tag']}
                                        themeColor={WX_AUTH_MAP[itemData.wxAuthStatus].type}>
                                        {WX_AUTH_MAP[itemData.wxAuthStatus].label}
                                    </Tag>
                                    <Tag
                                        classname={styles['item-tag']}
                                        themeColor={WX_FOLLOW_MAP[itemData.wxFollowStatus].type}>
                                        {WX_FOLLOW_MAP[itemData.wxFollowStatus].label}
                                    </Tag>
                                </>
                            );
                        }
                        if (item.key == 'dealerInfoPhoto') {
                            extraBox =
                                itemData[item.key].length > 0 ? (
                                    <Button mode='link' onClick={handleViewPicture} size='sm' skin='blue'>
                                        点击查看
                                    </Button>
                                ) : (
                                    '--'
                                );
                        }
                        return (
                            <List.Item className={styles['list-box-item']} extra={extraBox} key={item.key}>
                                {item.label}
                            </List.Item>
                        );
                    })}
                </List>
            </Panel>
            {/* 图片游览窗口 */}
            <ViewerPicture
                current={currentViewIndex}
                imgList={pictureList}
                onClose={handleViewPictureClose}
                open={openView}
                showTab={false}
                title='商户照片'
            />
        </div>
    );
});

export default BaseInfo;
