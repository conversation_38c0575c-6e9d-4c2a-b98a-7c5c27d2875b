@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';
@import '../../../../common/less/list.less';

.base-info-item {
    margin-bottom: 10 / @rem;
    position: relative;

    .item-tag {
        margin: 0 0 0 (5 / @rem);
    }

    .list-box {
        margin: 0;
        position: relative;

        &:after {
            .setTopLine(@color-border);
        }

        &-item {
            padding: (10 / @rem) (5 / @rem);
            min-height: 40 / @rem;
            overflow: inherit;

            &:after {
                .setBottomLine(@color-border);
            }

            &:last-child:after {
                border-bottom-width: 0px;
            }
        }
    }

    .tag-btn {
        margin-left: 5 / @rem;
        padding: 0 (5 / @rem);
    }
}
