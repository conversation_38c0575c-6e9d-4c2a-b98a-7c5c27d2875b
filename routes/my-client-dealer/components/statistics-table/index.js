// JS
import React, { memo } from 'react';
import { useToggle } from 'ahooks';
import { isEmpty } from 'ttp-utils';
import { STATISTICS_TABLE } from '../../constant';
// 组件
import Panel from 'components/panel';
// LESS
import styles from './index.less';

/**
 * 绩效指标
 */
const StatisticsTable = memo((props) => {
    // props
    const { itemData = {} } = props;
    // state
    const [open, { toggle }] = useToggle(true);

    return (
        <Panel classname={styles['statistics']} onClick={toggle} open={open} title='业务数据'>
            <table className={styles['statistics-table']}>
                <thead>
                    <tr>
                        <th>关键指标</th>
                        <th>昨日</th>
                        <th>今日</th>
                        <th>本月</th>
                    </tr>
                </thead>
                <tbody>
                    {STATISTICS_TABLE.map((item) => (
                        <tr key={item.label}>
                            <td>{item.label}</td>
                            {item.key.map((key, index) => (
                                <td key={`${item.label}${key || index}`}>
                                    {isEmpty(itemData[key]) ? '/' : itemData[key]}
                                </td>
                            ))}
                        </tr>
                    ))}
                </tbody>
            </table>
        </Panel>
    );
});

export default StatisticsTable;
