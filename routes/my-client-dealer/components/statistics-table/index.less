@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';

.statistics {
    margin-bottom: 10 / @rem;

    &-table {
        overflow: hidden;
        border-collapse: collapse;
        width: 100%;

        & > thead > tr {
            background-color: #f1fbff;
            height: 30 / @rem;

            & > th {
                color: @color-blue;
                font-size: 14 / @rem;
                font-weight: bold;
                text-align: center;
                border-bottom: 1px solid @color-blue;
            }
        }

        & > tbody > tr {
            background-color: #fff;
            height: 40 / @rem;
            border-bottom: 1px solid #ededed;

            &:last-child {
                border-bottom: none;
            }

            & > td {
                color: @color-gray-3;
                font-weight: bold;
                font-size: 14 / @rem;
                text-align: center;
                border-left: 1px solid #ededed;
                width: 25%;

                &:first-child {
                    font-size: 12 / @rem;
                    border-left: none;
                    font-weight: 400;
                }
            }
        }
    }
}
