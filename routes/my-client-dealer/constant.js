import { BUY_VIP_KEY } from 'common/js/constant.js';

/**
 * 增值权益包 筛选项
 */
export const BUY_VIP_MAP = {
    [BUY_VIP_KEY.YES]: { label: '已购权益包定金', type: 'fault' },
    [BUY_VIP_KEY.NO]: { label: '未购权益包定金', type: 'danger' }
};

/**
 * 微信授权状态-key： 0否，1是
 * @type {object}
 */
export const WX_AUTH_KEY = {
    // 未授权
    unAuthOrized: 0,
    // 已授权
    authOrized: 1
};

/**
 * 微信授权状态-MAP
 * @type {object}
 */
export const WX_AUTH_MAP = {
    // 未授权
    [WX_AUTH_KEY.unAuthOrized]: { label: '未授权', type: 'danger' },
    // 已授权
    [WX_AUTH_KEY.authOrized]: { label: '已授权', type: 'success' }
};

/**
 * 微信公众号关注状态-key: 0否，1是
 * @type {object}
 */
export const WX_FOLLOW_KEY = {
    // 未关注
    unFollow: 0,
    // 已关注
    follow: 1
};

/**
 * 微信授权状态-MAP
 * @type {object}
 */
export const WX_FOLLOW_MAP = {
    // 未授权
    [WX_FOLLOW_KEY.unFollow]: { label: '未关注', type: 'danger' },
    // 已授权
    [WX_FOLLOW_KEY.follow]: { label: '已关注', type: 'success' }
};

/**
 * 基本信息数据模版
 * @type {Array}
 */
export const BASE_INFO = [
    {
        label: '车商姓名',
        key: 'companyName'
    },
    {
        label: '车商ID',
        key: 'id'
    },
    {
        label: '手机号',
        key: 'mobilePhone'
    },
    {
        label: '负责人',
        key: 'leaderName'
    },
    {
        label: '职位',
        key: 'job'
    },
    {
        label: '微信互通',
        key: 'wxStatus'
    },
    {
        label: '负责人身份证号码',
        key: 'idCardNo'
    },
    {
        label: '注册时间',
        key: 'registerTime'
    },
    {
        label: '经营地',
        key: 'businessCity'
    },
    {
        label: '联系地址',
        key: 'businessAddress'
    },
    {
        label: '商户图片',
        key: 'dealerInfoPhoto',
        typeo: 'img'
    },
    {
        label: '经营性质',
        key: 'businessProperty'
    }
];

/**
 * 经销商数据指标统计
 * @type {Array}
 */
export const STATISTICS_TABLE = [
    {
        label: '出价',
        key: ['yesterdayBid', 'todayBid', 'monthBid']
    },
    {
        label: '中标',
        key: ['yesterdayWinBid', 'todayWinBid', 'monthWinBid']
    },
    {
        label: '邀约到店',
        key: ['', 'todayArriveShop', 'monthArriveShop']
    },
    {
        label: '登录',
        key: ['loginYesterday', 'loginToday', 'loginMonth']
    },
    {
        label: '拜访',
        key: ['', 'todayVisit', 'monthVisit']
    },
    {
        label: '违约',
        key: ['yesterdayPenaltyNum', 'todayPenaltyNum', 'monthPenaltyNum']
    },
    {
        label: '推荐车源',
        key: ['', 'todayRecommendedCarSource', 'monthRecommendedCarSource']
    },
    {
        label: '推荐出价量',
        key: ['', 'todayRecommendedBidNum', 'monthRecommendedBidNum']
    },
    {
        label: '成交',
        key: ['', 'todayTransaction', 'monthTransaction']
    }
];
