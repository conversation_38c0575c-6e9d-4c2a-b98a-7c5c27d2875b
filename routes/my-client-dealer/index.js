// JS
import React, { memo, useEffect, useMemo, useState } from 'react';
import { connect } from 'dva';
import { useToggle, useSetState } from 'ahooks';
import cls from 'classnames';
import { getUrlQueryObject, isEmpty } from 'ttp-utils';
import { uploadReport } from 'common/js/utils.js';
import { PORT_URL } from 'common/js/constant.js';
import { BUY_VIP_MAP } from './constant';
// 组件
import { Loader, Empty, OutCall, Button, message } from 'ttp-library';
import Tag from 'components/tag';
import WebViewDrawer from 'components/web-view/index.js';
import BaseInfo from './components/base-info';
import StatisticsTable from './components/statistics-table';
import FollowUpDrawer from '../my-client/components/follow-up-drawer';
import RecommendDrawer from '../my-client/components/recommend-drawer';
// LESS
import styles from './index.less';

/**
 * 我的客户
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const MyClient = memo((props) => {
    // props
    const { dispatch, loading, detailData } = props;
    const isLoading = loading.effects['myClientDealer/getDealerDetailData'];
    // state
    const [auctionUrl, setAuctionUrl] = useState('');
    const [outCallPort, setOutCallPort] = useSetState({ url: PORT_URL.outCall, method: 'POST' });
    // 弹窗开关
    const [openCall, { setLeft: setCloseCall, setRight: setOpenCall }] = useToggle(false);
    const [openDrawer, { setLeft: setCloseDrawer, setRight: setOpenDrawer }] = useToggle(false);
    const [openRecDrawer, { setLeft: setCloseRecDrawer, setRight: setOpenRecDrawer }] = useToggle(false);
    const [openWebview, { setLeft: setCloseWebview, setRight: setOpenWebview }] = useToggle(false);
    // 常量
    const { dealerId, from } = getUrlQueryObject();
    // 计算属性
    // 来源是否是“推荐历史”页面
    const isFromRecHistory = useMemo(() => {
        return from === 'recommendHistory' || from === 'recommendToday';
    }, [from]);
    // 拨号样式
    const callClass = useMemo(() => {
        return cls(styles['dealer-detail-footer-call-btn'], { [styles.block]: isFromRecHistory });
    }, [isFromRecHistory]);

    /**
     * 90天内不再跟进
     */
    const handleNoFollowUpClick = () => {
        // 90天内不再跟进
        setOpenDrawer();
    };

    /**
     * 推荐车源
     */
    const handleRecommendClick = () => {
        // 推荐车源
        setOpenRecDrawer();
    };

    /**
     * 拨打电话
     */
    const handleCallClick = () => {
        // 拨打电话
        setOutCallPort({ data: { dealerId } });
        setOpenCall();
        uploadReport('myClientDealerCall', '历史推荐拨打车商电话', { dealerId });
    };

    /**
     * 拨打电话成功 回调
     */
    const handleOutCallSucess = () => {
        message('拨打电话成功，请注意接听回拨电话。', 3);
    };

    /**
     * 推荐车源点击事件，打开车源详情
     * @param {string}           url 车源URL
     */
    const handelItemClick = (url) => {
        setAuctionUrl(decodeURIComponent(url));
        setOpenWebview();
    };

    // useEffect
    // 获取车商数据 // 获取标签列表
    useEffect(() => {
        if (isEmpty(dealerId)) {
            message('缺少必要参数，经销商ID。', 3);
            return;
        }
        dispatch({
            type: 'myClientDealer/getDealerDetailData',
            payload: { dealerId }
        });
    }, []);

    return (
        <>
            <Loader maskOpacity={0.6} open={isLoading} />
            {isEmpty(detailData) || isLoading ? (
                <Empty />
            ) : (
                <div className={styles['dealer-detail']}>
                    <h2 className={styles['dealer-detail-title']}>
                        <span className={styles['dealer-detail-title-text']}> {detailData.companyName}</span>
                        <span className={styles['dealer-detail-title-text']}> （{dealerId}）</span>
                        {/* 是否可竞拍（可竞拍，不可竞拍） */}
                        {isEmpty(detailData.canAuction) ? null : (
                            <Tag themeColor={detailData.canAuction == '可竞拍' ? 'success' : 'danger'}>
                                {detailData.canAuction}
                            </Tag>
                        )}
                        {/* 是否购买增值服务包（1：购买，0：未购买） */}
                        {isEmpty(detailData.isBuyEquityDeposit) ? null : (
                            <Tag themeColor={BUY_VIP_MAP[detailData.isBuyEquityDeposit].type}>
                                {BUY_VIP_MAP[detailData.isBuyEquityDeposit].label}
                            </Tag>
                        )}
                        {/* 会员等级名称 */}
                        {isEmpty(detailData.membershipName) ? null : (
                            <Tag themeColor='info'>{detailData.membershipName}</Tag>
                        )}
                    </h2>
                    {/* 基本信息 */}
                    <BaseInfo itemData={detailData} />
                    {/* 业务数据 */}
                    <StatisticsTable itemData={detailData.dealerDetailsDTO} />
                    {/* 底部按钮 */}
                    <div className={styles['dealer-detail-footer']}>
                        {!isFromRecHistory && (
                            <Button
                                block
                                className={styles['dealer-detail-footer-follow-btn']}
                                loading={isLoading}
                                onClick={handleNoFollowUpClick}
                                skin='white'>
                                90天不再跟进
                            </Button>
                        )}
                        {!isFromRecHistory && (
                            <Button
                                block
                                className={styles['dealer-detail-footer-rec-btn']}
                                loading={isLoading}
                                onClick={handleRecommendClick}
                                skin='white'>
                                推荐车源
                            </Button>
                        )}
                        <Button block className={callClass} loading={isLoading} onClick={handleCallClick}>
                            拨打电话
                        </Button>
                    </div>
                </div>
            )}
            {/* 电话 */}
            <OutCall
                autoCall={false}
                onClose={setCloseCall}
                onFetchData={handleOutCallSucess}
                open={openCall}
                port={outCallPort}
            />
            {/* 90天内不再跟进 */}
            <FollowUpDrawer item={detailData} onClose={setCloseDrawer} open={openDrawer} />
            {/* 车源推荐 */}
            <RecommendDrawer
                dealerId={dealerId}
                onClose={setCloseRecDrawer}
                onItemClick={handelItemClick}
                open={openRecDrawer}
            />
            {/* 车源详情 */}
            <WebViewDrawer
                iframeURL={auctionUrl}
                onClose={setCloseWebview}
                open={openWebview}
                title='检测报告'
            />
        </>
    );
});

export default connect(({ myClientDealer, loading }) => ({ ...myClientDealer, loading }))(MyClient);
