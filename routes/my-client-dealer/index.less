@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';

.dealer-detail {
    padding-bottom: 90 / @rem;

    &-title {
        width: 100%;
        min-height: 50 / @rem;
        margin: 0 auto;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        padding: (10 / @rem) (20 / @rem);
        box-sizing: border-box;

        &-text {
            font-size: 16 / @rem;
            line-height: 30 / @rem;
            color: @color-black;
            font-weight: bold;
        }
    }

    &-footer {
        width: 375 / @rem;
        padding: 0 (13 / @rem);
        height: 74 / @rem;
        background-color: @color-white;
        display: flex;
        justify-content: space-between;
        align-items: center;
        align-content: center;
        position: fixed;
        z-index: 10;
        left: 50%;
        bottom: 0;
        margin-left: -187 / @rem;
        border-width: 0px;

        &-follow-btn,
        &-rec-btn {
            width: 108 / @rem;
            height: 44 / @rem;
            border: 1px solid @color-gray-9;
            color: @color-gray-6;
        }

        &-follow-btn {
            width: 127 / @rem;
        }

        &-call-btn {
            width: 86 / @rem;
            height: 44 / @rem;

            &.block {
                width: 100%;
            }
        }
    }
}
