/**
 * 我的客户-车商详情
 */
import { isEmpty } from 'ttp-utils';
import * as api from './api.js';

/**
 * 默认参数
 * @returns {object}   默认参数
 */
const initState = () => ({
    // 详情数据
    detailData: {}
});

export default {
    namespace: 'myClientDealer',

    state: initState(),

    reducers: {
        /**
         * 重置状态
         * @param   {object}               state 当前state
         * @returns {object}                     更新过的state
         */
        resetState(state) {
            return {
                ...initState(),
                tagList: state.tagList
            };
        },

        /**
         * 更新状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateState(state, action) {
            return {
                ...state,
                ...action.payload
            };
        }
    },

    effects: {
        /**
         * 获取商家数据
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *getDealerDetailData({ payload }, { call, put }) {
            const { result } = yield call(api.getDealerDetail, payload);

            yield put({
                type: 'updateState',
                payload: {
                    detailData: {
                        ...result,
                        dealerId: result.id,
                        dealerName: result.companyName,
                        businessCity:
                            result.province == result.city
                                ? `${result.province || ''}`
                                : `${result.province || ''}${isEmpty(result.city) ? '' : ' ' + result.city}`,
                        businessAddress: result.address
                    }
                }
            });
        }
    }
};
