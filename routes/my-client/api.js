/**
 * @file 我的客户-我的客户
 */

/* requires JS */
import { postRequest } from 'common/js/fetch.js';
import { PORT_URL } from 'common/js/constant.js';
import { formatPostData } from 'common/js/utils.js';

/**
 * 获取商户签到信息数据
 * <AUTHOR>
 * @param    {object}                 data          请求参数对象
 * @returns  {Promise}                              返回一个Feach的Promise对象
 */
export function getDealerLabelList(data) {
    let opts = {
        url: PORT_URL.labelCode,
        data
    };
    return postRequest(opts);
}

/**
 * 获取商户信息数据
 * <AUTHOR>
 * @param    {object}                 data                 请求参数对象
 * @param    {number}                 data.currentPage     当前页面,从1开始
 * @param    {number}                 data.pageSize        分页大小
 * @param    {number}                 data.parentLabelCode 经销商对应 的客户分类 10:潜在客户, 20:纯新客户, 30:成交客户, 40:激活客户
 * @param    {number}                 data.childLabelCode  经销商对应 的子标签集合
 * @returns  {Promise}                                        返回一个Feach的Promise对象
 */
export function getDealerList(data) {
    let opts = {
        url: PORT_URL.dealerList,
        data: formatPostData(data)
    };
    return postRequest(opts);
}

/**
 * 90天不再跟进
 * <AUTHOR>
 * @param    {object}                 data                     请求参数对象
 * @param    {number}                 data.dealerId            经销商id
 * @param    {number}                 data.dealerName          经销商名称
 * @param    {number}                 data.abandonFollowReason 放弃跟进的原因
 * @returns  {Promise}                                         返回一个Feach的Promise对象
 */
export function saveAbandonFollow(data) {
    let opts = {
        url: PORT_URL.saveAbandonFollow,
        data
    };
    return postRequest(opts);
}

/**
 * [车源推荐详情页列表]
 *
 * @param    {object}                data 				[参数对象]
 * @param    {string}                data.dealerId 	    [商户id]
 * @param    {string}                data.currentPage   [当前页]
 * @param    {string}                data.orderRule     [排序规则]
 * @returns   {Promise}                     				[description]
 */
export function getRecommenList(data) {
    let opts = {
        url: PORT_URL.recommendDetailList,
        data
    };
    return postRequest(opts).then(({ result }) => result?.dataList || []);
}

/**
 * [车源推荐详情页列表]
 *
 * @param    {object}                data 				[参数对象]
 * @param    {string}                data.dealerId 	    [商户id]
 * @param    {string}                data.currentPage   [当前页]
 * @param    {string}                data.orderRule     [排序规则]
 * @returns   {Promise}                     				[description]
 */
export function saveRecommenList(data) {
    let opts = {
        url: PORT_URL.saveRecommendList,
        data
    };
    return postRequest(opts);
}
