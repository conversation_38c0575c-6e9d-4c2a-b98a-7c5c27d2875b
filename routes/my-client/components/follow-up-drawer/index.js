// JS
import React, { memo, useState } from 'react';
import { isEmpty, executeCB } from 'ttp-utils';
import { useRequest, useEventTarget } from 'ahooks';
import { saveAbandonFollow } from '../../api.js';
// 组件
import { Drawer, Textarea, Button, message, Icon } from 'ttp-library';
import FormItemErrorTip from 'components/form-item-error-tip/index.js';
// LESS
import styles from './index.less';

/**
 * 90天不在跟进弹窗
 */
const FollowUpDrawer = memo((props) => {
    // props
    const { open, item, onClose, title = '' } = props;
    // state
    const [value, { onChange, reset }] = useEventTarget({ initialValue: '' });
    const [errorMessage, setErrorMessage] = useState('');
    // hooks
    const { loading, runAsync } = useRequest(saveAbandonFollow, {
        manual: true,
        debounceWait: 300
    });

    /**
     * 关闭Drawer
     */
    const handleClose = () => {
        reset();
        setErrorMessage('');
        executeCB(onClose);
    };

    /**
     * 放弃原因
     * @param {event} e event
     */
    const handleChange = (e) => {
        onChange(e);
        setErrorMessage('');
    };

    /**
     * 提交
     */
    const handleSubmit = () => {
        const { dealerId, dealerName } = item;
        const options = { dealerId, dealerName, abandonFollowReason: value.trim() };
        if (isEmpty(value)) {
            setErrorMessage('请输入放弃原因');
            return;
        }
        runAsync(options)
            .then((res) => {
                message(res.message || '“确认成功！预计5分钟后生效”', 2);
                handleClose();
            })
            .catch((error) => {
                message(error.message, 3);
            });
    };

    // 常量
    const leftNode = <Icon className={styles['drawer-close']} onClick={handleClose} type='iconfont-close' />;

    return (
        <Drawer
            className={styles['follow-up-drawer']}
            destroy={false}
            full={false}
            hash='follow-up-drawer'
            left={leftNode}
            maskOpacity={0.5}
            mode='top'
            onClose={handleClose}
            open={open}
            title={title}>
            <div className={styles['drawer-body']}>
                <h2 className={styles['drawer-title']}>确认放弃跟进此客户？</h2>
                <p className={styles['drawer-subtitle']}>放弃后此客户90天内将不在您的列表展示</p>
                <p className={styles['drawer-label']}>放弃原因：</p>
                <div className={styles['drawer-value']}>
                    <Textarea
                        className={styles['drawer-textarea']}
                        maxLength={50}
                        onChange={handleChange}
                        placeholder='请输入放弃原因（必填）'
                        value={value}
                    />
                    <span className={styles['input-textarea-maxlength']}>{50 - value.length}个字</span>
                    <FormItemErrorTip classname={styles['drawer-error-msg']} message={errorMessage} />
                </div>

                <Button
                    block
                    className={styles['drawer-submit-btn']}
                    loading={loading}
                    onClick={handleSubmit}>
                    确定
                </Button>
            </div>
        </Drawer>
    );
});

export default FollowUpDrawer;
