@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';

.follow-up-drawer {
    :global {
        .Saitama-drawer-box {
            border-radius: 0 0 (8 / @rem) (8 / @rem);
        }
    }

    .drawer-close {
        font-size: 14 / @rem !important;
    }

    .drawer-body {
        padding: 0 (20 / @rem) (20 / @rem) (20 / @rem);
    }

    .drawer-title,
    .drawer-subtitle {
        font-size: 14 / @rem;
        color: @color-gray-3;
        text-align: center;
        line-height: 2;
        color: @color-gray-3;
    }

    .drawer-title {
        font-weight: 600;
        font-size: 18 / @rem;
    }

    .drawer-label,
    .drawer-value {
        width: 100%;
        margin-top: 10 / @rem;
        position: relative;
    }

    .drawer-textarea {
        color: @color-gray-3;
        padding: 10 / @rem;
        width: 100%;
        height: 120 / @rem;
        background: @color-gray-drop;
        border-radius: 6 / @rem;
        border-width: 0;
    }

    .drawer-error-msg {
        left: 20px / @rem;
        right: none;
    }

    .drawer-submit-btn {
        margin-top: 20 / @rem;
    }

    .input-textarea-maxlength {
        position: absolute;
        bottom: 10 / @rem;
        right: 10 / @rem;
        font-weight: 400;
        font-size: 12 / @rem;
        color: #999999;
        line-height: 17 / @rem;
        text-align: left;
    }
}
