// JS
import React, { memo } from 'react';
import { executeCB } from 'ttp-utils';
// 组件
import { Drawer } from 'ttp-library';
// LESS
import styles from './index.less';

/**
 * 我的客户数据ITEM
 * @param   {object}                     props props
 * @returns {React.ReactElement}               jsx
 */
const LabelIntroduce = memo((props) => {
    // props
    const { open } = props;

    /**
     * 关闭抽屉
     */
    const handleClose = () => {
        executeCB(props.onClose);
    };
    return (
        <Drawer
            className={styles['drawer-introduce']}
            destroy={false}
            full
            hash='drawer-introduce'
            mode='right'
            onClose={handleClose}
            open={open}
            title='客户分类及标签说明'>
            <div className={styles['desc-content']}>
                <p className={styles['desc-title']}>1.客户层级</p>
                <p>C2B商户按照月均成交量，分为以下几个层级： </p>
                <ul>
                    <li>S级：每个月成交 5台及以上。</li>
                    <li>A级：每个月成交 2-4台。</li>
                    <li>B级：每个月成交 1台及以下。</li>
                </ul>
                <p className={styles['desc-title']}>2.客户分类</p>
                <ul>
                    <li>潜在客户：真实二手车商且未充值保证金2000元。</li>
                    <li>纯新客户：已充值保证金2000元且历史未提车。</li>
                    <li>成交客户：近7天内保证金达到2000元且历史有提车记录。</li>
                    <li>激活客户：退保达到90天且历史有提车记录。</li>
                </ul>
                <p className={styles['desc-title']}>3.客户标签</p>
                <p>客户标签与客户分类是多对1的关系，详细说明如下： </p>
                <table className={styles['desc-table']}>
                    <thead>
                        <tr>
                            <th width='25%'>客户分类</th>
                            <th width='28%'>标签</th>
                            <th>标签对应的指标范围</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td rowSpan='4'>潜在客户</td>
                            <td>活跃度高</td>
                            <td>7天内达到3天登录APP</td>
                        </tr>
                        <tr>
                            <td>活跃度低</td>
                            <td>7天内未登录APP</td>
                        </tr>
                        <tr>
                            <td>深度浏览</td>
                            <td>7天内浏览车源超过100台</td>
                        </tr>
                        <tr>
                            <td>近期登录</td>
                            <td>近3天内有登录</td>
                        </tr>
                        <tr>
                            <td rowSpan='5'>纯新客户</td>
                            <td>高频类</td>
                            <td>7天内出价天数达到3天</td>
                        </tr>
                        <tr>
                            <td>低频类</td>
                            <td>7天内出价天数为0天</td>
                        </tr>
                        <tr>
                            <td>普通股</td>
                            <td>7天中标次数小于2次</td>
                        </tr>
                        <tr>
                            <td>潜力股</td>
                            <td>7天内中标达到10次及以上</td>
                        </tr>
                        <tr>
                            <td>待促成</td>
                            <td>7天内中标达到15次未成交</td>
                        </tr>
                        <tr>
                            <td rowSpan='8'>成交客户</td>
                            <td>高价值</td>
                            <td>历史提车达到100台</td>
                        </tr>
                        <tr>
                            <td>忠诚客户</td>
                            <td>近3个月连续提车</td>
                        </tr>
                        <tr>
                            <td>待激活</td>
                            <td>近60天未提车</td>
                        </tr>
                        <tr>
                            <td>逾期客户</td>
                            <td>近30天内累计过户逾期达到3台</td>
                        </tr>
                        <tr>
                            <td>高风险客户</td>
                            <td>近30天内扣罚保证金达到1.5万元</td>
                        </tr>
                        <tr>
                            <td>重点维护</td>
                            <td>近7天累计中标达到20台未成交</td>
                        </tr>
                        <tr>
                            <td>待出价</td>
                            <td>近15天未出价</td>
                        </tr>
                        <tr>
                            <td>激活可竞拍</td>
                            <td>保证金低于2000元的天数达到7天</td>
                        </tr>
                        <tr>
                            <td rowSpan='3'>激活客户</td>
                            <td>高价值</td>
                            <td>历史提车达到30台</td>
                        </tr>
                        <tr>
                            <td>活跃度高</td>
                            <td>7天内达到3天登录APP</td>
                        </tr>
                        <tr>
                            <td>活跃度低</td>
                            <td>7天内未登录APP</td>
                        </tr>
                    </tbody>
                </table>
                <p className={styles['desc-title']}>4.默认排序说明</p>
                <p>我的客户列表默认按未跟进天数倒序排列</p>
            </div>
        </Drawer>
    );
});

export default LabelIntroduce;
