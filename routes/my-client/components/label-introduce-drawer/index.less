@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';
.drawer-introduce {
    color: @color-gray-3;
    .desc-title {
        font-weight: 500;
        color: #000;
        font-size: 15 / @rem;
        margin-bottom: 10 / @rem;
    }

    .desc-content {
        padding: 15 / @rem;
        font-size: 13 / @rem;
    }

    ul {
        list-style-type: disc; // 原点样式
        padding-left: 20 / @rem; // 调整左侧缩进，使原点与文本对齐
        li {
            font-size: 13 / @rem;
            color: @color-gray-3;
            margin-bottom: 10 / @rem;
            list-style-type: disc;
            display: list-item;
            margin-left: 5 / @rem;
        }
    }
    p {
        margin-bottom: 10 / @rem;
    }

    .desc-table {
        border: 1px solid #ddd;
        border-collapse: collapse;
        margin-bottom: 10 / @rem;
        th {
            font-weight: 500;
            text-align: left;
        }
        td,
        th {
            padding: 7 / @rem;
            font-size: 13 / @rem;
            border: 1px solid #ddd;
        }
    }
}
