// JS
import React, { memo, useMemo, useCallback } from 'react';
import { useToggle } from 'ahooks';
import { executeCB, isEmpty } from 'ttp-utils';
import { formatTimeData } from 'common/js/utils';
import cls from 'classnames';
// 组件
import { List, Button } from 'ttp-library';
import Panel from 'components/panel';
import Tag from 'components/tag';
// LESS
import styles from './index.less';

/**
 * 客户层级
 * @type {object}
 */
const LAVE_CODE_MAP = {
    S级: 'S',
    A级: 'A',
    B级: 'B'
};

/**
 * 跟进状态: 0 跟进时间到期未跟进(红点提醒), 1 跟进时间到期前已跟进
 * @type {object}
 */
const FOLLOW_STATUS_MAP = {
    0: true,
    1: false
};

/**
 * 客户跟进TAG
 * @param   {object}                     props props
 * @returns {React.ReactElement}               jsx
 */
const ItemTagList = memo((props) => {
    // props
    const { itemData, onFollowUp } = props;
    // 常量
    const btnClass = cls(styles['tag-btn']);

    /**
     * 90天内不再跟进
     */
    const handleNoFollowUpClick = useCallback(() => {
        executeCB(onFollowUp, itemData, 'noFollowUp');
    }, [onFollowUp, itemData]);

    /**
     * 推荐车源
     */
    const handleRecommendClick = useCallback(() => {
        executeCB(onFollowUp, itemData, 'recommend');
    }, [onFollowUp, itemData]);

    /**
     * 拨打电话
     */
    const handleCallClick = useCallback(() => {
        executeCB(onFollowUp, itemData, 'call');
    }, [onFollowUp, itemData]);

    return (
        <>
            <Button onClick={handleNoFollowUpClick} size='sm' skin='white'>
                90天不再跟进
            </Button>
            <Button className={btnClass} onClick={handleRecommendClick} size='sm' skin='white'>
                推荐车源
            </Button>
            <Button className={btnClass} onClick={handleCallClick} size='sm' skin='white'>
                打电话
            </Button>
        </>
    );
});

/**
 * 我的客户数据ITEM
 * @param   {object}                     props props
 * @returns {React.ReactElement}               jsx
 */
const ListItem = memo((props) => {
    // props
    const { index, itemData, onFollowUp } = props;
    // state
    const [open, { toggle }] = useToggle(index == 0);

    // 计算属性
    // 主标题
    const title = useMemo(() => {
        const lave = LAVE_CODE_MAP[itemData.customerLevelCode];
        return (
            <span className={styles['list-item-title']}>
                <i className={`icon-${lave}`}>{lave}</i>
                <strong>姓名(车商ID)</strong>
            </span>
        );
    }, [itemData.customerLevelCode]);
    // 副标题
    const subtitle = useMemo(
        () => (
            <>
                <span className={styles['list-item-title-prev']}>{itemData.dealerName}</span>
                <span>({itemData.dealerId})</span>
            </>
        ),
        [itemData.dealerId, itemData.dealerName]
    );

    /**
     * 查看跟进记录
     */
    const handleViewRecordClick = useCallback(() => {
        executeCB(onFollowUp, itemData, 'viewRecord');
    }, [onFollowUp, itemData]);

    /**
     * 跳转到商家详情
     */
    const handleJump = useCallback(() => {
        console.log('handleJump');
        executeCB(onFollowUp, itemData, 'jump');
    }, [onFollowUp, itemData]);

    // 查看商家详情 按钮
    const listFooter = (
        <div className={styles['list-item-footer']} onClick={handleJump}>
            查看商家详情 &gt;
        </div>
    );

    return (
        <div className={styles['list-item']}>
            {FOLLOW_STATUS_MAP[itemData.followStatus] && <i className={styles['list-item-icon-red']} />}
            <Panel onClick={toggle} open={open} subtitle={subtitle} title={title}>
                <List className={styles['list-box']} footer={listFooter}>
                    <List.Item
                        className={styles['list-box-item']}
                        extra={
                            !isEmpty(itemData.childLabelName) ? <Tag>{itemData.childLabelName}</Tag> : '--'
                        }>
                        客户标签
                    </List.Item>
                    <List.Item className={styles['list-box-item']} extra={itemData.mobilePhone}>
                        联系电话
                    </List.Item>
                    <List.Item
                        className={styles['list-box-item']}
                        extra={
                            itemData.provinceName +
                            (itemData.zoneName == itemData.provinceName ? '' : itemData.zoneName)
                        }>
                        经营地
                    </List.Item>
                    <List.Item className={styles['list-box-item']} extra={itemData.margin.toFixed(2)}>
                        保证金余额
                    </List.Item>
                    <List.Item
                        className={styles['list-box-item']}
                        extra={formatTimeData(itemData.registerTime, 'yyyy-mm-dd')}>
                        注册时间
                    </List.Item>
                    <List.Item className={styles['list-box-item']} extra={itemData.latestBehaviorDesc}>
                        末次关键行为
                    </List.Item>
                    <List.Item
                        className={styles['list-box-item']}
                        extra={formatTimeData(itemData.latestBehaviorTime)}>
                        末次行为时间
                    </List.Item>
                    <List.Item
                        className={styles['list-box-item']}
                        extra={formatTimeData(itemData.latestFollowTime)}>
                        末次跟进时间
                    </List.Item>
                    <List.Item className={styles['list-box-item']} extra={itemData.noFollowDays}>
                        未跟进天数
                    </List.Item>
                    <List.Item
                        className={styles['list-box-item']}
                        extra={<ItemTagList itemData={itemData} onFollowUp={onFollowUp} />}>
                        跟进
                    </List.Item>
                    <List.Item
                        className={styles['list-box-item']}
                        extra={
                            <Button mode='link' onClick={handleViewRecordClick} size='sm' skin='blue'>
                                查看记录 &gt;
                            </Button>
                        }>
                        跟进记录
                    </List.Item>
                </List>
            </Panel>
        </div>
    );
});

export default ListItem;
