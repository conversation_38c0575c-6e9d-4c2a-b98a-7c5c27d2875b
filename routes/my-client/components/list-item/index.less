@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';
@import '../../../../common/less/list.less';

.list-item {
    margin-bottom: 10 / @rem;
    position: relative;

    &-title {
        display: flex;
        align-items: center;
    }

    &-title-prev {
        display: inline-block;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        max-width: 100 / @rem;
        text-align: right;
    }

    &-icon-red {
        width: 10 / @rem;
        height: 10 / @rem;
        background: @color-red;
        border: 1px solid @color-white;
        border-radius: 5 / @rem;
        position: absolute;
        left: 0 / @rem;
        top: 0 / @rem;
        z-index: 10;
    }

    .list-box {
        margin: 0;
        position: relative;

        &:after {
            .setTopLine(@color-border);
        }

        &-item {
            padding: (10 / @rem) (5 / @rem);
            min-height: 40 / @rem;
            overflow: inherit;

            &:after {
                .setBottomLine(@color-border);
            }

            &:last-child:after {
                border-bottom-width: 0px;
            }
        }
    }

    .tag-btn {
        margin-left: 5 / @rem;
        padding: 0 (5 / @rem);
    }

    &-footer {
        width: 100%;
        text-align: center;
        font-weight: 400;
        font-size: 14 / @rem;
        line-height: 1.75;
        color: @color-gray-3;
    }
}
