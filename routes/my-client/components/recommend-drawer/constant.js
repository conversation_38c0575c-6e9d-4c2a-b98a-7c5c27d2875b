/* 样式 */
export const AUCTION_LABEL_CN = {
    1: '重点车源',
    2: '未充分竞价',
    3: '极少出价'
};

/* 拍类型对应的标签 */
export const AUCTION_LABEL_TAG_CLS = {
    1: 'icon-recommend-tag-blue',
    2: 'icon-recommend-tag-yellow',
    3: 'icon-recommend-tag-red'
};

/* 拍类型 */
export const PAI_TYPE = {
    // 普通拍
    NORMAL: 1,
    // 加价拍
    MARKUP: 10,
    // 一拍即中
    ONEHIT: 2,
    // 延时拍预期
    TIMELAPSE: 11,
    // 延时拍正式
    PB_TIMELAPSE: 111
};

/* 拍类型对应的倒计时文案 */
export const PAI_TYPE_COUNT_DOWN_CN = {
    [PAI_TYPE.NORMAL]: '竞拍中',
    [PAI_TYPE.MARKUP]: '加价中',
    [PAI_TYPE.ONEHIT]: '竞拍中',
    [PAI_TYPE.TIMELAPSE]: '预拍中',
    [PAI_TYPE.PB_TIMELAPSE]: '加价中'
};

/**
 * 拍类型对应的价格
 * @param    {number}                 p                   拍价
 * @returns  {object}                                     拍价
 */
export const PAI_TYPE_PRICE_CN = (p) => {
    return {
        [PAI_TYPE.NORMAL]: '',
        [PAI_TYPE.MARKUP]: `当前最高 ¥${p}元`,
        [PAI_TYPE.ONEHIT]: `秒杀价 ¥${p}元`,
        [PAI_TYPE.TIMELAPSE]: '',
        [PAI_TYPE.PB_TIMELAPSE]: `当前最高 ¥${p}元`
    };
};
