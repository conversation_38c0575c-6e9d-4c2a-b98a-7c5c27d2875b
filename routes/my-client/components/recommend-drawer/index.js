// JS
import React, { memo, useEffect, useState } from 'react';
import { isEmpty, executeCB } from 'ttp-utils';
import { useRequest, useSet } from 'ahooks';
import { getRecommenList, saveRecommenList } from '../../api.js';
// 组件
import { Drawer, Button, message, Icon, Empty, BetterPullBox, Loader } from 'ttp-library';
import ListItem from './list-item';
// LESS
import styles from './index.less';

/**
 * 推荐车源弹窗
 * @param  {object}          props            props
 * @param  {Array}           props.auctionIds 选中车源ID
 */
const ButtonText = memo(({ auctionIds }) => {
    let text = '批量推荐';
    let status = '';
    if (auctionIds.size > 0) {
        status = `(共${auctionIds.size}台车)`;
    }
    return text + status;
});

/**
 * 推荐车源弹窗
 */
const EecommendDrawer = memo((props) => {
    // props
    const { dealerId, open, onClose, onItemClick } = props;
    // state
    const [pullboxRefreshKey, setPullboxRefreshKey] = useState(Date.now());
    const [preAuctionId, setPreAuctionId] = useState('');
    const [auctionIds, setAuctionIds] = useState([]);
    const [selectIds, { add, remove, reset }] = useSet([]);
    const [current, setCurrent] = useState(1);
    // hooks
    const { loading: listLoading, runAsync: ListRunAsync } = useRequest(getRecommenList, { manual: true });
    const { loading, runAsync } = useRequest(saveRecommenList, { manual: true });

    /**
     * 关闭Drawer
     */
    const handleClose = () => {
        reset();
        executeCB(onClose);
    };

    /**
     * 全选
     */
    const handleSelectALl = () => {
        if (auctionIds.length == selectIds.size) {
            reset();
        } else {
            auctionIds.forEach((id) => {
                add(id);
            });
        }
    };

    /**
     * 选择
     * @param {number}          auctionId auctionId
     */
    const handleSelect = (auctionId) => {
        if (selectIds.has(auctionId)) {
            remove(auctionId);
        } else {
            add(auctionId);
        }
    };

    /**
     * 提交
     */
    const handleSubmit = () => {
        if (selectIds.size == 0) {
            message('请选择至少1个推荐商品。');
            return;
        }
        const options = {
            dealerId,
            auctionIds: [...selectIds]
        };
        runAsync(options)
            .then((res) => {
                message(res.message || `共成功推荐${selectIds.size}台车。`, 3);
                handleClose();
            })
            .catch((error) => {
                message(error.message, 3);
            });
    };

    /**
     * 加载更多
     * @param   {object}            param         入参
     * @param   {boolean}           param.isReset 是否下拉刷新，true  则重置数据
     * @returns {Promise}                         Promise
     */
    const onLoadMore = ({ isReset = false }) => {
        const currentPage = isReset ? 1 : current + 1;
        return ListRunAsync({ dealerId, currentPage, pageSize: 10 }).then((dataList) => {
            setCurrent(currentPage);
            setAuctionIds(dataList.map((item) => item.auctionId));
            return dataList;
        });
    };

    /**
     * 使用虚拟列表的情况
     *
     * @param   {object}               item 列表每一项的数据
     * @returns {React.ReactElement}        JSX
     */
    const rowRender = (item) => {
        // 当无数据时，rowItem默认为{}
        if (isEmpty(item)) {
            return <Empty />;
        }
        const selected = selectIds.has(item.auctionId);
        return (
            <ListItem
                hideCheckbox={false}
                item={item}
                onClick={onItemClick}
                onSelect={handleSelect}
                selected={selected}
            />
        );
    };

    // JSX DOME
    const leftNode = <Icon className={styles['drawer-close']} onClick={handleClose} type='iconfont-close' />;
    const rightNode = (
        <Button mode='link' onClick={handleSelectALl} skin='blue'>
            {auctionIds.length == selectIds.size && selectIds.size != 0 ? '取消全选' : '全选'}
        </Button>
    );

    // useEffect
    useEffect(() => {
        if (open && dealerId !== preAuctionId && !isEmpty(dealerId)) {
            setCurrent(1);
            setPreAuctionId(dealerId);
            setPullboxRefreshKey(Date.now());
        }
    }, [open, dealerId, preAuctionId]);

    return (
        <Drawer
            className={styles['recommend-drawer']}
            destroy={false}
            full={false}
            hash='recommend-drawer'
            left={leftNode}
            maskOpacity={0.5}
            mode='bottom'
            onClose={handleClose}
            open={open}
            right={rightNode}
            title='推荐车源'>
            <Loader maskOpacity={0.6} open={listLoading} type='page' />
            <div className={styles['recommend-drawer-body']}>
                <BetterPullBox
                    key={pullboxRefreshKey}
                    requestData={onLoadMore}
                    rowRender={rowRender}
                    usePullDown
                    usePullUp
                    useVirtual={false}
                />
            </div>
            <div className={styles['recommend-drawer-footer']}>
                <Button
                    block
                    className={styles['recommend-drawer-btn']}
                    loading={loading}
                    onClick={handleClose}
                    skin='white'>
                    取消
                </Button>
                <Button
                    block
                    className={styles['recommend-drawer-btn']}
                    loading={loading}
                    onClick={handleSubmit}>
                    <ButtonText auctionIds={selectIds} />
                </Button>
            </div>
        </Drawer>
    );
});

export default EecommendDrawer;
