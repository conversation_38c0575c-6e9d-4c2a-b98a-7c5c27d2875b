@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';

.recommend-drawer {
    :global {
        .Saitama-drawer-box {
            border-radius: (8 / @rem) (8 / @rem) 0 0;
        }

        .Saitama-list-item {
            padding: 0;
            border-radius: 8 / @rem;
        }

        .Saitama-list-item-content {
            margin-top: 8 / @rem;
        }
    }

    .drawer-close {
        font-size: 14 / @rem !important;
    }

    &-body {
        box-sizing: border-box;
        padding: (10 / @rem) (8 / @rem);
        height: 70vh;
        position: relative;
        background-color: @color-gray-drop;
    }

    &-footer {
        width: 100%;
        padding: 0 (21 / @rem);
        height: 84 / @rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        align-content: center;
    }

    &-btn {
        width: 156 / @rem;
        height: 44 / @rem;
    }

    .list-item {
        background: @color-white;
        padding: 10 / @rem;
        box-sizing: border-box;
        position: relative;
        display: flex;
        align-items: center;
        min-height: 90 / @rem;

        &.container-selected {
            background-color: #f3fdff;
        }

        &-select {
            height: 90 / @rem;
            width: 20 / @rem;
            margin-left: 10 / @rem;
            display: flex;
            align-items: center;
        }

        &-left {
            width: 120 / @rem;
            height: 90 / @rem;
            border-radius: 6 / @rem;
            overflow: hidden;
            position: relative;
        }

        &-photo {
            max-width: 100%;
            max-height: 100%;
        }

        &-photo-tag {
            position: absolute;
            left: 0;
            top: 0;
            color: @color-white;
            font-size: 12 / @rem;
            z-index: 1;
            text-align: center;
        }

        .count-down {
            position: absolute;
            bottom: 0;
            width: 100%;
            height: 17 / @rem;
            line-height: 13 / @rem;
            background: #222;
            opacity: 0.69;
            border-bottom-right-radius: 4 / @rem;
            border-bottom-left-radius: 4 / @rem;
            text-align: center;

            &-notice-color {
                background-color: #f14b36;
            }

            &-time {
                height: 17 / @rem;
                line-height: 17 / @rem;
                color: @color-white;
                font-size: 12 / @rem;
            }
        }

        &-center {
            margin-left: 8 / @rem;
            width: 175 / @rem;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            min-height: 90 / @rem;

            .car-brand {
                font-size: 15 / @rem;
                color: @color-gray-3;
                font-weight: bold;
                display: inline-block;
                line-height: 1.75;
                .text-ellipsis-multi(2);

                .icon-recommend {
                    display: inline-block;
                    margin-right: 5 / @rem;
                    vertical-align: middle;
                }
            }
        }

        .car-property {
            padding: 1 / @rem 5 / @rem;
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            &-text {
                font-size: 12 / @rem;
                line-height: 1.75;
                color: @color-gray-9;
            }
        }

        .car-price {
            color: #ff765a;
            font-size: 14 / @rem;
            font-weight: 500;
            line-height: 1.75;

            .already-offer {
                float: right;
                color: #44ac3b;
                font-size: 14 / @rem;
                line-height: 1.75;
            }
        }
    }

    .bottom {
        padding: 10 / @rem 10 / @rem 0 0;
        .clearfix();

        .recommend-btn {
            float: right;
            font-size: 14 / @rem;
            color: @color-white;
            padding: 0;
            border-radius: 5 / @rem;
            height: 28 / @rem;
            line-height: 28 / @rem;
            width: 74 / @rem;
            background: @color-blue;

            &.recommended {
                background: #f0f0f0;
                color: @color-white;
            }
        }
    }
}
