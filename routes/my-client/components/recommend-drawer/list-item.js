/**
 * 车源推荐详情页列表
 *
 * @requires module:react
 * @requires module:dva
 * @requires module:common/js/constant
 */
/* JS */
import React, { memo, useState, useEffect } from 'react';
import cls from 'classnames';
import { formatTime, executeCB, isEmpty } from 'ttp-utils';
import { replaceUrl } from 'common/js/utils';
import { AUCTION_LABEL_CN, AUCTION_LABEL_TAG_CLS, PAI_TYPE_PRICE_CN } from './constant.js';
// 组件
import { LazyLoad } from 'ttp-library';
import CountDown from './count-down';
// LESS
import styles from './index.less';

/**
 * 标签
 */
const ItemTag = memo(({ auctionLable, importantAuction }) => {
    if (importantAuction) {
        const tagClass = cls(styles['list-item-photo-tag'], 'icon-recommend-tag-bluest');
        return <span className={tagClass}>重点车源</span>;
    }
    if (!isEmpty(AUCTION_LABEL_CN[auctionLable])) {
        const tagClass = cls(styles['list-item-photo-tag'], AUCTION_LABEL_TAG_CLS[auctionLable]);
        return <span className={tagClass}>{AUCTION_LABEL_CN[auctionLable]}</span>;
    }
    return null;
});

/**
 * 列表item
 * @param    {object}                 props           上层传过来的数据
 * @param    {object}                 props.item      车源信息
 * @param    {boolean}                props.selected  是否选中
 * @returns  {React.ReactElement}                     JSX
 */
const ListItem = memo((props) => {
    // props
    const { item, selected, onSelect, onClick } = props;
    // state
    const [baseCountTime, setBaseCountTime] = useState(0);
    // 常量
    // 是否可以被选中
    const selectStyle = cls({
        'icon-select': !selected,
        'icon-selected': selected
    });
    const itemContainer = cls(styles['list-item'], { [styles['container-selected']]: selected });
    const auctionUrl = encodeURIComponent(
        `https://dealerh5.ttpai.cn/client/to-check-report?auctionId=${item.confuseAuctionId}#/`
    );
    const registrationStr = formatTime(item.registration, 'yyyy.mm');
    const mileageStr = item.mileage ? (item.mileage / 10000).toFixed(1) : 0;

    // 函数
    /**
     * 点击选中车源id
     */
    const handleSelect = () => {
        executeCB(onSelect, item.auctionId);
    };

    /**
     * 点击选中车源id
     */
    const handleClick = () => {
        executeCB(onClick, auctionUrl);
    };

    // useEffect
    // 初始化
    useEffect(() => {
        const baseInterval = setInterval(() => setBaseCountTime((pre) => pre + 1), 1000);
        return () => clearInterval(baseInterval);
    }, []);

    return (
        <div className={itemContainer}>
            <div className={styles['list-item-left']}>
                <LazyLoad.LazyImg
                    className={styles['list-item-photo']}
                    src={replaceUrl(item.imageUrl, 320, 240)}
                />
                <ItemTag auctionLable={item.auctionLable} importantAuction={item.importantAuction} />
                <CountDown
                    key={item.auctionId}
                    timeStamp={item.awayFromEnd}
                    timer={baseCountTime}
                    type={item.paiMode}
                />
            </div>
            <div className={styles['list-item-center']} onClick={handleClick}>
                <div className={styles['car-brand']}>
                    {item.isRecommended == 1 && <span className='icon-recommend' />}
                    {`[${item.zoneName}·${item.licence}] ${item.brand} ${item.family} ${item.vehicleModel}`}
                </div>
                <div className={styles['car-property']}>
                    <span className={styles['car-property-text']}>{registrationStr}</span>
                    <span className={styles['car-property-text']}>{mileageStr}万公里</span>
                    {!!item.star && <span className={styles['car-property-text']}>结构件:{item.star}星</span>}
                </div>
                <p className={styles['car-price']}>
                    {PAI_TYPE_PRICE_CN(item.minPrice)[item.paiMode]}
                    {!!item.isBade && <span className={styles['already-offer']}>已出价</span>}
                </p>
            </div>
            <div className={styles['list-item-select']} onClick={handleSelect}>
                <i className={selectStyle} />
            </div>
        </div>
    );
});

export const ButtonText = React.memo(({ auctionIds }) => {
    let text = '批量推荐';
    let status = '';
    if (auctionIds.length > 0) {
        status = `(共${auctionIds.length}台车)`;
    }
    return text + status;
});

export default ListItem;
