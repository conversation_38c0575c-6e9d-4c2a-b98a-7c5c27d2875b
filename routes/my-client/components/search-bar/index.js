// JS
import React, { memo, useState, useRef, useMemo } from 'react';
import { connect } from 'dva';
import { useSetState, useUpdateEffect } from 'ahooks';
import { executeCB, isEmpty, getUrlQueryStringify } from 'ttp-utils';
import { BUY_VIP_OPTION, BUY_VIP_KEY } from 'common/js/constant.js';
import { PAGE_URL } from 'routes/router-config.js';
import cls from 'classnames';
// 组件
import { Button, DropdownMenu, Drawer, Radio } from 'ttp-library';
import SearchBox from 'components/search-box/index.js';
import SearchTagBox from 'components/search-tag-bar/index.js';
import SilderBar from 'components/slider-bar/index.js';
import TagBar from '../tag-bar/index.js';
import RadioTag from 'components/radio-tags/index.js';

const { DropdownItem } = DropdownMenu;
const Group = Radio.Group;
// LESS
import styles from './index.less';

/**
 * 下拉筛选标签默认值
 * @type {Array}
 */
export const FILTER_LABEL_LIST = [
    {
        itemKey: 'labelCode',
        defaultLabel: '全部客户'
    },
    {
        itemKey: 'commonBidStatus',
        defaultLabel: '当前暗拍场是否出价'
    },
    // {
    //     itemKey: 'noBidDays',
    //     defaultLabel: '未出价天数'
    // },
    // {
    //     itemKey: 'getCarCount',
    //     defaultLabel: '提车量'
    // },
    {
        itemKey: 'more',
        defaultLabel: '更多'
    }
];

const FILTER_SOURCE_LIST = {
    commonBidStatus: [
        {
            label: '已出价',
            value: 1
        },
        {
            label: '未出价',
            value: 0
        }
    ]
};

/**
 * 是否已购权益包定金	0 未购买 1 已购买
 * @type  {object}
 */
const BUY_VIP_MAP = {
    [BUY_VIP_KEY.YES]: '已购权益包',
    [BUY_VIP_KEY.NO]: '未购权益包'
};

/**
 * 未出价天数默认值(最小值/最大值)
 * @type {Array}
 */
const defaultNoBidDays = [0, 50];

/**
 * 提车量默认值(最小值/最大值)
 * @type {Array}
 */
const defaultGetCarCount = [0, 50];

/**
 * 保证金默认值(最小值/最大值)
 * @type {Array}
 */
const defaultMargin = [-5000, 2000];

/**
 * 下拉筛选底部按钮
 * @param   {object}         props props
 * @returns {object}               jsx
 */
const DropdownFooter = memo(({ classname = styles['dropdown-footer'], onClose, onSubmit }) => (
    <div className={classname}>
        <Button className={styles['footer-button']} onClick={onClose} skin='white'>
            取消
        </Button>
        <Button className={styles['footer-button']} onClick={onSubmit}>
            确认
        </Button>
    </div>
));

/**
 * 我的客户-搜索条件
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const SearchBar = memo((props) => {
    // props
    const { history, dispatch, classname, tagList, formData, getHeight, barStyle } = props;
    // state
    const [open, setOpen] = useState(false);
    const [searchData, setSearchData] = useSetState({ ...formData });
    const [noBidDays, setNoBidDays] = useState([...defaultNoBidDays]);
    const [getCarCount, setGetCarCount] = useState([...defaultGetCarCount]);
    const [margin, setMargin] = useState([...defaultMargin]);
    // 计算属性
    // 未出价天数
    const noBidDaysTitle = useMemo(() => {
        const [min, max] = noBidDays;
        return `未出价天数：${min}至${max > defaultNoBidDays[1] ? `${defaultNoBidDays[1]}以上` : max + '天'}`;
    }, [noBidDays]);
    // 提车量
    const getCarCountTitle = useMemo(() => {
        const [min, max] = getCarCount;
        return `提车量：${min}至${max > defaultGetCarCount[1] ? `${defaultGetCarCount[1]}以上` : max + '台'}`;
    }, [getCarCount]);
    // 保证金余额
    const marginTitle = useMemo(() => {
        const [min, max] = margin;
        return `保证金余额：${min}至${max > defaultMargin[1] ? `${defaultMargin[1]}以上` : max + '元'}`;
    }, [margin]);
    // 选中的tag数据
    const searchTagsList = useMemo(() => {
        const {
            queryText,
            parentLabelCode,
            childLabelCode,
            noBidDaysMin,
            noBidDaysMax,
            getCarCountMin,
            getCarCountMax,
            equityMember,
            marginMin,
            marginMax,
            commonBidStatus
        } = formData;
        const tags = [];
        if (!isEmpty(queryText)) {
            tags.push({
                key: 'queryText',
                label: queryText,
                clearKey: ['queryText']
            });
        }

        // 经销商标签
        if (!isEmpty(parentLabelCode) || !isEmpty(childLabelCode)) {
            const child = isEmpty(childLabelCode) ? '' : `-${childLabelCode}`;
            tags.push({
                key: 'labelCode',
                label: `${parentLabelCode}${child}`,
                clearKey: ['parentLabelCode', 'childLabelCode']
            });
        }
        // 当前暗拍场是否出价
        if (!isEmpty(commonBidStatus)) {
            tags.push({
                key: 'commonBidStatus',
                label: commonBidStatus == 1 ? '当前暗拍场已出价' : '当前暗拍场未出价',
                clearKey: ['commonBidStatus']
            });
        }
        // 出价天数
        if (!isEmpty(noBidDaysMin) || !isEmpty(noBidDaysMax)) {
            tags.push({
                key: 'noBidDays',
                label: noBidDaysTitle,
                clearKey: ['noBidDaysMin', 'noBidDaysMax']
            });
        }
        // 提车量
        if (!isEmpty(getCarCountMin) || !isEmpty(getCarCountMax)) {
            tags.push({
                key: 'getCarCount',
                label: getCarCountTitle,
                clearKey: ['getCarCountMin', 'getCarCountMax']
            });
        }
        // 是否权益包会员
        if (!isEmpty(equityMember)) {
            tags.push({
                key: 'equityMember',
                label: BUY_VIP_MAP[equityMember],
                clearKey: ['equityMember']
            });
        }
        // 保证金
        if (!isEmpty(marginMin) || !isEmpty(marginMax)) {
            tags.push({
                key: 'margin',
                label: marginTitle,
                clearKey: ['marginMin', 'marginMax']
            });
        }
        return tags;
    }, [formData, noBidDaysTitle, getCarCountTitle, marginTitle]);

    // 常量
    const searchRef = useRef(null);
    const dropRef = useRef(null);
    const searchClasss = cls(styles['search-bar'], classname);
    const drawerContent = cls(styles['drawer-content'], styles['drawer-content__flex']);

    // 函数请求

    /**
     * 获取搜索框高度
     */
    const getSearchHight = () => {
        const { height } = searchRef.current.getBoundingClientRect();
        console.log('getSearchHight', height);
        executeCB(getHeight, height);
    };

    /**
     * 还原state状态
     * @param   {object}               formData  原始筛选条件
     */
    const resetState = (formData) => {
        setSearchData(formData);
        setNoBidDays([
            isEmpty(formData.noBidDaysMin) ? defaultNoBidDays[0] : Number(formData.noBidDaysMin),
            isEmpty(formData.noBidDaysMax) ? defaultNoBidDays[1] + 1 : Number(formData.noBidDaysMax)
        ]);
        setGetCarCount([
            isEmpty(formData.getCarCountMin) ? defaultGetCarCount[0] : Number(formData.getCarCountMin),
            isEmpty(formData.getCarCountMax) ? defaultGetCarCount[1] + 1 : Number(formData.getCarCountMax)
        ]);
        setMargin([
            isEmpty(formData.marginMin) ? defaultMargin[0] : Number(formData.marginMin),
            isEmpty(formData.marginMax) ? defaultMargin[1] + 1000 : Number(formData.marginMax)
        ]);
    };

    /**
     * 获取车商数据
     * @param   {object}               data  请求参数
     */
    const getDealerList = (data = {}) => {
        const searchParamsData = { ...formData, ...data, currentPage: 1 };
        dispatch({
            type: 'myClient/updateFormDataState',
            payload: data
        });
        dispatch({
            type: 'myClient/getDealerList',
            payload: searchParamsData
        });
        // 修改URL的参数，方便回退查看数据
        const { currentPage, pageSize, ...searchParams } = searchParamsData;
        history.replace({
            pathname: PAGE_URL.myClient,
            search: getUrlQueryStringify(searchParams, '?')
        });
    };
    /**
     * 搜索框点击搜索
     * @param {string}         queryText 当前选中数据
     */
    const handleSearchChange = (queryText) => {
        getDealerList({ queryText });
    };

    /**
     * 处理关闭事件
     */
    const handleClose = () => {
        // 关闭下拉状态
        dropRef.current?.closeAllDrawer();
        resetState(formData);
    };

    /**
     * 处理搜索提交事件
     */
    const handleSumbit = () => {
        // 关闭下拉状态
        dropRef.current?.closeAllDrawer();
        getDealerList(searchData);
    };

    /**
     * 标签切换点击
     * @param {object}         item 当前选中数据
     */
    const handleTagChange = (item) => {
        console.log('handleTagChange--item', item, JSON.stringify(searchData));
        const { parentLabelCode, labelCode } = item;
        setSearchData({ parentLabelCode, childLabelCode: labelCode || '' });
    };

    /**
     * 未出价天数-滑块拖动事件
     * @param {Array<number>}         value 当前选中数据
     */
    const handleNObidDaysChange = (value) => {
        console.log('handleNObidDaysChange--value', value);
        setNoBidDays(value);
        setSearchData({
            noBidDaysMin: value[0],
            noBidDaysMax: value[1] > defaultNoBidDays[1] ? '' : value[1]
        });
    };

    /**
     * 提车辆-滑块拖动事件
     * @param {Array<number>}         value 当前选中数据
     */
    const handleGetCarCountChange = (value) => {
        console.log('handlegetCarCountChange--value', value);
        setGetCarCount(value);
        setSearchData({
            getCarCountMin: value[0],
            getCarCountMax: value[1] > defaultGetCarCount[1] ? '' : value[1]
        });
    };

    /**
     * 是否已购权益包定金 radio change事件
     * @param {number}               value 选中值
     */
    const handleEquityMemberChange = (value) => {
        console.log('handleEquityMemberChange--value', value);
        setSearchData({ equityMember: value });
    };

    /**
     * 当前暗拍场是否出价
     * @param {number}               value 选中值
     */
    const handleCommonBidStatusChange = (value) => {
        console.log('handleCommonBidStatusChange--value', value);
        setSearchData({ commonBidStatus: value });
    };

    /**
     * 保证金-滑块拖动事件
     * @param {Array<number>}         value 当前选中数据
     */
    const handleMarginChange = (value) => {
        console.log('handleMarginChange--value', value);
        setMargin(value);
        setSearchData({ marginMin: value[0], marginMax: value[1] > defaultMargin[1] ? '' : value[1] });
    };

    /**
     * 更多筛选条件关闭事件
     */
    const handleDrawerClose = () => {
        setOpen(false);
        handleClose();
    };

    /**
     * 更多筛选条件事件
     */
    const handleDrawerSumbit = () => {
        setOpen(false);
        handleSumbit();
    };

    /**
     * 返回当前打开的是哪一项
     * @param {string}         itemKey 当前选中筛选项
     */
    const handleSearchOpenItem = (itemKey) => {
        if (itemKey === 'more') {
            setOpen(true);
        }
    };

    /**
     * 删除筛选条件
     * @param {Array<string>}         fromDataKeys 筛选条件KEY
     */
    const handleTagBoxClose = (fromDataKeys) => {
        const resetData = fromDataKeys.reduce((data, cur) => {
            data[cur] = '';
            return data;
        }, {});
        setSearchData(resetData);
        getDealerList(resetData);
    };

    // useEffect
    useUpdateEffect(() => {
        // 重置状态
        resetState(formData);
        // 获取标签栏高度
        if (searchRef) {
            getSearchHight();
        }
    }, [formData]);

    return (
        <div className={searchClasss} ref={searchRef} style={barStyle}>
            <SearchBox onSearch={handleSearchChange} search={searchData.queryText} />
            <DropdownMenu
                className={styles['dropdown']}
                labelList={FILTER_LABEL_LIST}
                labelType='light'
                menuType='tag'
                onOpenItem={handleSearchOpenItem}
                ref={dropRef}
                viewType='tag'>
                <DropdownItem itemKey='labelCode'>
                    <div className={styles['dropdown-content']}>
                        <TagBar
                            formData={searchData}
                            list={tagList}
                            minDistance={10}
                            onChange={handleTagChange}
                            step={10}
                        />
                    </div>
                    <DropdownFooter onClose={handleClose} onSubmit={handleSumbit} />
                </DropdownItem>
                <DropdownItem itemKey='commonBidStatus'>
                    <div className={styles['dropdown-content']}>
                        <RadioTag
                            list={FILTER_SOURCE_LIST.commonBidStatus}
                            onChange={handleCommonBidStatusChange}
                            value={searchData.commonBidStatus}
                        />
                        <DropdownFooter onClose={handleClose} onSubmit={handleSumbit} />
                    </div>
                </DropdownItem>
                {/* <DropdownItem itemKey='noBidDays'>
                    <div className={styles['dropdown-content']}>
                        <SilderBar
                            defaultValue={noBidDays}
                            key='noBidDays'
                            onChange={handleNObidDaysChange}
                            preTitle={noBidDaysTitle}
                            sufTitle='单位：天'
                            value={noBidDays}
                        />
                    </div>
                    <DropdownFooter onClose={handleClose} onSubmit={handleSumbit} />
                </DropdownItem> */}
                {/* <DropdownItem itemKey='getCarCount'>
                    <div className={styles['dropdown-content']}>
                        <SilderBar
                            defaultValue={getCarCount}
                            key='getCarCount'
                            onChange={handleGetCarCountChange}
                            preTitle={getCarCountTitle}
                            sufTitle='单位：台'
                            value={getCarCount}
                        />
                    </div>
                    <DropdownFooter onClose={handleClose} onSubmit={handleSumbit} />
                </DropdownItem> */}
                <DropdownItem itemKey='more' />
            </DropdownMenu>
            {/* 筛选选中项 */}
            <SearchTagBox onClose={handleTagBoxClose} tagList={searchTagsList} />
            {/* 更多筛选弹窗 */}
            <Drawer full hasHeader={false} hash='drawer' mode='right' open={open}>
                <div className={styles['drawer-content']}>
                    <h2 className={styles['label-code-title']}>客户标签</h2>
                    <TagBar
                        formData={searchData}
                        list={tagList}
                        minDistance={10}
                        onChange={handleTagChange}
                        step={10}
                    />
                </div>
                <div className={styles['drawer-content']}>
                    <h2 className={styles['label-code-title']}>当前暗拍场是否出价</h2>
                    <RadioTag
                        list={FILTER_SOURCE_LIST.commonBidStatus}
                        onChange={handleCommonBidStatusChange}
                        value={searchData.commonBidStatus}
                    />
                </div>
                <div className={styles['drawer-content']}>
                    <SilderBar
                        defaultValue={noBidDays}
                        key='noBidDays'
                        onChange={handleNObidDaysChange}
                        preTitle={noBidDaysTitle}
                        sufTitle='单位：天'
                        value={noBidDays}
                    />
                </div>
                <div className={styles['drawer-content']}>
                    <SilderBar
                        defaultValue={getCarCount}
                        key='getCarCount'
                        onChange={handleGetCarCountChange}
                        preTitle={getCarCountTitle}
                        sufTitle='单位：台'
                        value={getCarCount}
                    />
                </div>
                <div className={drawerContent}>
                    <span className={styles['equity-nember-title']}>是否权益包</span>
                    <Group onChange={handleEquityMemberChange} value={searchData.equityMember}>
                        {BUY_VIP_OPTION.map((item) => (
                            <Radio
                                className={styles['radio-tag']}
                                key={item.value}
                                tagType='circle'
                                type='tag'
                                value={item.value}>
                                {item.label}
                            </Radio>
                        ))}
                    </Group>
                </div>
                <div className={styles['drawer-content']}>
                    <SilderBar
                        defaultValue={margin}
                        key='margin'
                        minAndMax={[-5000, 2000]}
                        minDistance={1000}
                        onChange={handleMarginChange}
                        preTitle={marginTitle}
                        step={1000}
                        sufTitle='单位：元'
                        value={margin}
                    />
                </div>
                <DropdownFooter
                    classname={styles['drawer-footer']}
                    onClose={handleDrawerClose}
                    onSubmit={handleDrawerSumbit}
                />
            </Drawer>
        </div>
    );
});

export default connect(({ myClient, loading }) => ({ ...myClient, loading }))(SearchBar);
