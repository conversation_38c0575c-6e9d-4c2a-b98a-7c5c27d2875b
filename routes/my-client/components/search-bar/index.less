@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';

.search-bar {
    width: 375 / @rem;
    position: fixed;
    z-index: 20;
    left: 50%;
    margin-left: -187 / @rem;
    background-color: @color-white;

    .dropdown {
        padding-bottom: 10 / @rem;

        :global {
            .Saitama-dropdown-menu__bar {
                padding: (4 / @rem) (15 / @rem);
            }

            .Saitama-dropdown-menu__item {
                flex: none;
                color: @color-gray-3;
            }

            .Saitama-dropdown-menu__title-arrow {
                border-color: transparent transparent #222 #222;
            }

            .Saitama-dropdown-menu__item-radius {
                border-radius: 14 / @rem;
                background-color: @color-white;
                border: 1px solid #f2f2f2;
                height: 28 / @rem;
                flex-grow: 1;

                &:last-child {
                    border-width: 0;
                    border-bottom-left-radius: 0;
                    background-color: inherit;

                    .Saitama-dropdown-menu__title {
                        color: @color-blue;
                    }

                    .Saitama-dropdown-menu__title-arrow {
                        border-color: transparent transparent @color-blue @color-blue;
                    }
                }
            }
        }

        &-content {
            position: relative;
            min-height: 100 / @rem;
            padding: (10 / @rem) 0;

            &:after {
                .setTopLine(@color-border);
            }
        }

        &-footer {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding: (10 / @rem) (20 / @rem);
            position: relative;

            &:after {
                .setTopLine(@color-border);
            }

            .footer-button {
                width: 156 / @rem;
                height: 44 / @rem;
            }
        }
    }
}

// 更多筛选
.drawer {
    &-content {
        position: relative;
        min-height: 100 / @rem;
        padding: (10 / @rem) 0;

        &__flex {
            display: flex;
            align-items: center;
        }

        &:after {
            .setTopLine(@color-border);
        }

        .label-code-title,
        .equity-nember-title {
            font-weight: bold;
            font-size: 14 / @rem;
            line-height: 1.75;
            padding: 0 (10 / @rem);
        }

        .equity-nember-title {
            margin-top: 10 / @rem;
        }

        .radio-tag {
            width: 70 / @rem;
            height: 34 / @rem;
            text-align: center;
            border-color: #f5f8fa;
            background: #f5f8fa;
            margin-right: 10 / @rem;
            display: inline-block;
        }

        :global {
            .Saitama-radio.Saitama-radio--checked.Saitama-tag {
                border-color: @color-blue;
                background: rgba(0, 162, 232, 0.1);
            }
        }
    }

    &-footer {
        position: absolute;
        background: @color-white;
        width: 375 / @rem;
        z-index: 10;
        bottom: 0;
        left: 50%;
        margin-left: -187 / @rem;
        padding: (10 / @rem) (20 / @rem);
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        &:after {
            .setTopLine(@color-border);
        }

        .footer-button {
            width: 156 / @rem;
            height: 44 / @rem;
        }
    }
}
