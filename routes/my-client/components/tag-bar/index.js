// JS
import React, { memo, useMemo, useRef } from 'react';
import { useUpdateEffect } from 'ahooks';
import { executeCB, isEmpty } from 'ttp-utils';
import cls from 'classnames';
// LESS
import styles from './index.less';

/**
 * 我的客户筛选TAG
 * @param   {object}         props props
 * @returns {object}               jsx
 */
const TagBar = memo((props) => {
    // props
    const { classname, tagStyle = {}, isFixed = false, formData, list, onChange, getHeight } = props;

    // 计算属性
    // 一级标签
    const mainList = useMemo(() => {
        const listData = Array.isArray(list) ? list.filter((item) => item.parentLabelCode == 0) : [];
        return [{ labelName: '全部', labelCode: '' }, ...listData];
    }, [list]);
    // 二级标签
    const childData = useMemo(() => {
        return Array.isArray(list)
            ? list.reduce((data, item) => {
                  const key = item.parentLabelCode;
                  // 排除顶层数据
                  if (isEmpty(key)) {
                      return data;
                  }
                  if (Array.isArray(data[key])) {
                      data[key].push(item);
                  } else {
                      data[key] = [item];
                  }
                  return data;
              }, {})
            : {};
    }, [list]);

    // 常量
    const tagRef = useRef(null);
    const tagClasss = cls(styles['tagbar'], { [styles['tagbar-fixed']]: isFixed }, classname);

    /**
     * 标签切换点击
     * @param {number} parentCode 一级标签数据
     */
    const handleChange = (parentCode) => {
        if (parentCode == formData.parentLabelCode) {
            return;
        }
        executeCB(onChange, { parentLabelCode: parentCode });
    };

    /**
     * 子标签切换点击
     * @param {object} item                 选中数据
     * @param {number} item.parentLabelCode 一级标签数据
     * @param {number} item.labelCode       二级标签数据
     */
    const handleChildChange = (item) => {
        if (item.labelCode == formData.childLabelCode) {
            return;
        }
        executeCB(onChange, item);
    };

    // useEffect：获取标签栏高度
    useUpdateEffect(() => {
        if (tagRef) {
            const { height } = tagRef.current.getBoundingClientRect();
            executeCB(getHeight, height);
        }
    }, [list, formData]);

    return (
        <div className={tagClasss} ref={tagRef} style={tagStyle}>
            <ul className={styles['tagbar-main']}>
                {mainList.map((item) => {
                    const itemClass = cls(styles['tagbar-main-item'], {
                        [styles['tagbar-main-item--active']]: item.labelCode == formData.parentLabelCode
                    });
                    return (
                        <li
                            className={itemClass}
                            key={item.labelCode}
                            onClick={() => handleChange(item.labelCode)}>
                            {item.labelName}
                        </li>
                    );
                })}
            </ul>
            {(formData.parentLabelCode || formData.labelCode) && (
                <ul className={styles['tagbar-childen']}>
                    {Array.isArray(childData[formData.parentLabelCode]) &&
                        childData[formData.parentLabelCode].map((item) => {
                            const itemClass = cls(styles['tagbar-childen-item'], {
                                [styles['tagbar-childen-item--active']]:
                                    item.labelCode == formData.childLabelCode
                            });
                            return (
                                <li
                                    className={itemClass}
                                    key={item.labelCode}
                                    onClick={() => handleChildChange(item)}>
                                    {item.labelName}
                                </li>
                            );
                        })}
                </ul>
            )}
        </div>
    );
});

export default TagBar;
