@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';

.tagbar {
    width: 375 / @rem;

    &-fixed {
        position: fixed;
        z-index: 20;
        left: 50%;
        margin-left: -187 / @rem;
    }

    &-main,
    &-childen {
        display: flex;
        align-items: center;
    }

    &-main {
        height: 52 / @rem;
        background: @color-white;
        padding: 0 (10 / @rem);

        &-item {
            width: 70 / @rem;
            height: 28 / @rem;
            line-height: 28 / @rem;
            background: #ffffff;
            border-radius: 14 / @rem;
            border: 1px solid #f2f2f2;
            color: @color-gray-9;
            font-size: 13 / @rem;
            text-align: center;
            margin: 0 (2 / @rem);

            &--active {
                border: 1px solid @color-blue;
                color: @color-blue;
                background-color: #e5f5fc;
            }

            &:last-child {
                margin-right: 0;
            }
        }
    }

    &-childen {
        min-height: 28 / @rem;
        flex-wrap: wrap;
        padding-bottom: 10 / @rem;
        background-color: @color-gray-drop;

        &-item {
            width: auto;
            min-width: 80 / @rem;
            height: 28 / @rem;
            line-height: 28 / @rem;
            background: @color-white;
            border-radius: 4 / @rem;
            color: @color-gray-6;
            font-size: 13 / @rem;
            text-align: center;
            margin-top: 10 / @rem;
            margin-left: 10 / @rem;
            padding: 0 3 / @rem;
            box-sizing: border-box;

            &--active {
                color: @color-blue;
            }

            &:last-child {
                margin-right: 0;
            }
        }
    }
}
