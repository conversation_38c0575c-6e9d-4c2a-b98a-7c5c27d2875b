// JS
import React, { memo, useEffect, useMemo, useState } from 'react';
import { connect } from 'dva';
import { useToggle, useScroll, useThrottleEffect, useSetState } from 'ahooks';
import { getUrlQueryStringify, getUrlQueryObject, isEmpty } from 'ttp-utils';
import { MY_CLIENT_TAB, PORT_URL } from 'common/js/constant.js';
import { PAGE_URL } from 'routes/router-config.js';
// 组件
import { Loader, Empty, OutCall, message } from 'ttp-library';
import WebViewDrawer from 'components/web-view/index.js';
import TabNav from 'components/tab-nav/index.js';
import BottomLine from 'components/bottom-line/index.js';
import SearchBar from './components/search-bar/index.js';
import ListItem from './components/list-item';
import FollowUpDrawer from './components/follow-up-drawer';
import RecommendDrawer from './components/recommend-drawer';
import LabelIntroduce from './components/label-introduce-drawer';
// LESS
import styles from './index.less';

// 获取视口的高度
const viewportHeight = window.innerHeight;

/**
 * 我的客户
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const MyClient = memo((props) => {
    // props
    const { history, dispatch, loading, tagList, dataList, noNextDataList, formData } = props;
    // 筛选条件
    const { currentPage } = formData;
    const tagLoading = loading.effects['myClient/getDealerTagList'];
    const listLoading = loading.effects['myClient/getDealerList'];
    // state
    const [navHeight, setNavHeight] = useState(0);
    const [searchHeight, setSearchHeight] = useState(0);
    const [item, setItem] = useState({});
    const [auctionUrl, setAuctionUrl] = useState('');
    const [outCallPort, setOutCallPort] = useSetState({ url: PORT_URL.outCall, method: 'POST' });
    // 弹窗开关
    const [openCall, { setLeft: setCloseCall, setRight: setOpenCall }] = useToggle(false);
    const [openDrawer, { setLeft: setCloseDrawer, setRight: setOpenDrawer }] = useToggle(false);
    const [openRecDrawer, { setLeft: setCloseRecDrawer, setRight: setOpenRecDrawer }] = useToggle(false);
    const [openWebview, { setLeft: setCloseWebview, setRight: setOpenWebview }] = useToggle(false);
    const [openLabelIntroduce, setCloseLabelIntroduce] = useState(false);
    // scroll
    const scroll = useScroll(document);
    // 计算属性
    // 搜索栏样式
    const searchBarStyle = useMemo(() => ({ top: navHeight }), [navHeight]);
    const bodyStyle = useMemo(
        () => ({ marginTop: navHeight + searchHeight + 10 }),
        [navHeight, searchHeight]
    );

    /**
     * 获取车商数据
     * @param   {object}               data  请求参数
     */
    const getDealerList = (data) => {
        if (!listLoading) {
            dispatch({
                type: 'myClient/getDealerList',
                payload: { ...formData, ...data }
            });
        }
    };

    /**
     * 导航tab切换点击
     * @param {object}         item 当前选中数据
     */
    const handleNavChange = (item) => {
        history.push(item.url);
    };

    /**
     * ListItem的点击事件回调
     * @param  {object}         item   当前选中数据
     * @param  {string}         action 事件类型
     */
    const handleFollowUp = (item, action) => {
        switch (action) {
            case 'jump':
                // 查看车商详情
                history.push({
                    pathname: PAGE_URL.myClientDealer,
                    search: getUrlQueryStringify({ dealerId: item.dealerId }, '?')
                });
                break;
            case 'viewRecord':
                //  查看跟进记录
                history.push({
                    pathname: PAGE_URL.myLog,
                    search: getUrlQueryStringify({ dealerId: item.dealerId }, '?')
                });
                break;
            case 'noFollowUp':
                // 90天内不再跟进
                setItem(item);
                setOpenDrawer();
                break;
            case 'recommend':
                // 推荐车源
                setItem(item);
                setOpenRecDrawer();
                break;
            case 'call':
                // 拨打电话
                setOutCallPort({ data: { dealerId: item.dealerId } });
                setOpenCall();
                break;
            default:
                break;
        }
    };

    /**
     * 拨打电话成功 回调
     */
    const handleOutCallSucess = () => {
        message('拨打电话成功，请注意接听回拨电话。', 3);
    };

    /**
     * 推荐车源点击事件，打开车源详情
     * @param {string}           url 车源URL
     */
    const handelItemClick = (url) => {
        setAuctionUrl(decodeURIComponent(url));
        setOpenWebview();
    };

    /**
     * icon点击事件
     */
    const handleIconClick = () => {
        setCloseLabelIntroduce(true);
    };

    /**
     * 关闭客户标签说明
     */
    const handleCloseIntroduce = () => {
        setCloseLabelIntroduce(false);
    };

    // useEffect
    // 获取车商数据 & 获取标签数据
    useEffect(() => {
        // 获取标签列表
        if (tagList.length == 0) {
            dispatch({ type: 'myClient/getDealerTagList' });
        }
        // 获取搜索条件
        const searchData = getUrlQueryObject();
        if (!isEmpty(searchData)) {
            dispatch({
                type: 'myClient/updateFormDataState',
                payload: searchData
            });
        }
        // 获取车商数据
        getDealerList({ ...searchData, currentPage: 1 });
        return () => {
            dispatch({ type: 'myClient/resetState' });
        };
    }, []);
    // 监听页面滚动
    useThrottleEffect(
        () => {
            // 获取页面的总高度
            const totalHeight = document.documentElement.scrollHeight;
            // 获取当前滚动的位置
            const { top = 0 } = scroll || {};
            // 判断是否滚动到底部（考虑到可能的浏览器差异）
            if (Math.ceil(top + viewportHeight + 100) >= totalHeight && !noNextDataList && top != 0) {
                // 滚动到底部时执行的代码
                getDealerList({ currentPage: currentPage + 1 });
            }
        },
        [scroll],
        { wait: 500 }
    );

    return (
        <>
            <Loader maskOpacity={0.6} open={tagLoading || listLoading} />
            <div className={styles['my-client']}>
                {/* 我的客户/我的工作 tab */}
                <TabNav
                    current={1}
                    getHeight={setNavHeight}
                    list={MY_CLIENT_TAB}
                    onChange={handleNavChange}
                    onIconClick={handleIconClick}
                />
                {/* 搜索栏：搜索框、筛选、标签 */}
                <SearchBar
                    barStyle={searchBarStyle}
                    formData={formData}
                    getHeight={setSearchHeight}
                    history={history}
                />
                {/* 商家列表 */}
                <div className={styles['my-client-body']} style={bodyStyle}>
                    {dataList.length ? (
                        dataList.map((item, index) => (
                            <ListItem
                                index={index}
                                itemData={item}
                                key={item.dealerId}
                                onFollowUp={handleFollowUp}
                            />
                        ))
                    ) : (
                        <Empty />
                    )}
                </div>
                {noNextDataList && <BottomLine />}
            </div>
            {/* 电话:TODO */}
            <OutCall
                autoCall={false}
                onClose={setCloseCall}
                onFetchData={handleOutCallSucess}
                open={openCall}
                port={outCallPort}
            />
            {/* 90天内不再跟进 */}
            <FollowUpDrawer item={item} onClose={setCloseDrawer} open={openDrawer} />
            {/* 车源推荐 */}
            <RecommendDrawer
                dealerId={item.dealerId}
                onClose={setCloseRecDrawer}
                onItemClick={handelItemClick}
                open={openRecDrawer}
            />
            {/* 车源详情 */}
            <WebViewDrawer
                iframeURL={auctionUrl}
                onClose={setCloseWebview}
                open={openWebview}
                title='检测报告'
            />
            <LabelIntroduce onClose={handleCloseIntroduce} open={openLabelIntroduce} />
        </>
    );
});

export default connect(({ myClient, loading }) => ({ ...myClient, loading }))(MyClient);
