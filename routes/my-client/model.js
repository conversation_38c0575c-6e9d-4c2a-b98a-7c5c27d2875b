/**
 * 我的客户-我的客户
 */
import * as api from './api.js';

/**
 * 默认参数
 * @returns {object}   默认参数
 */
const initState = () => ({
    tagList: [],
    dataList: [],
    // 没有下一页
    noNextDataList: false,
    formData: {
        // 搜索关键字
        queryText: '',
        // 是否权益包会员,(0否/1是)
        equityMember: '',
        // 提车量(最小值/最大值)
        getCarCountMin: '',
        getCarCountMax: '',
        // 未出价天数(最小值/最大值)
        noBidDaysMin: '',
        noBidDaysMax: '',
        // 保证金(最小值/最大值)
        marginMin: '',
        marginMax: '',
        // 经销商对应 的客户分类 10:潜在客户, 20:纯新客户, 30:成交客户, 40:激活客户
        parentLabelCode: '',
        // 经销商对应 的子标签Code
        childLabelCode: '',
        // 当前暗拍场是否出价
        commonBidStatus: '',
        // 分页参数
        currentPage: 1,
        pageSize: 20
    }
});

export default {
    namespace: 'myClient',

    state: initState(),

    reducers: {
        /**
         * 重置状态
         * @param   {object}               state 当前state
         * @returns {object}                     更新过的state
         */
        resetState(state) {
            return {
                ...initState(),
                tagList: state.tagList
            };
        },

        /**
         * 更新状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateState(state, action) {
            return {
                ...state,
                ...action.payload
            };
        },

        /**
         * 更新状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateFormDataState(state, action) {
            return {
                ...state,
                formData: {
                    ...state.formData,
                    ...action.payload
                }
            };
        }
    },

    effects: {
        /**
         * 获取标签数据
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *getDealerTagList({ payload }, { call, put }) {
            const { result } = yield call(api.getDealerLabelList, payload);
            const tagList = Array.isArray(result) ? result : [];
            yield put({
                type: 'updateState',
                payload: {
                    tagList
                }
            });
        },

        /**
         * 获取商家数据
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @param   {Function}             param1.select  select
         * @returns {object}                              用户信息
         */
        *getDealerList({ payload }, { call, put, select }) {
            const dataState = yield select((state) => state.myClient);
            const { result } = yield call(api.getDealerList, payload);
            const { currentPage = 1, dataList = [] } = result || {};
            yield put({
                type: 'updateState',
                payload: {
                    dataList: Array.isArray(dataList)
                        ? currentPage == 1
                            ? dataList
                            : dataState.dataList.concat(dataList)
                        : [],
                    noNextDataList: dataList.length < dataState.formData.pageSize,
                    formData: {
                        ...dataState.formData,
                        currentPage: payload.currentPage
                    }
                }
            });
            console.log(
                'dataList.length ',
                dataList.length,
                'dataState.formData.pageSize ',
                dataState.formData.pageSize
            );
            console.log('noNextDataList', dataList.length < dataState.pageSize);
        }
    }
};
