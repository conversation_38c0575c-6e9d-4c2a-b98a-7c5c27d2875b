/**
 * @file 我的客户-跟进记录详情
 */

/* requires JS */
import { postRequest, getRequest } from 'common/js/fetch.js';
import { PORT_URL } from 'common/js/constant.js';

/**
 * 获取商户签到信息数据
 * <AUTHOR>
 * @param    {object}                 data 请求参数对象
 * @returns  {Promise}                     返回一个Feach的Promise对象
 */
export function getLogList(data) {
    let opts = {
        url: PORT_URL.recordList,
        data
    };
    return postRequest(opts);
}

/**
 * 根据ID查询跟进记录
 * @param   {object}                  param          参数对象
 * @param   {number}                  param.dealerId dealerId
 * @param   {number}                  param.recordId recordId
 * @returns {object}                                 跟进记录
 */
export function getLogRecord({ dealerId, recordId }) {
    let opts = {
        url: PORT_URL.recordDetail,
        data: { dealerId, recordId }
    };
    return postRequest(opts);
}

/**
 * 保存跟进记录
 * @param {*} data 请求参数对象
 * @returns {Promise} 返回一个Feach的Promise对象
 */
export function submitLogRecord(data) {
    let opts = {
        url: PORT_URL.saveRecord,
        data
    };
    return postRequest(opts);
}

/**
 * 获取七牛上传文件域名
 * <AUTHOR>
 * @param    {object}                 data          请求参数对象
 * @returns  {Promise}                              返回一个Feach的Promise对象
 */
export function getDomain(data) {
    let opts = {
        url: PORT_URL.getDomain,
        data
    };
    return getRequest(opts);
}

/**
 * 获取Boss上传TOKEN
 * <AUTHOR>
 * @param    {object}                 data          请求参数对象
 * @returns  {Promise}                              返回一个Feach的Promise对象
 */
export function getBossToken(data) {
    let opts = {
        url: PORT_URL.getStoreToken,
        data
    };
    return postRequest(opts);
}
