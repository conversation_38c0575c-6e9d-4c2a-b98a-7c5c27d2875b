// JS
import React, { memo, useEffect, useState } from 'react';
import { useToggle, useEventTarget, useMap, useRequest } from 'ahooks';
import { executeCB, isEmpty, formatTime } from 'ttp-utils';
import { formatTimeData } from 'common/js/utils';
import { FOLLOW_UP_TAG_MAP, FOLLOW_UP_OPTION } from 'common/js/constant.js';
import { getAudioList } from 'routes/my-follow-up-log/api';
import cls from 'classnames';
// 组件
import { List, Button, Textarea, Calendar, Drawer, Icon } from 'ttp-library';
import FileList from 'components/file-list';
import Panel from 'components/panel';
import Tag from 'components/tag';
import FormItemErrorTip from 'components/form-item-error-tip';
import DrawerDown from 'components/drawer-down';
import AudioDrawer from 'components/audio-drawer';
// LESS
import styles from './index.less';

/**
 * 意向程度弹窗数据
 * @type {Array<object>}
 */
const intentionLevelOptions = Array.from(new Array(11), (_, i) => ({ label: `${i * 10}%`, value: i * 10 }));

// 跟进结论标签
const FollowConclusionTag = memo((props) => {
    const { item } = props;
    const tagData = FOLLOW_UP_TAG_MAP[item];
    if (isEmpty(tagData)) {
        return <span className={styles['select-text']}>请选择</span>;
    }
    return (
        <Tag classname={styles['right-tag']} themeColor={tagData?.type}>
            {tagData?.label}
        </Tag>
    );
});

/**
 * 错误提示
 * @type {object}
 */
const ERROR_MSG_MAP = {
    followType: '必填项“跟进方式”不能为空。',
    followResult: '必填项“跟进结果”不能为空。',
    followGoal: '必填项“跟进目标”不能为空。',
    followConclusion: '必填项“跟进结论”不能为空。'
};

/**
 * 我的客户数据ITEM
 * @param   {object}                     props props
 * @returns {React.ReactElement}               jsx
 */
const ListItem = memo((props) => {
    // props
    const { followRecord = {}, itemData, onEdit, onClickUpload, onSubmit } = props;
    const {
        followGoalMap = {},
        followGoalOption = [],
        followUpTypeMap = {},
        followUpTypeOption = []
    } = followRecord || {};
    const { imageFileUrls = [], videoFileUrls = [], audioFileUrls = [] } = itemData;
    // state
    const [clickSubmit, setClickSubmit] = useState(false);
    const [openFollowMethod, { setLeft: followMethodClose, setRight: followMethodOpen }] = useToggle(false);
    const [openNextDate, { setLeft: nextDateClose, setRight: nextDateOpen }] = useToggle(false);
    const [openFollowConclusion, { setLeft: followConclusionClose, setRight: followConclusionOpen }] =
        useToggle(false);
    const [openIntentionLevel, { setLeft: intentionLevelClose, setRight: intentionLevelOpen }] =
        useToggle(false);
    const [openFollowGoal, { setLeft: followGoalClose, setRight: followGoalOpen }] = useToggle(false);
    // 错误提示
    const [errorMsg, { set: setErrorMsg, get: getErrorMsg }] = useMap([
        // 跟进方式
        ['followType', ''],
        // 跟进结果
        ['followResult', ''],
        // 跟进目标
        ['followGoal', ''],
        // 跟进结论
        ['followConclusion', '']
    ]);
    const [followGoalExtendError, setFollowGoalExtendError] = useState('');
    // 跟进目标
    const [followGoal, onFollowGoalChange] = useState('');
    // 跟进目标-其他
    const [followGoalExtend, { onChange: onFollowGoalExtendChange, reset: resetFollowGoalExtend }] =
        useEventTarget({ initialValue: '' });
    // 天润录音播放
    const [openDrawer, { setLeft: setCloseDrawer, setRight: setOpenDrawer }] = useToggle(false);
    const [audioList, setAudioList] = useState([]);
    const { loading, runAsync } = useRequest(getAudioList, {
        manual: true,
        debounceWait: 300,
        // eslint-disable-next-line jsdoc/require-jsdoc
        onSuccess: ({ result }) => {
            if (isEmpty(result)) {
                setAudioList([]);
            }
            setAudioList([{ title: '音频', type: 'audio', src: result }]);
        }
    });

    // 常量
    const uploadStyle = cls(styles['list-box-item'], styles['no-border']);
    const pictureStyle = cls(styles['list-box-item'], styles['no-padding']);

    // 函数
    /**
     * 校验必填项
     * @param   {object}                     fromData  表单数据
     * @param   {object}                     isSubmit  是否提交过表单
     * @returns {boolean}                              校验是否通过
     */
    const isVerification = (fromData, isSubmit = clickSubmit) => {
        if (isSubmit) {
            // 获取必填项的KEY
            const requiredKeys = [...errorMsg.keys()];
            // 校验必填项
            return requiredKeys.reduce((data, key) => {
                const value = fromData[key];
                if (isEmpty(value)) {
                    setErrorMsg(key, ERROR_MSG_MAP[key]);
                    data = false;
                } else {
                    setErrorMsg(key, '');
                }
                console.log('校验', key, value, isEmpty(value), data);
                return data;
            }, true);
        }
        return true;
    };

    /**
     * 跟进方式弹窗--确定回调
     * @param   {string}             checkedData 跟进方式
     */
    const handelFollowTypeOk = (checkedData) => {
        executeCB(onEdit, { followType: checkedData });
        followMethodClose();
    };

    /**
     * 跟进结论弹窗--确定回调
     * @param   {string}             checkedData 跟进结论
     */
    const handelFollowConclusionOk = (checkedData) => {
        executeCB(onEdit, { followConclusion: checkedData });
        followConclusionClose();
    };

    /**
     * 意向程度弹窗--确定回调
     * @param   {string}             checkedData 跟进结论
     */
    const handelIntentionLevelOk = (checkedData) => {
        executeCB(onEdit, { intentionLevel: checkedData });
        intentionLevelClose();
    };

    /**
     * 日期组件切换事件
     * @param   {Date}                     value 日期
     */
    const handelCalendarchange = (value) => {
        console.log('handelCalendarchange', value, formatTimeData(value, 'yyyy年mm月dd日'));
        executeCB(onEdit, { nextFollowTime: value.getTime() });
        nextDateClose();
    };

    /**
     * 日期组件-title-desabled事件
     * @param   {object}                   param      参数
     * @param   {Date}                     param.date 日期
     * @returns {boolean}                             是否禁用
     */
    const handelTileDisabled = ({ date }) => {
        return date < new Date();
    };

    /**
     * 跟进目标-radio change
     * @param   {string}                   value value
     */
    const handelRadioChange = (value) => {
        onFollowGoalChange(value);
        if (!isEmpty(followGoalExtend)) {
            resetFollowGoalExtend();
        }
    };

    /**
     * 跟进目标-确定点击事件
     */
    const handelFollowGoalExtendOk = () => {
        if (followGoal == 200 && isEmpty(followGoalExtend)) {
            setFollowGoalExtendError('跟进目标不能为空。');
            return;
        }
        setFollowGoalExtendError('');
        executeCB(onEdit, {
            followGoalExtend,
            followGoal
        });
        followGoalClose();
    };

    /**
     * 跟进目标-取消点击事件
     */
    const handelFollowGoalExtendCancel = () => {
        setFollowGoalExtendError('');
        onFollowGoalChange(itemData.followGoal);
        resetFollowGoalExtend();
        followGoalClose();
    };

    /**
     * 放弃原因
     * @param {event} e event
     */
    const handleChange = (e) => {
        const value = e.target.value.trim();
        executeCB(onEdit, { followResult: value });
    };

    /**
     * 提交
     */
    const handelSubmit = () => {
        setClickSubmit(true);
        // 判断校验，并提交
        if (isVerification(itemData, true)) {
            executeCB(onSubmit);
        }
    };

    /**
     * 天润录音：查看录音
     */
    const handleCallClick = () => {
        const { callId } = itemData;
        if (isEmpty(callId) || callId <= 0) {
            return;
        }
        if (audioList.length <= 0) {
            runAsync({ callId });
        }
        setOpenDrawer();
    };
    /**
     * 天润录音：关闭游览窗口
     */
    const handleDrawerClose = () => {
        setCloseDrawer();
    };

    // 常量
    const leftNode = (
        <div className={styles['drawer-left-close']} onClick={nextDateClose}>
            <Icon className={styles['drawer-close']} type='iconfont-close' />
        </div>
    );

    // useEffect
    useEffect(() => {
        console.log('followGoal', itemData.followGoal);
        console.log('ollowGoalExtend', itemData.followGoalExtend);
        onFollowGoalChange(itemData.followGoal);
        onFollowGoalExtendChange({ target: { value: itemData.followGoalExtend } });
    }, [itemData.followGoalExtend, itemData.followGoal]);

    return (
        <>
            <Panel classname={styles['list-item']} open={true}>
                <List className={styles['list-box']}>
                    <List.Item
                        className={styles['list-box-item']}
                        extra={formatTime(itemData.createTime, 'yyyy年mm月dd日')}>
                        日期
                    </List.Item>
                    <List.Item className={styles['list-box-item']} extra={itemData.dealerAdminName}>
                        跟进人姓名
                    </List.Item>
                    <List.Item
                        className={styles['list-box-item']}
                        extra={
                            !isEmpty(itemData.dealerLabelName) ? <Tag>{itemData.dealerLabelName}</Tag> : '--'
                        }>
                        用户标签
                    </List.Item>
                    <List.Item
                        arrow='right'
                        className={styles['list-box-item']}
                        extra={
                            !isEmpty(itemData.followType) &&
                            !isEmpty(followUpTypeMap[itemData.followType]) ? (
                                followUpTypeMap[itemData.followType]
                            ) : (
                                <span className={styles['select-text']}>请选择</span>
                            )
                        }
                        onClick={followMethodOpen}>
                        <span>
                            <b className={styles['red-text']}>*</b> 跟进方式
                        </span>
                    </List.Item>
                    <FormItemErrorTip
                        classname={styles['list-box-error']}
                        message={getErrorMsg('followType')}
                    />
                    <List.Item className={styles['list-box-item']}>
                        <span>
                            <b className={styles['red-text']}>*</b> 跟进结果
                        </span>
                        <div className={styles['input-textarea-wrapper']}>
                            <Textarea
                                className={styles['input-textarea']}
                                maxLength={500}
                                onChange={handleChange}
                                placeholder='请输入结果'
                                value={itemData.followResult}
                            />
                            <span className={styles['input-textarea-maxlength']}>
                                {500 - itemData.followResult.length}个字
                            </span>
                        </div>
                    </List.Item>
                    <FormItemErrorTip
                        classname={styles['list-box-error']}
                        message={getErrorMsg('followResult')}
                    />
                    <List.Item
                        arrow='right'
                        className={styles['list-box-item']}
                        extra={
                            !isEmpty(itemData.nextFollowTime) ? (
                                formatTimeData(itemData.nextFollowTime, 'yyyy年mm月dd日')
                            ) : (
                                <span className={styles['select-text']}>请选择</span>
                            )
                        }
                        onClick={nextDateOpen}>
                        后续跟进计划
                    </List.Item>
                    <List.Item
                        arrow='right'
                        className={cls([styles['list-box-item'], styles['no-border']])}
                        extra={
                            !isEmpty(itemData.followGoal) && !isEmpty(followGoalMap[itemData.followGoal]) ? (
                                followGoalMap[itemData.followGoal]
                            ) : (
                                <span className={styles['select-text']}>请选择</span>
                            )
                        }
                        onClick={followGoalOpen}>
                        <span>
                            <b className={styles['red-text']}>*</b> 跟进目标
                        </span>
                    </List.Item>
                    <div className={styles['follow-goal-extend']}>
                        {!isEmpty(itemData.followGoalExtend) && (
                            <div className={styles['follow-goal-extend-text']}>
                                {itemData.followGoalExtend}
                            </div>
                        )}
                    </div>
                    <FormItemErrorTip
                        classname={styles['list-box-error']}
                        message={getErrorMsg('followGoal')}
                    />
                    <List.Item
                        arrow='right'
                        className={styles['list-box-item']}
                        extra={<FollowConclusionTag item={itemData.followConclusion} />}
                        onClick={followConclusionOpen}>
                        <span>
                            <b className={styles['red-text']}>*</b> 跟进结论
                        </span>
                    </List.Item>
                    <FormItemErrorTip
                        classname={styles['list-box-error']}
                        message={getErrorMsg('followConclusion')}
                    />
                    <List.Item
                        arrow='right'
                        className={styles['list-box-item']}
                        extra={
                            !isEmpty(itemData.intentionLevel) && itemData.intentionLevel >= 0 ? (
                                `${itemData.intentionLevel}%`
                            ) : (
                                <span className={styles['select-text']}>请选择</span>
                            )
                        }
                        onClick={intentionLevelOpen}>
                        意向程度
                    </List.Item>
                    <List.Item
                        className={uploadStyle}
                        extra={
                            <Button className={styles['upload-btn']} size='sm' skin='white'>
                                上传资料
                            </Button>
                        }
                        onClick={onClickUpload}>
                        资料上传
                    </List.Item>
                    <List.Item className={pictureStyle}>
                        <FileList audio={audioFileUrls} image={imageFileUrls} video={videoFileUrls} />
                    </List.Item>
                    {/* <List.Item arrow='right' className={styles['list-box-item']} extra={subtitle}>
                    当前位置
                    </List.Item>
                    <List.Item arrow='right' className={styles['list-box-item']} extra={subtitle}>
                        更新时间
                    </List.Item> */}
                    {!isEmpty(itemData.callId) && itemData.callId > 0 && (
                        <List.Item
                            className={styles['list-box-item']}
                            extra={
                                <Button className={styles['upload-btn']} size='sm' skin='white'>
                                    查看录音
                                </Button>
                            }
                            key={itemData.callId}
                            onClick={handleCallClick}>
                            录音同步
                        </List.Item>
                    )}
                </List>
            </Panel>
            <div className={styles['footer']}>
                <Button className={styles['submit-btn']} onClick={handelSubmit}>
                    确认提交
                </Button>
            </div>
            {/* 跟进方式弹窗 */}
            <DrawerDown
                dataList={followUpTypeOption}
                onChange={handelFollowTypeOk}
                onClose={followMethodClose}
                open={openFollowMethod}
                title={'选择跟进方式'}
                value={itemData.followType}
            />

            {/* 跟进结论弹窗 */}
            <DrawerDown
                dataList={FOLLOW_UP_OPTION}
                onChange={handelFollowConclusionOk}
                onClose={followConclusionClose}
                open={openFollowConclusion}
                title={'选择跟进结论'}
                value={itemData.followConclusion}
            />

            {/* 意向程度弹窗 */}
            <DrawerDown
                dataList={intentionLevelOptions}
                onChange={handelIntentionLevelOk}
                onClose={intentionLevelClose}
                open={openIntentionLevel}
                title={'选择意向程度'}
                value={itemData.intentionLevel}
            />

            {/* 后续跟进计划弹窗 */}
            <Drawer
                left={leftNode}
                mode='bottom'
                onClose={nextDateClose}
                open={openNextDate}
                title='选择后续跟进计划'>
                <Calendar
                    defaultValue={itemData.nextFollowTime ? new Date(itemData.nextFollowTime) : undefined}
                    onChange={handelCalendarchange}
                    tileDisabled={handelTileDisabled}
                />
            </Drawer>
            {/* 跟进目标弹窗 */}
            <DrawerDown
                dataList={followGoalOption}
                onChange={handelFollowGoalExtendOk}
                onClose={handelFollowGoalExtendCancel}
                onSelect={handelRadioChange}
                open={openFollowGoal}
                title={'选择跟进目标'}
                value={followGoal}>
                {followGoal == 200 && (
                    <div className={styles['input-textarea-wrapper']}>
                        <Textarea
                            className={styles['input-textarea']}
                            maxLength={20}
                            onChange={onFollowGoalExtendChange}
                            placeholder='请输入跟进目标'
                            value={followGoalExtend}
                        />
                        <span className={styles['input-textarea-maxlength']}>
                            {20 - followGoalExtend.length}个字
                        </span>
                        <FormItemErrorTip message={followGoalExtendError} />
                    </div>
                )}
            </DrawerDown>
            {/* 天润录音弹窗 */}
            <AudioDrawer
                currentIndex={0}
                list={audioList}
                loading={loading}
                onClose={handleDrawerClose}
                open={openDrawer}
                title='跟进资料（音频）'
            />
        </>
    );
});

export default ListItem;
