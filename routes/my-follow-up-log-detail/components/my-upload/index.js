// JS
import React, { memo, useEffect, useMemo, useState } from 'react';
import { connect } from 'dva';
import { isEmpty, getUrlQueryStringify } from 'ttp-utils';
// 组件
import { Loader, FileUpload, Button, Drawer } from 'ttp-library';
import QiniuUpload from 'components/upload-from-qiniu-v3/index.js';
// LESS
import styles from './index.less';

// 视频请求配置
// const videoOptions = {
//     filters: {
//         mime_types: [
//             { title: 'Video files', extensions: 'mpg,mpeg,avi,rmvb,mkv,m4v,mp4', mime_types: ['video/*'] }
//         ]
//     }
// };
// // 音频请求配置
// const audioOptions = {
//     filters: {
//         mime_types: [
//             {
//                 title: 'Audio files',
//                 extensions: 'aac,mp3,mp4,wav,m4r,caf,ogg,opus,m4a,flac,x-m4a',
//                 mime_types: ['audio/*']
//             }
//         ]
//     }
// };

/**
 * 我的客户-附件上传
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const myUpload = memo((props) => {
    // props
    const {
        dispatch,
        loading,
        formData: { imageFileUrls = [], videoFileUrls = [], audioFileUrls = [] },
        bossToken,
        videoDomain = '',
        audioDomain = '',
        isOpenUpload
    } = props;
    const [imgUrls, setImgUrls] = useState(imageFileUrls);
    const [videoUrls, setVideoUrls] = useState(videoFileUrls);
    const [audioUrls, setAudioUrls] = useState(audioFileUrls);
    const [imgLoading, setImgLoading] = useState(false);

    const isLoading = loading.effects['myUpload/getDomain'];
    // 计算属性
    // 图片请求参数
    const uploadPicPort = useMemo(
        () => ({
            url: '//store.boss.ttpai.cn/store/encryption?token=' + bossToken,
            config: { credentials: 'include' }
        }),
        [bossToken]
    );

    // /**
    //  * 上传图片成功回调
    //  * @param {Array} successList  上传的图片
    //  */
    // const onUploadImgComplete = (successList) => {
    //     console.log('onUploadImgComplete-token', bossToken);
    //     setImgUrls([...imgUrls, getUrlQueryStringify({ token: bossToken }, `${successList}?`)]);
    // };

    /**
     * 文件上传前回调，参数分别为处理文件资源。 文件原始对象，继续内部触发向下执行的回调函数，也可通过返回一个 promise控制是否继续向后执行上传，如果 reject 则不会继续上传
     * @param   {string}                  base 上传图片base64File
     * @param   {File}                    file 上传图片文件
     * @param   {Function}                next 继续执行函数
     * @returns {Promise}                      Promise
     */
    const handleBeforeUpload = (base, file, next) => {
        setImgLoading(true);
        next(base);
    };

    /**
     * 上传图片成功回调
     * @param {Array} successList  上传的图片
     */
    const handleQueueComplete = (successList) => {
        console.log('onUploadImgComplete-token', bossToken);
        if (Array.isArray(successList) && successList.length > 0) {
            // 图片使用加密上传，为了正确的显示图片，需要添加bossToken
            const imageList = successList.map((item) => {
                if (item.indexOf('token=') === -1) {
                    return getUrlQueryStringify({ token: bossToken }, `${item}?`);
                } else {
                    return item;
                }
            });
            setImgUrls(imageList);
        }
        setImgLoading(false);
    };

    /**
     * 删除图片
     * @param {object} delItem  删除图片信息
     * @param {number} idx  索引
     */
    const handleRemovePic = (delItem, idx) => {
        setImgUrls(imgUrls.filter((_, index) => index != idx));
    };

    /**
     * 上传视频成功回调
     * @param {Array} successList  上传的图片
     */
    const onUploadVideoComplete = (successList) => {
        setVideoUrls([...videoUrls, successList]);
    };

    /**
     * 删除视频成功回调
     * @param {Array} idx  上传的图片索引
     */
    const onDeleteVideo = (idx) => {
        setVideoUrls(videoUrls.filter((item, index) => index !== idx));
    };

    /**
     * 上传语音成功回调
     * @param {Array} successList  上传的图片
     */
    const onUploadAudioComplete = (successList) => {
        setAudioUrls([...audioUrls, successList]);
    };

    /**
     * 删除语音成功回调
     * @param {Array} idx  上传的图片索引
     */
    const onDeleteAudio = (idx) => {
        setAudioUrls(audioUrls.filter((item, index) => index !== idx));
    };

    /**
     * 关闭弹框
     */
    const handleClose = () => {
        dispatch({
            type: 'myLogDetail/updateState',
            payload: {
                isOpenUpload: false
            }
        });
    };

    /**
     * 提交
     */
    const onSubmit = () => {
        dispatch({
            type: 'myLogDetail/submitUpload',
            payload: {
                imageFileUrls: imgUrls,
                videoFileUrls: videoUrls,
                audioFileUrls: audioUrls
            }
        });
        handleClose();
    };

    // useEffect
    // 初始化：获取域名
    useEffect(() => {
        console.log('open upload', props);
        dispatch({
            type: 'myLogDetail/getDomain'
        });
    }, []);

    useEffect(() => {
        if (isOpenUpload) {
            setImgUrls(imageFileUrls);
            setVideoUrls(videoFileUrls);
            setAudioUrls(audioFileUrls);
        }
    }, [isOpenUpload]);

    return (
        <Drawer
            destroy={false}
            full
            hash='drawer-upload-file'
            mode='right'
            onClose={handleClose}
            open={isOpenUpload}
            title='跟进记录'>
            <div className={styles['upload-page']}>
                <Loader maskOpacity={0.6} open={isLoading} type='page' />
                <div className={styles['my-upload']}>
                    <div className={styles['upload-block']}>
                        <div className={styles['title-wrapper']}>
                            <span className={styles['title']}>照片</span>
                            <span className={styles['tips']}>*可以上传多张图片</span>
                        </div>
                        <div className={styles['content-wrapper']}>
                            {!isEmpty(bossToken) && (
                                <FileUpload
                                    accept='image/jpg,image/png'
                                    className={styles['file-upload']}
                                    fileFieldName='file'
                                    fileFieldType='file'
                                    files={imgUrls}
                                    maxSize={10}
                                    onBeforeUpload={handleBeforeUpload}
                                    onQueueComplete={handleQueueComplete}
                                    // onComplete={onUploadImgComplete}
                                    onRemove={handleRemovePic}
                                    quality={0.2}
                                    total={15}
                                    type='picture'
                                    uploadUrl={uploadPicPort}
                                />
                            )}
                        </div>
                    </div>
                    <div className={styles['upload-block']}>
                        <div className={styles['title-wrapper']}>
                            <span className={styles['title']}>视频</span>
                            <span className={styles['tips']}>*可以上传多条视频</span>
                        </div>
                        <div className={styles['content-wrapper']}>
                            {videoDomain && (
                                <QiniuUpload
                                    accept='video/*'
                                    domain={videoDomain}
                                    fileType='video'
                                    limit={2}
                                    maxSize={200 * 1024}
                                    onDelete={onDeleteVideo}
                                    onSuccess={onUploadVideoComplete}
                                    uploadedList={videoUrls}
                                />
                            )}
                        </div>
                    </div>
                    <div className={styles['upload-block']}>
                        <div className={styles['title-wrapper']}>
                            <span className={styles['title']}>录音</span>
                            <span className={styles['tips']}>*可以上传多个录音</span>
                        </div>
                        <div className={styles['content-wrapper']}>
                            {audioDomain && (
                                <QiniuUpload
                                    accept='audio/aac,audio/mp3,audio/wav,audio/m4a,audio/flac, audio/x-m4a'
                                    domain={audioDomain}
                                    fileType='audio'
                                    limit={3}
                                    maxSize={20 * 1024}
                                    onDelete={onDeleteAudio}
                                    onSuccess={onUploadAudioComplete}
                                    uploadedList={audioUrls}
                                />
                            )}
                        </div>
                    </div>
                </div>
                <div className={styles['footer']}>
                    <Button className={styles['submit-btn']} loading={imgLoading} onClick={onSubmit}>
                        确认提交
                    </Button>
                </div>
            </div>
        </Drawer>
    );
});

export default connect(({ myLogDetail, loading }) => ({ ...myLogDetail, loading }))(myUpload);
