@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';

.hidden {
    display: none;
}

.upload-page {
    .my-upload {
        margin: 10 / @rem 10 / @rem 90 / @rem 10 / @rem;
        padding: 15 / @rem;
        background: #fff;
        border-radius: 6 / @rem;

        .upload-block {
            margin-bottom: 26 / @rem;

            .title-wrapper {
                margin-bottom: 20 / @rem;

                .title {
                    margin-right: 9 / @rem;
                    font-weight: 400;
                    font-size: 14 / @rem;
                    color: #000000;
                    line-height: 14 / @rem;
                }

                .tips {
                    font-weight: 400;
                    font-size: 12 / @rem;
                    color: #999999;
                    line-height: 12 / @rem;
                }
            }
        }
    }

    .bottom-gap {
        height: 80 / @rem;
    }

    .footer {
        height: 80 / @rem;
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        background: #fff;

        .submit-btn {
            width: 330 / @rem;
            height: 46 / @rem;
        }
    }

    :global {
        .Saitama-fileUpload-upload-item {
            margin-right: 5 / @rem;
        }

        .Saitama-lazy-load {
            display: flex;
            justify-content: center;
            align-items: center;

            .Saitama-lazy-load__image {
                max-width: 100%;
                max-height: 100%;
                height: auto;
                width: auto;
            }
        }
    }
}
