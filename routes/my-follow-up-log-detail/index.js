// JS
import React, { memo, useEffect } from 'react';
import { connect } from 'dva';
import { useRequest } from 'ahooks';
import { getFollowRecord } from 'routes/my-follow-up-log/api';
import { PAGE_URL } from 'routes/router-config.js';
import { getUrlQueryObject, getUrlQueryStringify, isEmpty } from 'ttp-utils';
// 组件
import { Loader, message } from 'ttp-library';
import ListItem from './components/list-item';
import MyUpload from './components/my-upload/index';
// LESS
import styles from './index.less';

/**
 * 我的客户-跟进记录详情
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const myLogDetail = memo((props) => {
    // props
    const { history, dispatch, loading, formData, isOpenUpload, bossToken } = props;
    const detailLoading = loading.effects['myLogDetail/getLogDetail'];
    const submitLoading = loading.effects['myLogDetail/submitLogDeatil'];
    const bossTokenLoading = loading.effects['myLogDetail/getBossToken'];
    // 获取“跟进目标-map/options”数据
    const { loading: followLoading, data: followRecord } = useRequest(getFollowRecord, {
        cacheKey: 'FollowRecordCache',
        // 2 分钟不发送请求
        cacheTime: 2 * 60 * 1000
    });
    // state

    // 常量
    const { dealerId, id } = getUrlQueryObject();

    /**
     * 提交
     * @returns {Promise}        Promise
     */
    const onSubmit = () => {
        const { imageFileUrls = [] } = formData;
        const imgUrls = imageFileUrls.map((item) => item.split('?')[0]);
        console.log('submit', formData);
        return dispatch({
            type: 'myLogDetail/submitLogDeatil',
            payload: {
                ...formData,
                imageFileUrls: imgUrls
            }
        }).then((result) => {
            message({
                text: result.message || '修改跟进记录历史记成功。',
                time: 3,
                lock: true,
                // 关闭弹窗后跳转
                // eslint-disable-next-line jsdoc/require-jsdoc
                onClose: () => {
                    history.replace({
                        pathname: PAGE_URL.myLog,
                        search: getUrlQueryStringify({ dealerId }, '?')
                    });
                }
            });
        });
    };

    /**
     * 点击跳转上传页面
     */
    const handelClickUpload = () => {
        dispatch({
            type: 'myLogDetail/updateState',
            payload: { isOpenUpload: true }
        });
    };

    /**
     * 修改数据
     * @param {object}         data 数据对象
     */
    const handelEdit = (data) => {
        dispatch({
            type: 'myLogDetail/updateState',
            payload: { formData: data }
        });
    };

    // useEffect
    // 初始化
    useEffect(() => {
        if (!isEmpty(dealerId)) {
            dispatch({ type: 'myLogDetail/getLogDetail', payload: { dealerId, recordId: id } });
        }
        // 图片上传用
        if (isEmpty(bossToken)) {
            dispatch({ type: 'myLogDetail/getBossToken' });
        }
        return () => {
            dispatch({ type: 'myLogDetail/resetState' });
        };
    }, []);

    return (
        <>
            <Loader
                maskOpacity={0.6}
                open={detailLoading || submitLoading || bossTokenLoading || followLoading}
            />
            <div className={styles['mylog-detail-page']}>
                {formData && (
                    <ListItem
                        followRecord={followRecord}
                        itemData={formData}
                        onClickUpload={handelClickUpload}
                        onEdit={handelEdit}
                        onSubmit={onSubmit}
                    />
                )}
            </div>
            <MyUpload open={isOpenUpload} />
        </>
    );
});

export default connect(({ myLogDetail, loading }) => ({ ...myLogDetail, loading }))(myLogDetail);
