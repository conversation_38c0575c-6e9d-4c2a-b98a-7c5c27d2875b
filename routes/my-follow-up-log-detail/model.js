/**
 * 我的客户--跟进记录详情
 */
import { isEmpty, formatTime } from 'ttp-utils';
import * as api from './api.js';

/**
 * 默认参数
 * @returns {object}   默认参数
 */
const defaultFormData = () => ({
    // 经销商ID(被跟进人)
    dealerId: '',
    // 经销商名称(被跟进人)
    dealerName: '',
    // 跟进人boss账户ID
    dealerAdminId: '',
    // 跟进人姓名
    dealerAdminName: '',
    // 跟进记录创建时间
    createTime: '',
    // 跟进记录修改时间
    modifyTime: '',
    // 跟进方式  1电话，2微信， 3线下拜访 4其他
    followType: '',
    // 跟进结果
    followResult: '',
    // 计划下次跟进时间（后续跟进计划）)
    nextFollowTime: '',
    // 线上BD跟进目标 10、促出价；20、询价；30、投诉处理；40、客情维护；50、保证金问题；60、门店成交问题；70、过户售后问题；200、其他，线下BD 跟进目标 110、初次合作，介绍业务流程及签约；120、上拍品、撮成交；130、客情维护；140、售后处理；
    followGoal: '',
    // 跟进目标为 其他 时 填写内容
    followGoalExtend: '',
    // 跟进结论 1有意向，2无意向，3待定
    followConclusion: '',
    // 跟进结论(0,10,20,30,40,50,60,70,80,90,100) 为1有意向的意向度 支持0%-100% 阶梯选择，每隔10% 为一个阶梯
    intentionLevel: '',
    // 上传的图片资料url
    imageFileUrls: [],
    // 上传的视频资料url
    videoFileUrls: [],
    // 上传的音频资料url
    audioFileUrls: [],
    // 当前位置纬度
    latitude: '',
    // 当前位置经度
    longitude: '',
    //  商拓与经销商 通话录音 url
    callRecordingUrls: []
});

/**
 * 默认参数
 * @returns {object}   默认参数
 */
const initState = () => ({
    bossToken: null,
    formData: defaultFormData(),
    audioDomain: '',
    videoDomain: '',
    isOpenUpload: false
});

export default {
    namespace: 'myLogDetail',

    state: initState(),

    reducers: {
        /**
         * 重置状态
         * @returns {object}                     更新过的state
         */
        resetState() {
            return initState();
        },

        /**
         * 更新状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateState(state, action) {
            const { formData = {}, ...other } = action.payload;
            return {
                ...state,
                ...other,
                formData: {
                    ...state.formData,
                    ...formData
                }
            };
        },

        /**
         * 更新图片/视频/音频
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateFileList(state, action) {
            return {
                ...state,
                formData: {
                    ...state.formData,
                    ...action.payload
                }
            };
        }
    },

    effects: {
        /**
         * 获取商家数据
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @param   {Function}             param1.select  select
         * @returns {object}                              用户信息
         */
        *getLogDetail({ payload }, { call, put, select }) {
            const editType = payload.recordId ? 'update' : 'create';
            const { result } = yield call(api.getLogRecord, payload);
            const {
                dealerLabelName,
                dealerName,
                dealerId,
                dealerAdminName,
                followRecord = {}
            } = result || {};
            const formData =
                editType == 'update' ? { ...payload, ...followRecord } : { ...defaultFormData(), ...payload };
            const { nextFollowTime = '' } = formData;
            yield put({
                type: 'updateState',
                payload: {
                    formData: {
                        ...formData,
                        // 下次跟进时间，需要特殊处理
                        nextFollowTime:
                            isEmpty(nextFollowTime) ||
                            formatTime(nextFollowTime, 'yyyy年mm月dd日') == '2000年01月01日'
                                ? ''
                                : nextFollowTime,
                        dealerLabelName,
                        dealerName,
                        dealerAdminName,
                        dealerId
                    }
                }
            });
        },

        /**
         * 提交
         * @param   {object}               param0 入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @param   {Function}             param1.select  select
         * @returns {Promise}                             Promise
         */
        *submitLogDeatil({ payload }, { call, put, select }) {
            const { result } = yield call(api.submitLogRecord, payload);
            console.log('更新结果', result);
            return result;
        },
        /**
         * 获取上传七牛域名
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *getDomain({ payload }, { call, put }) {
            const { result } = yield call(api.getDomain, payload);
            yield put({
                type: 'updateState',
                payload: {
                    audioDomain: result.audioDomain,
                    videoDomain: result.videoDomain
                }
            });
        },
        /**
         * 提交上传视频图片数据
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *submitUpload({ payload }, { call, put }) {
            yield put({
                type: 'updateFileList',
                payload: {
                    ...payload
                }
            });
        },

        /**
         * 获取上传图片的TOKEN
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *getBossToken({ payload }, { call, put }) {
            const { result } = yield call(api.getBossToken, payload);
            yield put({
                type: 'updateState',
                payload: {
                    bossToken: result
                }
            });
        }
    }
};
