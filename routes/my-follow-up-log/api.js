/**
 * @file 我的客户-跟进记录列表
 */

/* requires JS */
import { postRequest, getRequest } from 'common/js/fetch.js';
import { PORT_URL } from 'common/js/constant.js';
import { setArrayToObject } from 'common/js/utils.js';

/**
 * 格式化 跟进记录详情页基础数据
 * @param    {Array}                 list 需要格式化的数据
 * @returns  {Array}                      格式化完的数据
 */
const formatFollowRecord = (list) => {
    if (Array.isArray(list)) {
        return list.map(({ code, description }) => ({
            value: code,
            label: description
        }));
    }
    return [];
};

/**
 * 获取商户签到信息数据
 * <AUTHOR>
 * @param    {object}                 data          请求参数对象
 * @returns  {Promise}                              返回一个Feach的Promise对象
 */
export function getLogList(data) {
    let opts = {
        url: PORT_URL.recordList,
        data
    };
    return postRequest(opts);
}

/**
 * 获取天润录音数据
 * <AUTHOR>
 * @param    {object}                 data          请求参数对象
 * @returns  {Promise}                              返回一个Feach的Promise对象
 */
export function getAudioList(data) {
    let opts = {
        url: PORT_URL.getAudioList,
        data
    };
    return getRequest(opts);
}

/**
 * 获取新增跟进记录详情页基础数据
 * <AUTHOR>
 * @param    {object}                 data          请求参数对象
 * @returns  {Promise}                              返回一个Feach的Promise对象
 */
export function getFollowRecord(data) {
    let opts = {
        url: PORT_URL.getFollowRecord,
        data
    };
    return postRequest(opts).then(({ result = {} }) => {
        // 跟进目标
        const followGoalOption = formatFollowRecord(result?.followUpGoals);
        // 跟进方式
        const followUpTypeOption = formatFollowRecord(result?.followUpMethods);

        return {
            // 跟进方式-options
            followUpTypeOption,
            // 跟进方式-map
            followUpTypeMap: setArrayToObject(followUpTypeOption),
            // 跟进目标-options
            followGoalOption,
            // 跟进目标-map
            followGoalMap: setArrayToObject(followGoalOption)
        };
    });
}
