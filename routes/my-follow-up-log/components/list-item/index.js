// JS
import React, { memo, useMemo, useState } from 'react';
import { useToggle, useRequest } from 'ahooks';
import { executeCB, isEmpty } from 'ttp-utils';
import { formatTimeData, getDealerAdminId } from 'common/js/utils';
import { FOLLOW_UP_TAG_MAP } from 'common/js/constant.js';
import { getAudioList } from 'routes/my-follow-up-log/api';
import cls from 'classnames';
// 组件
import { List, Icon, Button } from 'ttp-library';
import FileList from 'components/file-list';
import Panel from 'components/panel';
import Tag from 'components/tag';
import AudioDrawer from 'components/audio-drawer';
// LESS
import styles from './index.less';
/**
 * 获取dealerAdminId
 */
const dealerAdminId = getDealerAdminId();

/**
 * 副标题
 */
const SubtitleTag = memo((props) => {
    const { item } = props;
    const tagData = FOLLOW_UP_TAG_MAP[item];
    if (isEmpty(tagData)) {
        return '--';
    }
    return (
        <Tag classname={styles['right-tag']} themeColor={tagData?.type}>
            {tagData?.label}
        </Tag>
    );
});

/**
 * 我的客户数据ITEM
 * @param   {object}                     props props
 * @returns {React.ReactElement}               jsx
 */
const ListItem = memo((props) => {
    // props
    const { itemData, followRecord = {}, onEdit } = props;
    const { followGoalMap = {}, followUpTypeMap = {} } = followRecord || {};
    const { imageFileUrls = [], videoFileUrls = [], audioFileUrls = [] } = itemData;
    // state
    const [open, { toggle }] = useToggle(false);
    // 天润录音播放
    const [openDrawer, { setLeft: setCloseDrawer, setRight: setOpenDrawer }] = useToggle(false);
    const [audioList, setAudioList] = useState([]);
    const { loading, runAsync } = useRequest(getAudioList, {
        manual: true,
        debounceWait: 300,
        // eslint-disable-next-line jsdoc/require-jsdoc
        onSuccess: ({ result }) => {
            if (isEmpty(result)) {
                setAudioList([]);
            }
            setAudioList([result]);
        }
    });

    // 计算属性
    const showFileList = useMemo(() => {
        return imageFileUrls.length > 0 || videoFileUrls.length > 0 || audioFileUrls.length > 0;
    }, [imageFileUrls, videoFileUrls, audioFileUrls]);

    // 常量
    const title = `日期：${formatTimeData(itemData.createTime, 'yyyy年mm月dd日')}`;
    const pictuerListStyle = cls(styles['list-box-item'], {
        [styles['list-box-item-picture']]: showFileList
    });
    const followGoalStr = !isEmpty(itemData.followGoalExtend)
        ? `${followGoalMap[itemData.followGoal]}：${itemData.followGoalExtend}`
        : followGoalMap[itemData.followGoal] || '--';

    // 函数

    /**
     * 天润录音：查看录音
     */
    const handleCallClick = () => {
        setAudioList([]);
        runAsync({ callId: itemData.callId });
        setOpenDrawer();
    };
    /**
     * 天润录音：关闭游览窗口
     */
    const handleDrawerClose = () => {
        setCloseDrawer();
    };
    /**
     * 点击编辑按钮
     */
    const handleEdit = () => {
        executeCB(onEdit, itemData);
    };

    return (
        <>
            <Panel
                classname={styles['list-item']}
                onClick={toggle}
                open={open}
                subtitle={<SubtitleTag item={itemData.followConclusion} />}
                title={title}>
                <List className={styles['list-box']}>
                    <List.Item className={styles['list-box-item']} extra={itemData.dealerAdminName || '--'}>
                        跟进人姓名
                    </List.Item>
                    <List.Item
                        className={styles['list-box-item']}
                        extra={followUpTypeMap[itemData.followType] || '--'}>
                        跟进方式
                    </List.Item>
                    <List.Item className={styles['list-box-item']} extra={itemData.followResult || '--'}>
                        跟进结果
                    </List.Item>
                    <List.Item className={styles['list-box-item']} extra={followGoalStr}>
                        跟进目标
                    </List.Item>

                    <List.Item
                        className={styles['list-box-item']}
                        extra={<SubtitleTag item={itemData.followConclusion} />}>
                        跟进结论
                    </List.Item>
                    <List.Item
                        className={styles['list-box-item']}
                        extra={
                            <span className={styles['blue-text']}>
                                {itemData.intentionLevel >= 0 ? `${itemData.intentionLevel}%` : '--'}
                            </span>
                        }>
                        意向程度
                    </List.Item>
                    <List.Item
                        className={styles['list-box-item']}
                        extra={formatTimeData(itemData.nextFollowTime, 'yyyy年mm月dd日')}>
                        后续跟进计划
                    </List.Item>
                    <List.Item
                        className={pictuerListStyle}
                        extra={
                            showFileList ? (
                                <FileList audio={audioFileUrls} image={imageFileUrls} video={videoFileUrls} />
                            ) : (
                                '--'
                            )
                        }>
                        跟进资料
                    </List.Item>
                    {!isEmpty(itemData.callId) && itemData.callId > 0 && (
                        <List.Item
                            className={styles['list-box-item']}
                            extra={
                                <Button className={styles['upload-btn']} size='sm' skin='white'>
                                    查看录音
                                </Button>
                            }
                            key={itemData.callId}
                            onClick={handleCallClick}>
                            录音同步
                        </List.Item>
                    )}
                </List>
                {dealerAdminId == itemData.dealerAdminId && (
                    <div className={styles['list-box-edit-btn']} onClick={handleEdit}>
                        <Icon type='iconfont-edit' /> 编辑
                    </div>
                )}
            </Panel>
            {/* 天润录音 */}
            <AudioDrawer
                currentIndex={0}
                list={audioList}
                loading={loading}
                onClose={handleDrawerClose}
                open={openDrawer}
                title='跟进资料（音频）'
            />
        </>
    );
});

export default ListItem;
