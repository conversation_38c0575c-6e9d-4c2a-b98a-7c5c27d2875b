@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';
@import '../../../../common/less/list.less';

.list-item {
    margin-bottom: 10 / @rem;
    position: relative;

    :global {
        .white-box-title {
            font-size: 14 / @rem;
            font-weight: 400;
            color: @color-gray-3;
        }
    }

    .right-tag {
        margin: 0;
    }

    .list-box {
        margin: 0;
        position: relative;

        &:after {
            .setTopLine(@color-border);
        }

        &-item {
            padding: (10 / @rem) (5 / @rem);
            min-height: 40 / @rem;
            overflow: inherit;

            &:after {
                .setBottomLine(@color-border);
            }

            &:last-child:after {
                border-bottom-width: 0px;
            }

            .blue-text {
                color: @color-blue;
            }
        }

        &-item-picture {
            :global {
                .Saitama-list-item-wrap {
                    flex-direction: column;
                }

                .Saitama-list-item-content {
                    padding: 0;
                    width: 100%;
                    text-align: left;
                }

                .Saitama-list-item-extra {
                    width: 100%;
                    max-width: 100%;
                    margin: 0;
                }
            }
        }

        &-edit-btn {
            padding: (10 / @rem) (5 / @rem);
            min-height: 40 / @rem;
            text-align: center;
            color: @color-gray-9;
            position: relative;

            &:after {
                .setTopLine(@color-border);
            }
        }
    }

    .tag-btn {
        margin-left: 5 / @rem;
        padding: 0 (5 / @rem);
    }
}
