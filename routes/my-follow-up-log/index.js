// JS
import React, { memo, useEffect, useMemo } from 'react';
import { connect } from 'dva';
import { useRequest, useThrottleEffect } from 'ahooks';
import { getUrlQueryObject, getUrlQueryStringify } from 'ttp-utils';
import { PAGE_URL } from 'routes/router-config.js';
import { getFollowRecord } from 'routes/my-follow-up-log/api';
// 组件
import { Loader, Empty, Button } from 'ttp-library';
import BottomLine from 'components/bottom-line/index.js';
import Panel from 'components/panel/index.js';
import ListItem from './components/list-item/index.js';
// LESS
import styles from './index.less';
// IMG
import NoData from '../../src/images/no-log.png';

// 获取视口的高度
const viewportHeight = window.innerHeight;

/**
 * 我的客户-跟进列表
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const myLog = memo((props) => {
    // props
    const { history, dispatch, loading, dataList, currentPage, pageSize, noNextDataList } = props;
    const logLoading = loading.effects['myLog/getLogList'];
    // 获取“跟进目标-map/options”数据
    const { loading: followLoading, data: followRecord } = useRequest(getFollowRecord, {
        cacheKey: 'FollowRecordCache',
        // 2 分钟不发送请求
        cacheTime: 2 * 60 * 1000
    });
    // state
    // 计算属性
    const showNowData = useMemo(() => {
        return dataList.length == 0 && currentPage == 1 && !logLoading;
    }, [dataList, currentPage, logLoading]);

    // 常量
    const { dealerId } = getUrlQueryObject();

    // 函数

    /**
     * 获取跟进列表数据
     * @param   {object}               data  请求参数
     */
    const getLogList = (data) => {
        if (!logLoading) {
            dispatch({ type: 'myLog/getLogList', payload: { dealerId, currentPage, pageSize, ...data } });
        }
    };
    /**
     * 跳转新增日志页面
     */
    const handleAddLog = () => {
        history.push({
            pathname: PAGE_URL.myLogDetail,
            search: getUrlQueryStringify({ dealerId }, '?')
        });
    };

    /**
     * 跳转编辑日志页面
     * @param   {object}               item 数据对象
     */
    const handleEditLog = (item) => {
        history.push({
            pathname: PAGE_URL.myLogDetail,
            search: getUrlQueryStringify({ dealerId, id: item.id }, '?')
        });
    };

    // useEffect
    // 获取日志列表
    useEffect(() => {
        getLogList();
        return () => {
            console.log('myLog/resetState');
            dispatch({ type: 'myLog/resetState' });
        };
    }, []);
    // 监听页面滚动
    useThrottleEffect(
        () => {
            // 获取页面的总高度
            const totalHeight = document.documentElement.scrollHeight;
            // 获取当前滚动的位置
            const { top = 0 } = scroll || {};
            // 判断是否滚动到底部（考虑到可能的浏览器差异）
            if (Math.ceil(top + viewportHeight + 100) >= totalHeight && !noNextDataList && top != 0) {
                // 滚动到底部时执行的代码
                getLogList({ currentPage: currentPage + 1 });
            }
        },
        [scroll],
        { wait: 500 }
    );

    return (
        <>
            <Loader maskOpacity={0.6} open={logLoading || followLoading} />
            <div className={styles['log-list']}>
                <div className={styles['log-list-header']}>
                    <h2 className={styles['log-list-header-title']}>{name || ''}跟进记录</h2>
                </div>

                {showNowData ? (
                    <div className={styles['log-list-body']}>
                        <Panel classname={styles['no-data-panel']} open={true}>
                            <Empty description='暂无跟进记录' image={NoData} />
                            <Button block className={styles['add-btn']} onClick={handleAddLog}>
                                去添加跟进记录
                            </Button>
                        </Panel>
                    </div>
                ) : (
                    <div className={styles['log-list-body']}>
                        {dataList.map((item) => (
                            <ListItem
                                followRecord={followRecord}
                                itemData={item}
                                key={item.id}
                                onEdit={handleEditLog}
                            />
                        ))}

                        {noNextDataList && <BottomLine />}
                        <div className={styles['log-list-footer']}>
                            <Button block className={styles['add-btn']} onClick={handleAddLog}>
                                去添加跟进记录
                            </Button>
                        </div>
                    </div>
                )}
            </div>
        </>
    );
});

export default connect(({ myLog, loading }) => ({ ...myLog, loading }))(myLog);
