@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';

.log-list {
    padding-top: 64 / @rem;
    padding-bottom: 70 / @rem;

    &-header {
        width: 375 / @rem;
        height: 64 / @rem;
        padding: 8 / @rem;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: @color-page-bg;
        position: fixed;
        top: 0;
        left: 50%;
        margin-left: -187 / @rem;
        z-index: 10;

        &-title {
            font-weight: bold;
            font-size: 16px;
            color: @color-black;
            line-height: 22px;
        }
    }

    &-body {
        width: 100%;
        box-sizing: border-box;
    }

    &-footer {
        background-color: @color-white;
        width: 375 / @rem;
        height: 70 / @rem;
        padding: 0 20 / @rem;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        align-content: center;
        position: fixed;
        bottom: 0;
        left: 50%;
        margin-left: -187 / @rem;
        z-index: 10;
    }

    .no-data-panel {
        padding-bottom: (20 / @rem);

        &-body {
            overflow: hidden;
            min-height: 100 / @rem;
        }

        .add-btn {
            margin-top: 25 / @rem;
        }
    }
}
