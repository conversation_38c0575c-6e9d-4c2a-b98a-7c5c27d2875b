/**
 * 我的客户-跟进记录列表
 */
import * as api from './api.js';

/**
 * 默认参数
 * @returns {object}   默认参数
 */
const initState = () => ({
    // 没有下一页
    noNextDataList: false,
    dataList: [],
    currentPage: 1,
    pageSize: 20
});

export default {
    namespace: 'myLog',

    state: initState(),

    reducers: {
        /**
         * 重置状态
         * @returns {object}                     更新过的state
         */
        resetState() {
            return initState();
        },

        /**
         * 更新状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateState(state, action) {
            const { formData = {}, ...other } = action.payload;
            return {
                ...state,
                ...other,
                formData: {
                    ...state.formData,
                    ...formData
                }
            };
        }
    },

    effects: {
        /**
         * 获取商家数据
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @param   {Function}             param1.select  select
         * @returns {object}                              用户信息
         */
        *getLogList({ payload }, { call, put, select }) {
            const dataState = yield select((state) => state.myLog);
            const { result } = yield call(api.getLogList, payload);
            const { currentPage = 1, dataList = [] } = result || {};
            yield put({
                type: 'updateState',
                payload: {
                    currentPage: payload.currentPage,
                    dataList: Array.isArray(dataList)
                        ? currentPage == 1
                            ? dataList
                            : dataState.dataList.concat(dataList)
                        : [],
                    noNextDataList: dataList.length < dataState.pageSize
                }
            });
        }
    }
};
