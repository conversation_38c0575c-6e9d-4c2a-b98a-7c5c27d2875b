/**
 * @file 我的工作--客户跟进更多记录
 */

/* requires JS */
import { getRequest } from 'common/js/fetch.js';
import { PORT_URL } from 'common/js/constant.js';

/**
 * 获取客户跟进记录(按月分组)
 *
 * @param   {object}         data 请求参数
 * @returns {Promise}             返回请求结果
 */
export function getCustomerFollowMore(data) {
    let opts = {
        url: PORT_URL.customerFollowMore,
        data
    };
    return getRequest(opts);
}
