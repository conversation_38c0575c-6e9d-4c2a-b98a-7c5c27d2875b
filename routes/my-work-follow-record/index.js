// JS
import React, { memo, useEffect } from 'react';
import { connect } from 'dva';
// 组件
import { Loader } from 'ttp-library';
import CustomerFollowTable from '../my-work/components/custom-follow-table';
import SectionHeader from '../my-work/components/section-header';
// LESS
import styles from './index.less';

/**
 * 我的客户-跟进记录详情
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const myWorkFollowRecordPage = memo((props) => {
    // props
    const { dispatch, loading, followList } = props;
    const followLoading = loading.effects['myWorkFollowRecord/getCustomerFollowMore'];

    useEffect(() => {
        // 获取客户跟进数据
        dispatch({ type: 'myWorkFollowRecord/getCustomerFollowMore' });
    }, []);

    return (
        <>
            <Loader maskOpacity={0.6} open={followLoading} />
            <div className={styles['my-work-follow-record']}>
                <SectionHeader title='客户跟进记录' />
                <CustomerFollowTable followList={followList} />
            </div>
        </>
    );
});

export default connect(({ myWorkFollowRecord, loading }) => ({ ...myWorkFollowRecord, loading }))(
    myWorkFollowRecordPage
);
