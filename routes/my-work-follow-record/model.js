/**
 * 我的工作--客户跟进更多记录
 */
import * as api from './api.js';

/**
 * 默认参数
 * @returns {object}   默认参数
 */
const initState = () => ({
    followList: [],
    audioDomain: '',
    videoDomain: '',
    isOpenUpload: false
});

export default {
    namespace: 'myWorkFollowRecord',

    state: initState(),

    reducers: {
        /**
         * 重置状态
         * @returns {object}                     更新过的state
         */
        resetState() {
            return initState();
        },

        /**
         * 更新状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateState(state, action) {
            return {
                ...state,
                ...action.payload
            };
        }
    },

    effects: {
        /**
         * 获取客户跟进记录(少数几条)
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              更新过的state
         */
        *getCustomerFollowMore({ payload }, { call, put }) {
            const { result } = yield call(api.getCustomerFollowMore, payload);
            const followList = Array.isArray(result) ? result : [];
            console.log('followList', followList);
            yield put({
                type: 'updateState',
                payload: {
                    followList
                }
            });
        }
    }
};
