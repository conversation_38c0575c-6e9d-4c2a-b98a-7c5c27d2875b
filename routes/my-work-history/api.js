/**
 * @file 我的客户-跟进记录详情
 */

/* requires JS */
import { getRequest } from 'common/js/fetch.js';
import { PORT_URL } from 'common/js/constant.js';

/**
 * 获取更多绩效数据
 * <AUTHOR>
 * @param    {object}                 data 请求参数对象
 * @param {boolean} [isMy=true]           是否是我的绩效
 * @returns  {Promise}                     返回一个Feach的Promise对象
 */
export function getKpiList(data, isMy = true) {
    let opts = {
        url: isMy ? PORT_URL.kpiList : PORT_URL.groupKpiHistory,
        data
    };
    return getRequest(opts);
}
