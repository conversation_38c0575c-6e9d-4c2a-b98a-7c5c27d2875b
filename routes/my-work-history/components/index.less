@import '../../../common/less/validation.less';
@import '../../../common/less/mixins.less';
@import '../../../common/less/list.less';

.list-item {
    margin-top: 10 / @rem;
    margin-bottom: 0 / @rem;
    position: relative;
    border-radius: 6px 6px;

    :global {
        .white-box-title {
            font-size: 11 / @rem;
            font-weight: 400;
            color: @color-gray-3;
            display: flex;
            flex-direction: column;
            line-height: 16 / @rem;
        }
    }

    .no-item-data {
        padding: 0 0 (20 / @rem);

        :global {
            .Saitama-empty-image {
                max-width: 90 / @rem;
                max-height: 95 / @rem;
            }
        }
    }

    &-title {
        font-size: 14 / @rem;
        font-weight: 600;
        margin-right: 5 / @rem;
        color: #222;
    }

    &-extra {
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }

    &-rate-progress {
        width: 75 / @rem;
        margin-right: 5 / @rem;
    }

    &-rate {
        font-size: 11 / @rem;
        margin-right: 5 / @rem;
    }

    .right-tag {
        margin: 0;
    }

    .list-box {
        &-item {
            padding: (10 / @rem) (5 / @rem);
            min-height: 40 / @rem;
            position: relative;
            overflow: inherit;

            &:after {
                .setBottomLine(@color-border);
            }

            &:last-child:after {
                border-bottom-width: 0px;
            }

            &.no-border::after {
                border-bottom: none;
            }

            &.no-padding {
                padding-top: 0;
            }

            &-title-blue {
                font-weight: 400;
                font-size: 14p / @rem;
                color: #00a2e8;
            }

            h4 {
                font-weight: 600;
            }
        }

        &-error {
            position: static;
            padding: 5 / @rem;
        }

        &-image-list {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            align-content: center;
            justify-content: flex-start;

            .img-item,
            .video-item,
            .audio-item {
                width: 74 / @rem;
                height: 56 / @rem;
                border-radius: 4px;
                border: 1px solid @color-border;
                padding: (1 / @rem);
                margin-right: 6 / @rem;
                margin-bottom: 6 / @rem;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .video-item {
                background-color: rgba(0, 0, 0, 0.3);
            }
        }
    }

    .select-text {
        color: @color-gray-9;
        font-weight: 400;
    }

    .red-text {
        color: @color-red;
    }

    .tag-btn {
        margin-left: 5 / @rem;
        padding: 0 (5 / @rem);
    }

    .follow-goal-extend {
        font-weight: 600;
        font-size: 14 / @rem;
        color: #222222;
        position: relative;

        &:after {
            .setBottomLine(@color-border);
        }

        .follow-goal-extend-text {
            padding-bottom: 10 / @rem;
            padding-left: 4 / @rem;
        }
    }
}

.drawer-left-close {
    width: 20 / @rem;
    height: 20 / @rem;

    .drawer-close {
        font-size: 14 / @rem !important;
    }
}

.follow-goal-wrapper {
    padding: 0 (10 / @rem) (18 / @rem);

    .follow-goal-group {
        display: flex;
        align-items: center;
        // padding: 0 (12 / @rem);

        .follow-goal-tag {
            max-width: 100%;
            min-width: 80 / @rem;
            height: 28 / @rem;
            line-height: 28 / @rem;
            font-size: 14 / @rem;
            text-align: center;
            margin-right: 8 / @rem;
            padding: 0;
            box-sizing: border-box;
            padding: 0 (10 / @rem);

            &:last-child {
                margin-right: 0;
            }
        }

        :global {
            .Saitama-tag-box {
                max-width: 100%;
            }
        }
    }
}

.line {
    width: 100%;
    height: 1px;
    background-color: #ccc;
}

.btn-group {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    padding: 10 / @rem;

    .button {
        width: 156 / @rem;
        height: 44 / @rem;
    }

    .button-cancel {
        color: #999999;
        border-color: #999999;
    }
}

.input-textarea-wrapper {
    position: relative;

    .input-textarea {
        margin-top: 10 / @rem;
        color: @color-gray-3;
        padding: 10 / @rem;
        width: 100%;
        height: 60 / @rem;
        background: @color-gray-drop;
        border-radius: 6 / @rem;
        border-width: 0;
        resize: none;
        padding: 8 / @rem;
        font-size: 14 / @rem;
        line-height: 1.5;
    }

    .input-textarea-maxlength {
        position: absolute;
        bottom: 10 / @rem;
        right: 10 / @rem;
        font-weight: 400;
        font-size: 12 / @rem;
        color: #999999;
        line-height: 17 / @rem;
        text-align: left;
    }
}

.footer {
    height: 80 / @rem;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    background: #fff;

    .submit-btn {
        width: 330 / @rem;
        height: 46 / @rem;
    }
}
