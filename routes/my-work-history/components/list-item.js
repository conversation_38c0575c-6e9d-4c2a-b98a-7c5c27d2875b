// JS
import React, { memo, Fragment } from 'react';
import { useToggle } from 'ahooks';
import { formatTime, isEmpty } from 'ttp-utils';
// 组件
import { List, Empty } from 'ttp-library';
import Panel from 'components/panel';
import ProgressBar from 'components/progress-bar';
// LESS
import styles from './index.less';
// img
import NoLog from 'src/images/no-log.png';

/**
 * 我的客户数据ITEM
 * @param   {object}                     props props
 * @returns {React.ReactElement}               jsx
 */
const ListItem = memo((props) => {
    // props
    const { itemData, dateTime, isMy } = props;
    // state
    const [isOpen, { toggle: toggleDetail }] = useToggle(false);
    const wholeFinishRate = itemData.wholeFinishRate;
    return (
        <Panel
            classname={styles['list-item']}
            onClick={toggleDetail}
            open={isOpen}
            subtitle={
                <div className={styles['list-item-extra']}>
                    <span className={styles['list-item-rate']}>
                        完成率{isEmpty(wholeFinishRate) ? '0%' : wholeFinishRate}
                    </span>
                    <ProgressBar className={styles['list-item-rate-progress']} progress={wholeFinishRate} />
                    <span className={styles['list-item-detail']}>详情</span>
                </div>
            }
            title={
                isMy ? (
                    <span className={styles['list-item-title']}>{formatTime(dateTime, 'yyyy年mm月')}</span>
                ) : (
                    <>
                        <span className={styles['list-item-title']}>{itemData.memberName}</span>
                        <span>{formatTime(dateTime, 'yyyy年mm月')}</span>
                    </>
                )
            }>
            {Array.isArray(itemData.kpiList) && itemData.kpiList.length > 0 ? (
                <List className={styles['list-box']}>
                    {itemData.kpiList.map((item, index) => {
                        return isMy ? (
                            <Fragment key={itemData.memberName + item.indicatorName}>
                                <List.Item
                                    className={styles['list-box-item']}
                                    extra={<h4>{item.indicatorName}</h4>}>
                                    <span className={styles['list-box-item-title-blue']}>
                                        绩效指标{index + 1}
                                    </span>
                                </List.Item>
                                <List.Item className={styles['list-box-item']} extra={item.targetValue}>
                                    目标值
                                </List.Item>
                                <List.Item className={styles['list-box-item']} extra={item.achievementValue}>
                                    当前进展
                                </List.Item>
                                <List.Item className={styles['list-box-item']} extra={item.achievementRate}>
                                    完成率
                                </List.Item>
                            </Fragment>
                        ) : (
                            <Fragment key={itemData.memberName + item.indicatorName}>
                                <List.Item
                                    className={styles['list-box-item']}
                                    extra={<h4>{item.indicatorName}</h4>}>
                                    <span className={styles['list-box-item-title-blue']}>
                                        指标{index + 1}
                                    </span>
                                </List.Item>
                                <List.Item className={styles['list-box-item']} extra={item.targetValue}>
                                    目标值
                                </List.Item>
                                <List.Item className={styles['list-box-item']} extra={item.targetValueWeight}>
                                    目标权重
                                </List.Item>
                                <List.Item className={styles['list-box-item']} extra={item.achievementValue}>
                                    完成进展
                                </List.Item>
                                <List.Item className={styles['list-box-item']} extra={item.achievementRate}>
                                    完成率
                                </List.Item>
                            </Fragment>
                        );
                    })}
                </List>
            ) : (
                <Empty className={styles['no-item-data']} description='尚未添加绩效指标。' image={NoLog} />
            )}
        </Panel>
    );
});

export default ListItem;
