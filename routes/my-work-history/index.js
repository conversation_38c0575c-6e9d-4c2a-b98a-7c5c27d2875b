// JS
import React, { memo, useEffect, useRef, useState } from 'react';
import { connect } from 'dva';
import { useSetState, useUpdateEffect } from 'ahooks';
import { formatTime, getUrlQueryObject } from 'ttp-utils';
import { PAGE_URL } from 'routes/router-config.js';
// 组件
import { Loader, DropdownMenu, Calendar, Empty } from 'ttp-library';
import WorkerDrawer from 'components/worker-drawer/index.js';
import ProgressCircle from 'components/progress-bar/circle.js';
import ListItem from './components/list-item.js';
const { DropdownItem } = DropdownMenu;
// LESS
import styles from './index.less';

/**
 * 我的客户-跟进记录详情
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const myWorkHistoryPage = memo((props) => {
    // props
    const { dispatch, dataList, myKpiData, loading } = props;
    const Loading = loading.effects['myWorkHistory/getKpiList'];
    // state
    const [menuValue, setMenuValue] = useSetState({
        date: {
            label: formatTime(new Date(), 'YYYY年MM月'),
            value: new Date()
        },
        worker: {
            label: '员工',
            value: []
        }
    });
    const [workerValue, setWorkerValue] = useState([]);
    const [dateValue, setDateValue] = useState(new Date());
    const [open, setOpen] = useState(false);
    // 时间进度

    // ref
    const dropRef = useRef(null);
    const topMenuRef = useRef(null);
    // 常量
    // 通过url判断是否是我的更多绩效
    const currentUrl = window.location.pathname;
    const isMy = currentUrl === PAGE_URL.myWorkHistory;
    const teamId = isMy ? '' : getUrlQueryObject().id;
    const labelList = [
        {
            itemKey: 'date',
            defaultLabel: '日期'
        },
        {
            itemKey: 'worker',
            defaultLabel: '员工'
        }
    ];
    // 如果是我的历史绩效，只需要日期，不需要员工
    if (isMy) {
        labelList.splice(1, 1);
    }

    // 函数
    /**
     * 获取详情数据
     */
    const getDetailData = () => {
        console.log('workerValue:', workerValue);
        dispatch({
            type: 'myWorkHistory/getKpiList',
            payload: {
                teamId,
                appraisalTimeInterval: formatTime(dateValue, 'YYYY-MM'),
                filterMemberIds: isMy ? '' : workerValue.join(','),
                isMy
            }
        });
    };

    /**
     * 下拉菜单change
     * @param  {object} value   选中的值
     */
    const onMenuChange = (value) => {
        console.log('menu:', value);
        setMenuValue({
            ...menuValue,
            ...value
        });
    };

    /**
     * 日历change
     * @param {string} value   选中的值
     */
    const handleCalendarChange = (value) => {
        console.log('date:', value);
        setDateValue(value);
        setMenuValue({
            ...menuValue,
            date: {
                label: formatTime(value, 'YYYY年MM月'),
                value: formatTime(value, 'YYYY-MM')
            }
        });
        dropRef.current?.closeAllDrawer();
        // getDetailData();
    };

    /**
     * 打开菜单
     * @param {string} itemKey  key
     */
    const onOpenItem = (itemKey) => {
        if (itemKey === 'worker') {
            setOpen(true);
        }
    };
    /**
     * 关闭弹框
     */
    const handleClose = () => {
        setOpen(false);
        dropRef.current?.closeAllDrawer();
    };

    /**
     * 确定选择
     * @param {object} checkedData   data
     * @param {object}  rowData 行数据
     */
    const handleOk = (checkedData, rowData) => {
        console.log('onOK checkedData:', checkedData);
        console.log('onOK rowData:', rowData);
        setWorkerValue(checkedData);
        setMenuValue({
            ...menuValue,
            worker: {
                label: `员工（${checkedData.length}）`,
                value: rowData.value
            }
        });
        // getDetailData();
    };

    // useEffect
    // 初始化
    useEffect(() => {
        getDetailData();
        return () => {
            dispatch({ type: 'myWorkHistory/resetState' });
        };
    }, []);
    // 筛选条件更新后，请求列表数据
    useUpdateEffect(() => {
        getDetailData();
    }, [dateValue, workerValue]);

    return (
        <>
            <Loader maskOpacity={0.6} open={Loading} />
            <div className={styles['my-work-history']}>
                <div className={styles['top-header']} ref={topMenuRef}>
                    <h4 className={styles['title']}>{isMy ? '我的历史绩效' : '员工历史绩效'}</h4>
                    <DropdownMenu
                        className={workerValue.length > 0 ? styles['dropdown-menu-active'] : ''}
                        labelList={labelList}
                        menuType='tag'
                        onChange={onMenuChange}
                        onOpenItem={onOpenItem}
                        ref={dropRef}
                        value={menuValue}>
                        <DropdownItem itemKey='date'>
                            <Calendar
                                defaultView='year'
                                maxDetail='year'
                                onChange={handleCalendarChange}
                                value={dateValue}
                            />
                        </DropdownItem>
                    </DropdownMenu>
                </div>
                <div className={styles['my-work-history-body']}>
                    {isMy ? (
                        <ListItem
                            dateTime={formatTime(dateValue, 'YYYY-MM')}
                            isMy={isMy}
                            itemData={myKpiData}
                        />
                    ) : dataList.length ? (
                        dataList.map((item) => (
                            <ListItem
                                dateTime={formatTime(dateValue, 'YYYY-MM')}
                                isMy={isMy}
                                itemData={item}
                                key={`${item.memberId}_${dateValue}`}
                            />
                        ))
                    ) : (
                        <Empty />
                    )}
                </div>
                {/* 时间进度 */}
                <ProgressCircle />
            </div>
            {/* 员工筛选 */}
            <WorkerDrawer
                multiple={true}
                onChange={handleOk}
                onClose={handleClose}
                open={open}
                teamId={teamId}
                value={workerValue}
            />
        </>
    );
});

export default connect(({ myWorkHistory, loading }) => ({ ...myWorkHistory, loading }))(myWorkHistoryPage);
