@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';
@import '../../common/less/dropdown.less';

.my-work-history {
    padding-bottom: 10 / @rem;

    .libraryDrawer();
    .libraryCalendar();
    .librarySearchbar();

    .top-header {
        position: fixed;
        top: 0;
        height: 95 / @rem;
        background: #fff;
        z-index: 11;
        width: 100%;
        max-width: 750px;
    }

    .title {
        font-weight: 600;
        font-size: 16 / @rem;
        color: #000000;
        line-height: 22 / @rem;
        margin-bottom: 5 / @rem;
        padding: 15 / @rem 15 / @rem 0;
    }

    .my-work-history-body {
        margin-top: 105 / @rem;
    }

    :global {
        .Saitama-dropdown-menu__item-radius {
            background-color: #fff;
            border: 1px solid #f2f2f2;
            border-radius: 21 / @rem;
            margin-left: 15 / @rem;
            max-width: 120 / @rem;

            &:first-child {
                background: rgba(0, 162, 232, 0.1);
                border: 1px solid #00a2e8;

                .Saitama-dropdown-menu__title-selected {
                    font-weight: 600;
                    font-size: 14 / @rem;
                    color: #00a2e8;
                }
            }

            .Saitama-dropdown-menu__title-selected {
                color: #222;
            }
        }
        .Saitama-dropdown-menu__title-arrow {
            border-color: transparent transparent #222 #222;
        }
    }
}

.dropdown-menu-active {
    :global {
        .Saitama-dropdown-menu__item-radius {
            background: rgba(0, 162, 232, 0.1);
            border: 1px solid #00a2e8;
            max-width: 120 / @rem;

            .Saitama-dropdown-menu__title-selected {
                font-weight: 600;
                font-size: 14 / @rem;
                color: #00a2e8;
            }
        }
        .Saitama-list-group-tag .Saitama-list-group-box .Saitama-tag.Saitama-checkbox {
            width: calc((100% - 0.75rem * 3) / 4);
            border-radius: 6 / @rem;
            &:nth-child(4n) {
                margin-right: 0;
            }
        }

        .Saitama-tag-box {
            font-size: 12 / @rem;
        }
    }
}

.librarySelectPerson();
