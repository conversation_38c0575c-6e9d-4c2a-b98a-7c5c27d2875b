/**
 * 我的客户--跟进记录详情
 */
import * as api from './api.js';

/**
 * 默认参数
 * @returns {object}   默认参数
 */
const initState = () => ({
    dataList: [],
    myKpiData: {
        kpiList: [],
        wholeFinishRate: ''
    }
});

export default {
    namespace: 'myWorkHistory',

    state: initState(),

    reducers: {
        /**
         * 重置状态
         * @returns {object}                     更新过的state
         */
        resetState() {
            return initState();
        },

        /**
         * 更新状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateState(state, action) {
            return {
                ...state,
                ...action.payload
            };
        }
    },

    effects: {
        /**
         * 获取商家数据
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @param   {Function}             param1.select  select
         * @returns {object}                              用户信息
         */
        *getKpiList({ payload }, { call, put }) {
            const { isMy, ...others } = payload;
            const { result } = yield call(api.getKpiList, others, isMy);
            yield put({
                type: 'updateState',
                payload: {
                    dataList: Array.isArray(result) ? result : [],
                    myKpiData: isMy
                        ? {
                              kpiList: Array.isArray(result.kpiList) ? result.kpiList : [],
                              wholeFinishRate: result.wholeAchievementRate
                          }
                        : {}
                }
            });
        }
    }
};
