/**
 * @file 我的客户-跟进记录详情
 */

/* requires JS */
import { getRequest } from 'common/js/fetch.js';
import { PORT_URL } from 'common/js/constant.js';

/**
 * 获取商户签到信息数据
 * <AUTHOR>
 * @param    {object}                 data 请求参数对象
 * @returns  {Promise}                     返回一个Feach的Promise对象
 */
export function getWorkTrace(data) {
    let opts = {
        url: PORT_URL.getWorkTracePort,
        data
    };
    return getRequest(opts);
}
