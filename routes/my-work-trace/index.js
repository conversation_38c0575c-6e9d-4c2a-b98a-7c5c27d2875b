/* eslint-disable no-undef */
// JS
import React, { memo, useEffect } from 'react';
import { connect } from 'dva';
// 组件
import { Loader, Empty } from 'ttp-library';
// LESS
import styles from './index.less';
/**
 * 百度地图类
 */
class BaiduMap {
    /**
     * constructor
     * @param  {string}  mapId   mapId
     * @param {Array} path  路径数据
     */
    constructor(mapId, path) {
        this.mapId = mapId;
        this.map = null;
        this.path = path;
        this.init();
    }

    /**
     * 初始化
     */
    init() {
        const centerIndex = this.path.length > 1 ? Math.ceil(this.path.length / 2 - 1) : 0;
        this.map = new BMapGL.Map(this.mapId); // 创建Map实例
        this.map.centerAndZoom(
            new BMapGL.Point(this.path[centerIndex].longitude, this.path[centerIndex].latitude),
            18
        ); // 初始化地图，设置中心点坐标和地图级别
        this.map.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
        this.map.setTilt(20); // 设置地图倾斜角度为0
        let point = [];
        for (let i = 0; i < this.path.length; i++) {
            point.push(new BMapGL.Point(this.path[i].longitude, this.path[i].latitude));
        }
        let polyline = new BMapGL.Polyline(point, {
            strokeColor: '#ff0000',
            strokeWeight: 8,
            strokeOpacity: 0.8
        });
        this.map.addOverlay(polyline); // 增加折线
        // 创建位置点
        let point3dStart = new BMapGL.Point(this.path[0].longitude, this.path[0].latitude);
        // 创建带高度的点
        let marker3dStart = new BMapGL.Marker3D(point3dStart, 1000, {
            size: 30,
            shape: 1,
            fillColor: '#00A2E8',
            fillOpacity: 0.8
        });
        // 创建位置点
        const lastPoint = this.path.length - 1;
        let point3dEnd = new BMapGL.Point(this.path[lastPoint].longitude, this.path[lastPoint].latitude);
        // 创建带高度的点
        let marker3dEnd = new BMapGL.Marker3D(point3dEnd, 1000, {
            size: 30,
            shape: 1,
            fillColor: '#454399',
            fillOpacity: 0.6
        });
        // 将起使点添加到地图上
        this.map.addOverlay(marker3dStart);
        this.map.addOverlay(marker3dEnd);
        // 手动设置地图边界
        this.map.setViewport(point);
    }
}

/**
 * 我的客户-跟进记录详情
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const myWorkTrace = memo((props) => {
    // props
    const { dispatch, loading, workingTracks = [], todayKilometres = 0 } = props;
    const Loading = loading.effects['myWorkTrace/getWorkTrace'];

    /**
     * 获取员工工作轨迹数据
     *
     * @returns  {Promise}    promise
     */
    const getTraceData = () => {
        return dispatch({
            type: 'myWorkTrace/getWorkTrace'
        });
    };

    useEffect(() => {
        getTraceData().then((result) => {
            console.log('workingTracks:', result);
            if (!result.workingTracks.length) {
                return;
            }
            new BaiduMap('baiduMap', result.workingTracks);
        });
    }, []);
    return (
        <>
            <Loader maskOpacity={0.6} open={Loading} />
            <div className={styles['my-work-trace']}>
                <div className={styles['my-work-trace-container']}>
                    <div className={styles['my-work-trace-map-title']}>
                        <p>
                            <i className='icon-great' />
                        </p>
                        <p>辛苦啦！</p>
                    </div>
                    <p className={styles['my-work-trace-desc']}>
                        您今日工作轨迹共<span className={styles['text-blue']}>{todayKilometres}</span>
                        千米
                    </p>
                </div>
                <div className={styles['my-work-trace-map']}>
                    {workingTracks.length > 0 ? (
                        <div className={styles['my-work-map-body']} id='baiduMap' />
                    ) : (
                        <Empty description='暂无工作轨迹数据' />
                    )}
                </div>
                {/* <p className={styles['my-work-trace-total']}>
                    本月您为工作共贡献了<span className={styles['text-blue']}>{todayKilometres}</span>千米
                </p> */}
            </div>
        </>
    );
});

export default connect(({ myWorkTrace, loading }) => ({ ...myWorkTrace, loading }))(myWorkTrace);
