/**
 * @file 我的客户-跟进记录详情
 */

/* requires JS */
import { getRequest } from 'common/js/fetch.js';
import { PORT_URL } from 'common/js/constant.js';

/**
 * 获取KPI列表
 *
 * @param   {object}         data 请求参数
 * @returns {Promise}             返回请求结果
 */
export function getKPIList(data) {
    let opts = {
        url: PORT_URL.kpiList,
        data
    };
    return getRequest(opts);
}

/**
 * 获取客户分层数据
 *
 * @param   {object}         data 请求参数
 * @returns {Promise}             返回请求结果
 */
export function getCustomerOverview(data) {
    let opts = {
        url: PORT_URL.customerOverview,
        data
    };
    return getRequest(opts);
}

/**
 * 获取客户跟进记录(少数几条)
 *
 * @param   {object}         data 请求参数
 * @returns {Promise}             返回请求结果
 */
export function getCustomerFollow(data) {
    let opts = {
        url: PORT_URL.customerFollow,
        data
    };
    return getRequest(opts);
}
