import React, { memo } from 'react';
import styles from './index.less';
import { isEmpty } from 'ttp-utils/lib';

const CustomerFollowTable = memo((props) => {
    const { followList, visitCoverageRateDesc } = props;

    return (
        <div>
            <table className={styles['follow-table']}>
                <thead>
                    <tr>
                        <th>日期</th>
                        <th>跟进客户数</th>
                        <th>
                            线上拜访
                            <br />
                            客户数
                        </th>
                        <th>
                            线下拜访
                            <br />
                            客户数
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {followList.map((item) => (
                        <tr key={item.hash}>
                            <td>{item.time}</td>
                            <td>{item.totalFollowCustomer}</td>
                            <td>{item.onlineFollowCustomer}</td>
                            <td>{item.offlineFollowCustomer}</td>
                        </tr>
                    ))}
                    {!isEmpty(visitCoverageRateDesc) && (
                        <tr>
                            <td className={styles['coverage']} colSpan='4'>
                                {visitCoverageRateDesc}
                            </td>
                        </tr>
                    )}
                </tbody>
            </table>
        </div>
    );
});

export default CustomerFollowTable;
