@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';

.follow-table {
    border-radius: 8 / @rem;
    border-collapse: collapse;
    overflow: hidden;
    margin: 0 8 / @rem;
    justify-content: space-between;
    width: (375 - 16) / @rem;

    thead > tr {
        background-color: #f1fbff;
        height: 48 / @rem;
        & > th {
            color: @color-blue;
            font-size: 12 / @rem;
            font-weight: bold;
            text-align: center;
            border-bottom: 1px solid @color-blue;
        }
    }

    tbody > tr {
        background-color: #fff;
        height: 33 / @rem;
        border-bottom: 1px solid #ededed;
        &:last-child {
            border-bottom: none;
        }

        & > td {
            color: @color-gray-3;
            font-weight: bold;
            font-size: 14 / @rem;
            text-align: center;
            padding: 5 / @rem;
            border-left: 1px solid #ededed;
            &:first-child {
                border-left: none;
                font-size: 12 / @rem;
                width: 85 / @rem;
                font-weight: normal;
            }
        }
        .coverage {
            text-align: start;
            padding-left: 10 / @rem;
        }
    }
}
