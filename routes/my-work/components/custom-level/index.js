import React, { memo } from 'react';
import cls from 'classnames';
import styles from './index.less';
import SectionHeader from '../section-header';

/**
 * 客户分层数据
 * @param   {object}                    props props
 * @returns {React.ReactElement}              jsx
 */
const CustomLevel = memo((props) => {
    const { overviewList } = props;

    return (
        <div className={styles['customer-level']}>
            <SectionHeader hiddenMarginBottom={true} title='客户分层数据' />
            <ul className={styles['item-container']}>
                {overviewList.map((item, index) => (
                    <CustomerLevelItem {...item} key={index} />
                ))}
            </ul>
        </div>
    );
});

/**
 * 客户分层数据ITEM
 * @param   {object}                    props props
 * @returns {React.ReactElement}              jsx
 */
const CustomerLevelItem = memo((props) => {
    const { compareTips, compareType, compareValue, label, level, unit, value } = props;
    const iconClass = cls({
        ['icon-grow-up']: compareType === 1,
        ['icon-grow-down']: compareType === 2
    });
    const growthRateClass = cls({
        [styles['growth-rate-up']]: compareType === 1,
        [styles['growth-rate-down']]: compareType === 2
    });

    return (
        <li className={styles['customer-level-item']}>
            <div className={styles['level']}>
                {/* <img alt='icon' className={styles['level-icon']} src={icon} /> */}
                <i className={`icon-${level}`}> {level} </i>
                <span className={styles['level-title']}>{label}</span>
            </div>
            <div className={styles['count']}>
                <span className={styles['count-label']}>{value}</span>
                <span className={styles['count-unit']}>{unit}</span>
            </div>
            <div className={styles['growth']}>
                <span className={styles['growth-label']}>{compareTips}</span>
                <i className={iconClass} />
                <span className={growthRateClass}>{compareValue}</span>
            </div>
        </li>
    );
});

export default CustomLevel;
