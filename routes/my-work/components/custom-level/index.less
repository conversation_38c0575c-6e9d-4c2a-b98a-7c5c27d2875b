@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';

.customer-level {
    .item-container {
        display: flex;
        justify-content: flex-start; /* 修改为左对齐 */
        flex-wrap: wrap; /* 允许子元素换行 */
        margin-left: 10 / @rem;
    }
}

.customer-level-item {
    border-radius: 8 / @rem;
    background-color: #ffffff;
    padding-top: 8 / @rem;
    width: 112 / @rem;
    height: 84 / @rem;
    text-align: center;
    margin-right: 8 / @rem;
    margin-top: 10 / @rem; /* 元素之间的垂直间距 */

    .level {
        display: flex;
        justify-content: center;
        align-items: center;

        &-title {
            color: #222222;
            font-size: 14 / @rem;
            line-height: 17 / @rem;
        }
        &-icon {
            width: 15 / @rem;
            height: 15 / @rem;
            margin-right: 4 / @rem;
        }
    }

    .count {
        margin: (6 / @rem) 0;
        &-label {
            color: #222222;
            font-size: 24 / @rem;
            font-weight: bold;
        }
        &-unit {
            color: #222222;
            font-size: 14 / @rem;
        }
    }

    .growth {
        &-label {
            color: #999999;
            font-size: 11 / @rem;
            margin-right: 5 / @rem;
        }
        &-rate-up {
            margin-left: 5 / @rem;
            font-size: 11 / @rem;
            color: #fa2256;
        }
        &-rate-down {
            margin-left: 5 / @rem;
            font-size: 11 / @rem;
            color: #2fb87a;
        }
    }
}
