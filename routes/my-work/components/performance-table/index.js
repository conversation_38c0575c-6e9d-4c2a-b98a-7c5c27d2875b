import React, { memo } from 'react';
import SectionHeader from '../section-header';
import styles from './index.less';

const PerformanceTable = memo((props) => {
    const { kpiList, onClick } = props;

    return (
        <div>
            <SectionHeader btnText='更多绩效 &gt;' onClick={onClick} title='我的绩效' />
            <table className={styles['performance-table']}>
                <thead>
                    <tr>
                        <th>绩效指标</th>
                        <th>目标值</th>
                        <th>当前进展</th>
                        <th>完成率</th>
                    </tr>
                </thead>
                <tbody>
                    {kpiList.map((item) => (
                        <tr key={item.indicatorCode}>
                            <td>{item.indicatorName}</td>
                            <td>{item.targetValue}</td>
                            <td>{item.achievementValue}</td>
                            <td>{item.achievementRate}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
});

export default PerformanceTable;
