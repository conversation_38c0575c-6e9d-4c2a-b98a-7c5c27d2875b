@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';

.performance-table {
    border-radius: 8 / @rem;
    overflow: hidden;
    border-collapse: collapse;
    margin: 0 8 / @rem;
    justify-content: space-between;
    width: (375 - 16) / @rem;

    & > thead > tr {
        background-color: #f1fbff;
        height: 32 / @rem;
        // border-bottom: 1px solid #00a2e880;

        & > th {
            color: @color-blue;
            font-size: 14 / @rem;
            font-weight: bold;
            text-align: center;
            border-bottom: 1px solid @color-blue;
        }
    }

    & > tbody > tr {
        background-color: #fff;
        height: 40 / @rem;
        border-bottom: 1px solid #ededed;

        &:last-child {
            border-bottom: none;
        }

        & > td {
            color: @color-gray-3;
            font-weight: bold;
            font-size: 14 / @rem;
            text-align: center;
            border-left: 1px solid #ededed;

            &:first-child {
                border-left: none;
                font-weight: normal;
            }
        }
    }
}
