import React, { memo, useState } from 'react';
import { executeCB } from 'ttp-utils';
import cls from 'classnames';
import styles from './index.less';

const SectionHeader = memo((props) => {
    const { title, onClick, btnText, hiddenMarginTop, hiddenMarginBottom } = props;
    const btnVisible = btnText && btnText.length > 0;

    const headerClass = cls({
        [styles['section-header']]: true,
        [styles['no-top']]: hiddenMarginTop == true,
        [styles['no-bottom']]: hiddenMarginBottom == true
    });

    /**
     * 点击事件
     */
    const handleClick = () => {
        executeCB(onClick);
    };

    return (
        <div className={headerClass}>
            <span className={styles['section-header-title']}>{title}</span>
            {btnVisible && (
                <span className={styles['section-header-more']} onClick={handleClick}>
                    {btnText}
                </span>
            )}
        </div>
    );
});

export default SectionHeader;
