import React, { memo, useState } from 'react';
import { executeCB } from 'ttp-utils';
import { Button } from 'ttp-library';

import cls from 'classnames';
import styles from './index.less';
import { useMount } from 'ahooks';

const MyWorkTabs = memo((props) => {
    // props
    const { tagStyle, onChange, getHeight, current } = props;

    const [activeTab, setActiveTab] = useState('performance');
    const mainList = [
        { id: 'performance', name: '我的业绩' },
        { id: 'task', name: '我的任务', disabled: true }
    ];

    useMount(() => {
        setActiveTab(mainList[current].id);
    });
    /**
     * 获取ref
     * @param {object} ref ref
     */
    const getRef = (ref) => {
        if (ref) {
            const { height } = ref.getBoundingClientRect();
            executeCB(getHeight, height);
        }
    };

    /**
     * tab切换点击
     * @param {string} tabKey 当前tab key
     */
    const handleTabClick = (tabKey) => {
        setActiveTab(tabKey);
        executeCB(onChange, tabKey);
    };

    return (
        <div className={styles['work-tab']} ref={getRef} style={tagStyle}>
            {mainList.map((item) => {
                const itemClass = cls(styles['work-tab-item'], {
                    [styles['work-tab-item--active']]: activeTab == item.id
                });
                return (
                    <Button
                        className={itemClass}
                        disabled={item.disabled}
                        key={item.id}
                        onClick={() => handleTabClick(item.id)}
                        radius='round'>
                        {item.name}
                    </Button>
                );
            })}
        </div>
    );
});

export default MyWorkTabs;
