@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';

.work-tab {
    position: fixed;
    width: 375 / @rem;
    min-width: 375 / @rem;
    height: 52 / @rem;
    background: @color-white;
    padding: (12 / @rem) 0 (12 / @rem) (48 / @rem);
    align-items: center;

    &-item {
        width: 130 / @rem;
        height: 28 / @rem;
        line-height: 28 / @rem;
        background: #ffffff;
        border-radius: 14 / @rem;
        border: 1px solid @color-gray-9;
        color: @color-gray-9;
        font-size: 14 / @rem;
        text-align: center;
        margin-right: 20 / @rem;

        &--active {
            border: 1px solid @color-blue;
            color: @color-blue;
            background-color: #e5f5fc;
        }
    }
}
