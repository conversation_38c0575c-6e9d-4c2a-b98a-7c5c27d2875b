// JS
import React, { memo, useEffect, useState } from 'react';
import { connect } from 'dva';
import { MY_CLIENT_TAB } from 'common/js/constant.js';
import { PAGE_URL } from 'routes/router-config.js';
// 组件
import { Loader } from 'ttp-library';
import TabNav from 'components/tab-nav/index.js';
// import MyWorkTabs from './components/work-tag-bar';
import CustomLevel from './components/custom-level';
import PerformanceTable from './components/performance-table';
import CustomerFollowTable from './components/custom-follow-table';
import SectionHeader from './components/section-header';
// LESS
import styles from './index.less';

/**
 * 我的客户-跟进记录详情
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const myWorkPage = memo((props) => {
    // props
    const { history, dispatch, loading, kpiList, overviewList, followList, visitCoverageRateDesc } = props;
    const kpiLoading = loading.effects['myWork/getKPIList'];
    const overviewLoading = loading.effects['myWork/getCustomerOverview'];
    const followLoading = loading.effects['myWork/getCustomerFollow'];

    // state
    const [navHeight, setNavHeight] = useState(0);
    // const [barHeight, setBarHeight] = useState(0);

    /**
     * 导航tab切换点击
     * @param {object}         item 当前选中数据
     */
    const handleNavChange = (item) => {
        history.push(item.url);
    };

    // /**
    //  * tab切换点击
    //  * @param {string}         key 当前选中key
    //  */
    // const handleTabChange = (key) => {
    //     console.log(key);
    // };

    /**
     * 查看工作轨迹
     */
    const handlekWorkTrack = () => {
        history.push({ pathname: PAGE_URL.myWorkTrace });
    };

    /**
     * 更多绩效
     */
    const handlePerformanceMore = () => {
        history.push({ pathname: PAGE_URL.myWorkHistory });
    };

    /**
     * 更多记录
     */
    const handleCustomerFollowMore = () => {
        history.push({ pathname: PAGE_URL.myWorkFollowRecord });
    };

    useEffect(() => {
        // 获取KPI数据
        dispatch({ type: 'myWork/getKPIList' });
        // 获取客户分层数据
        dispatch({ type: 'myWork/getCustomerOverview' });
        // 获取客户跟进数据
        dispatch({ type: 'myWork/getCustomerFollow' });
    }, []);

    return (
        <>
            <Loader maskOpacity={0.6} open={kpiLoading || overviewLoading || followLoading} />
            <div className={styles['my-work']}>
                <TabNav
                    current={0}
                    getHeight={setNavHeight}
                    list={MY_CLIENT_TAB}
                    onChange={handleNavChange}
                />
                {/* <MyWorkTabs
                    current={0}
                    getHeight={setBarHeight}
                    onChange={handleTabChange}
                    tagStyle={{ top: navHeight }}
                /> */}
                <div className={styles['my-work-body']} style={{ marginTop: navHeight }}>
                    <CustomLevel overviewList={overviewList} />
                    <PerformanceTable kpiList={kpiList} onClick={handlePerformanceMore} />
                    <SectionHeader
                        btnText='更多记录 &gt;'
                        onClick={handleCustomerFollowMore}
                        title='客户跟进'
                    />
                    <CustomerFollowTable
                        followList={followList}
                        visitCoverageRateDesc={visitCoverageRateDesc}
                    />
                    <div className={styles['my-work-track']} onClick={handlekWorkTrack}>
                        查看工作轨迹 &gt;
                    </div>
                </div>
            </div>
        </>
    );
});

export default connect(({ myWork, loading }) => ({ ...myWork, loading }))(myWorkPage);
