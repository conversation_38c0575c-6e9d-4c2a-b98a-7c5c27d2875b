/**
 * 我的客户--跟进记录详情
 */
import * as api from './api.js';

/**
 * 默认参数
 * @returns {object}   默认参数
 */
const initState = () => ({
    kpiList: [],
    overviewList: [],
    followList: [],
    visitCoverageRateDesc: '',
    audioDomain: '',
    videoDomain: '',
    isOpenUpload: false
});

export default {
    namespace: 'myWork',

    state: initState(),

    reducers: {
        /**
         * 重置状态
         * @returns {object}                     更新过的state
         */
        resetState() {
            return initState();
        },

        /**
         * 更新状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateState(state, action) {
            return {
                ...state,
                ...action.payload
            };
        }
    },

    effects: {
        /**
         * 获取KPI列表
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              更新过的state
         */
        *getKPIList({ payload }, { call, put }) {
            const { result } = yield call(api.getKPIList, payload);
            const { kpiList = [] } = result || {};
            yield put({
                type: 'updateState',
                payload: {
                    kpiList
                }
            });
        },

        /**
         * 获取客户分层数据
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              更新过的state
         */
        *getCustomerOverview({ payload }, { call, put }) {
            const { result } = yield call(api.getCustomerOverview, payload);
            const overviewList = Array.isArray(result) ? result : [];
            yield put({
                type: 'updateState',
                payload: {
                    overviewList
                }
            });
        },

        /**
         * 获取客户跟进记录(少数几条)
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              更新过的state
         */
        *getCustomerFollow({ payload }, { call, put }) {
            const { result } = yield call(api.getCustomerFollow, payload);
            const { visitCoverageRateDesc = '', kpiList = [] } = result || {};
            yield put({
                type: 'updateState',
                payload: {
                    followList: kpiList,
                    visitCoverageRateDesc
                }
            });
        }
    }
};
