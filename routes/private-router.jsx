/**
 * 私有路由组件，用于控制页面权限
 * <AUTHOR>
 * @module   routes/private-router
 */

/* JS */
import React, { memo, useState } from 'react';
import { connect } from 'dva';
import { Redirect } from 'dva/router';
import { useRequest, useDebounceEffect } from 'ahooks';
import { getUrlQueryObject, isEmpty } from 'ttp-utils';
import { checkUserLogin, exchangeCode, mustWeChat } from 'routes/login/api';
import { PAGE_URL } from './router-config.js';
// 组件
import { Loader } from 'ttp-library';
import SetTitle from 'components/set-global-title';

// 设置企业微信标题
const PrivateRouter = memo((props) => {
    const { dispatch, isLogin, menuList, history, component: Component, isAuth, title, ...rest } = props;
    const [pageLoading, setPageLoading] = useState(isLogin ? false : true);
    const { runAsync: runAsyncCheckLogin } = useRequest(checkUserLogin, { manual: true });
    const { runAsync: runAsyncMustWeChat } = useRequest(mustWeChat, { manual: true });
    const { runAsync: runAsyncExchangeCode } = useRequest(exchangeCode, {
        manual: true
    });

    /**
     * 根据企业微信ID自动登录
     * 1、判断是否强制企业微信内访问
     * 2、根据企业微信ID自动登录
     * @returns {Promise}            Promise
     */
    const autoLogin = async () => {
        const { code } = getUrlQueryObject();
        try {
            // 是否必须企微登录
            const { must, authUrl } = await runAsyncMustWeChat();
            if (isEmpty(must) || must == false) {
                return;
            }
            if (isEmpty(code)) {
                // 必须企微登录, 但是没有code
                window.location.href = authUrl;
                return;
            }
            // TODO:增加callback地址,后端拼到queryString上,方便重定向
            const { result } = await runAsyncExchangeCode({ code });
            //  调用登录成功回调
            return dispatch({ type: 'app/loginSuccess', payload: { isLogin: true, ...result } });
        } catch (error) {
            console.error('private-router->autoLogin->error', error);
            throw error;
        }
    };

    // 页面初始化，监听监听登录状态是否需要自动登录
    useDebounceEffect(
        () => {
            if (isLogin == false) {
                runAsyncCheckLogin()
                    .then((checkData) => {
                        if (checkData === false) {
                            return autoLogin();
                        }
                        // 写入登录状态
                        return dispatch({
                            type: 'app/updateLoginState',
                            payload: { isLogin: true, ...checkData }
                        });
                    })
                    .finally(() => {
                        // 结束载入状态
                        setPageLoading(false);
                    });
            }
        },
        [isLogin],
        { wait: 250 }
    );

    // 如果页面还初始化中的时候，展示加载动画
    if (pageLoading && !isLogin) {
        return <Loader maskOpacity={0.6} open={true} type='page' />;
    }

    // 1、需要登录，未登录
    if (isAuth && !isLogin) {
        const config = {
            pathname: PAGE_URL.login,
            state: { from: rest.path, search: location.search }
        };
        return <Redirect to={config} />;
    }

    // 2、不需要登录
    // 3、需要登录，已登录，并且有权限
    if (!isAuth || (isAuth && isLogin && menuList.includes(rest.path))) {
        return (
            <SetTitle title={title}>
                <Component {...rest} history={history} />
            </SetTitle>
        );
    }
    // 4、需要登录，已登录，并且无权限
    return <Redirect to='/pages/403' />;
});

export default connect((state) => state.app)(PrivateRouter);
