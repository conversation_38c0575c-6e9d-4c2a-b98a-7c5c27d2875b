/**
 * @file 车源智推-历史推荐
 */

/* requires JS */
import { postRequest } from 'common/js/fetch.js';
import { PORT_URL } from 'common/js/constant.js';
import { formatPostData } from 'common/js/utils.js';
import { isEmpty, formatTime } from 'ttp-utils';

/**
 * 历史推荐列表-经销商维度
 * <AUTHOR>
 * @param    {object}                 data          请求参数对象
 * @returns  {Promise}                              返回一个Feach的Promise对象
 */
export function historyDealerList(data) {
    const queryDate = isEmpty(data.queryDate) ? '' : formatTime(data.queryDate, 'yyyy-mm-dd');
    let opts = {
        url: PORT_URL.historyDealerList,
        data: formatPostData({
            ...data,
            queryDate
        })
    };
    return postRequest(opts);
}

/**
 * 历史推荐列表-经销商名下车源维度
 * <AUTHOR>
 * @param    {object}                 data          请求参数对象
 * @returns  {Promise}                              返回一个Feach的Promise对象
 */
export function historyAuctionList(data) {
    const queryDate = isEmpty(data.queryDate) ? '' : formatTime(data.queryDate, 'yyyy-mm-dd');
    let opts = {
        url: PORT_URL.historyAuctionList,
        data: formatPostData({
            ...data,
            queryDate
        })
    };
    return postRequest(opts);
}
