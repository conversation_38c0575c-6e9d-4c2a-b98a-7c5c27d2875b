// JS
import React, { memo, useEffect, useState, useMemo } from 'react';
import { connect } from 'dva';
import { isEmpty, executeCB } from 'ttp-utils';
import { useSize, useSetState } from 'ahooks';
// 组件
import { Drawer, Empty, BetterPullBox, Loader } from 'ttp-library';
import Panel from 'components/panel/index.js';
import CarItem from 'components/car-item/index.js';
import HistoryList from '../history-list/index.js';
// LESS
import styles from './index.less';

/**
 * 车商数据
 * @returns {object} 默认数据
 */
const defaultDealerData = () => ({
    city: '',
    dealerId: '',
    dealerName: ''
});

/**
 * 车商统计数据
 * @returns {object} 默认数据
 */
const defaultSummaryData = () => ({
    dealerBidCount: 0,
    dealerValidBidCount: 0,
    recommendAuctionCount: 0,
    recommendCount: 0,
    recommendDealCount: 0
});

/**
 * 历史推荐，经销商个推荐车源列表--弹窗
 */
const AuctionDrawer = memo((props) => {
    // props
    const { dispatch, loading, auctionFormData, open, onClose, onClick } = props;
    const listLoading = loading.effects['recommendHistory/getHistoryAuctionList'];
    // state
    const [pullboxRefreshKey, setPullboxRefreshKey] = useState(Date.now());
    const [dealerData, setDealerData] = useSetState(defaultDealerData());
    const [summary, setSummary] = useSetState(defaultSummaryData());
    const [bodyStyle, setBodyStyle] = useSetState({ height: '90vh' });
    // state
    const size = useSize(document.querySelector('body'));
    // 计算属性
    // 获取视口的宽度
    const remRate = useMemo(() => Math.min(size.width, 750) / 375, [size]);
    /**
     * 关闭Drawer
     */
    const handleClose = () => {
        executeCB(onClose);
    };

    /**
     * 获取头部高度
     * @param   {number}               number 顶部高度
     * @returns {number}                      顶部高度
     */
    const getHeaderHeight = (number) => {
        console.log('getHeaderHeight', number);
        // 窗口高度 - drawer的title高度- panel的title高度-  panel的margin和padding高度 - panel的底部统计高度
        setBodyStyle({ height: window.innerHeight - number - Math.floor((43 + 40 + 34) * remRate) });
    };

    /**
     * 车看经销商详情
     */
    const handleDealerClick = () => {
        executeCB(onClick, 'dealer', { dealerId: dealerData.dealerId, from: 'drawer' });
    };

    /**
     * 点击呼叫
     */
    const handleCallClick = () => {
        executeCB(onClick, 'call', { dealerId: dealerData.dealerId });
    };

    /**
     * 选择车源，展示车源详情
     * @param   {object}               auctionData   车源信息
     */
    const handleCarClick = (auctionData) => {
        const { checkReportUrl, auctionId } = auctionData;
        executeCB(onClick, 'report', { checkReportUrl, auctionId, from: 'drawer' });
    };

    /**
     * 更多车源推荐记录
     * @param   {Array}                recordList  车源推荐历史记录
     */
    const handleHistoryMoreClick = (recordList) => {
        executeCB(onClick, 'historyMore', { recordList, from: 'drawer' });
    };

    /**
     * 加载更多
     * @param   {object}            param         入参
     * @param   {boolean}           param.isReset 是否下拉刷新，true  则重置数据
     * @returns {Promise}           Promise
     */
    const onLoadMore = ({ isReset = false }) => {
        const current = isReset ? 1 : auctionFormData.currentPage + 1;
        return dispatch({
            type: 'recommendHistory/getHistoryAuctionList',
            payload: { ...auctionFormData, currentPage: current }
        }).then((result) => {
            const { summary, dealerId, dealerName, city } = result;
            if (!isEmpty(dealerId) && !isEmpty(dealerName) && !isEmpty(city)) {
                setDealerData({ dealerId, dealerName, city });
            }
            if (!isEmpty(summary)) {
                setSummary(summary);
            }
            return result.auctionList;
        });
    };

    /**
     * 使用虚拟列表的情况
     *
     * @param   {object}               item 列表每一项的数据
     * @returns {React.ReactElement}        JSX
     */
    const rowRender = (item) => {
        if (isEmpty(item)) {
            return <Empty />;
        }

        return (
            <div className={styles['auction-item']}>
                <CarItem itemData={item.auctionDetail} onClick={handleCarClick} />
                <HistoryList listData={item.recordList} onClick={handleHistoryMoreClick} />
            </div>
        );
    };

    // 经销商名称+ID
    const title = useMemo(() => {
        const { city, dealerId, dealerName } = dealerData;
        return (
            <div className={styles['left']} id='AuctionDrawerTitle' onClick={handleDealerClick}>
                <strong>{city}</strong>
                <strong>{dealerName}</strong>
                <span className={styles['text-gray']}>ID:{dealerId}</span>
                <i className='icon-arrow-next' />
            </div>
        );
    }, [dealerData]);
    // 拨打经销商电话
    const subtitle = useMemo(() => {
        return (
            <div className={styles['operation']}>
                <i className='icon-tel-sm' onClick={handleCallClick} />
            </div>
        );
    }, [handleCallClick]);

    // useEffect
    useEffect(() => {
        if (open) {
            setPullboxRefreshKey(Date.now());
        }
    }, [open]);

    return (
        <Drawer
            className={styles['auction-drawer']}
            destroy={false}
            full={true}
            getHeaderHeight={getHeaderHeight}
            hash='auction-drawer'
            maskOpacity={0.5}
            mode='right'
            onClose={handleClose}
            open={open}
            title='更多历史推荐车源'>
            <Loader maskOpacity={0.6} open={listLoading} type='page' />
            <Panel
                button={false}
                classname={styles['auction-drawer-body']}
                open={true}
                subtitle={subtitle}
                title={title}>
                <div className={styles['auction-drawer-list']} style={bodyStyle}>
                    <BetterPullBox
                        key={pullboxRefreshKey}
                        requestData={onLoadMore}
                        rowRender={rowRender}
                        usePullDown
                        usePullUp
                        useVirtual={false}
                    />
                </div>
                {/* 统计信息 */}
                <div className={styles['auction-drawer-footer']}>
                    已推{summary.recommendCount}次，共{summary.recommendAuctionCount}台车，共出价
                    {summary.dealerBidCount}次
                    {/* ，有效出价{summary.dealerValidBidCount}次，推荐成交{summary.recommendDealCount}次 */}
                </div>
            </Panel>
        </Drawer>
    );
});

export default connect(({ recommendHistory, loading }) => ({ ...recommendHistory, loading }))(AuctionDrawer);
