@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';

.auction-drawer {
    :global {
        .Saitama-drawer-box {
            background-color: @color-gray-drop;
        }

        .Saitama-list-item {
            padding: 0;
            border-radius: 8 / @rem;
        }

        .Saitama-list-item-content {
            margin-top: 8 / @rem;
        }
    }

    .drawer-close {
        font-size: 14 / @rem !important;
    }

    &-body {
        margin: (10 / @rem) auto;
        padding: (10 / @rem) (8 / @rem);
        position: relative;
    }

    &-list {
        .auction-item {
            margin-top: 20 / @rem;

            &:first-child {
                margin-top: 0;
            }
        }
    }

    .left {
        display: flex;
        align-items: center;

        strong {
            font-size: 16 / @rem;
            font-weight: 600;
            padding-right: 5 / @rem;
            display: block;
            max-width: 120 / @rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .text-gray {
            font-weight: 400;
            font-size: 14 / @rem;
            color: @color-gray-6;
        }
    }

    .operation {
        :global {
            .icon-tel {
                width: 24 / @rem;
                height: 24 / @rem;
            }
        }
    }

    &-btn {
        width: 156 / @rem;
        height: 44 / @rem;
    }

    &-footer {
        height: 34 / @rem;
        line-height: 34 / @rem;
        color: @color-gray-9;
        font-size: 11 / @rem;
        position: relative;

        &:after {
            .setTopLine(@color-border);
        }
    }
}
