// JS
import React, { memo, useMemo } from 'react';
import { executeCB } from 'ttp-utils';
// 组件
import { Drawer, Icon } from 'ttp-library';
import HistoryList from '../history-list/index.js';
// LESS
import styles from './index.less';

/**
 * 车源推荐记录弹窗
 */
const HistoryDrawer = memo((props) => {
    // props
    const { from, listData = [], open, onClose } = props;

    /**
     * 关闭Drawer
     */
    const handleClose = () => {
        executeCB(onClose);
    };

    // JSX DOME
    const rightNode = useMemo(
        () => <Icon className={styles['drawer-close']} onClick={handleClose} type='iconfont-close' />,
        [handleClose]
    );

    return (
        <Drawer
            className={styles['history-drawer']}
            destroy={false}
            full={false}
            hash={`history-drawer-${from}`}
            left={false}
            maskOpacity={0.5}
            mode='bottom'
            onClose={handleClose}
            open={open}
            right={rightNode}
            title='更多推荐记录'>
            <div className={styles['history-drawer-body']}>
                <HistoryList header={false} listData={listData} max={9999} />
            </div>
        </Drawer>
    );
});

export default HistoryDrawer;
