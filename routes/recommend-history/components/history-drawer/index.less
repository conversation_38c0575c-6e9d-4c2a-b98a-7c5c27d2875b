@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';

.history-drawer {
    :global {
        .Saitama-drawer-box {
            border-radius: (8 / @rem) (8 / @rem) 0 0;
        }
    }

    .drawer-close {
        font-size: 14 / @rem !important;
    }

    &-body {
        box-sizing: border-box;
        padding: 0 (8 / @rem) (10 / @rem);
        height: 70vh;
        position: relative;
        background-color: @color-white;

        &::after {
            .setTopLine(@color-border);
        }
    }
}
