// JS
import React, { memo } from 'react';
import cls from 'classnames';
import { executeCB, isEmpty } from 'ttp-utils';
// 组件
import Tag from 'components/tag/index.js';
// LESS
import styles from './index.less';

/**
 * 跟进状态_key
 * @type {object}
 */
const FOLLOW_STATUS_KEY = {
    view: 1,
    notView: 2,
    bid: 3
};

/**
 * 跟进状态_MAP
 * @type {object}
 */
const FOLLOW_STATUS_MAP = {
    [FOLLOW_STATUS_KEY.view]: '已查看',
    [FOLLOW_STATUS_KEY.notView]: '未查看',
    [FOLLOW_STATUS_KEY.bid]: '已出价'
};

/**
 * 跟进状态_MAP
 * @type {object}
 */
const FOLLOW_STATUS_COLOR = {
    [FOLLOW_STATUS_KEY.view]: 'info',
    [FOLLOW_STATUS_KEY.notView]: 'danger',
    [FOLLOW_STATUS_KEY.bid]: 'info'
};

/**
 * 车源历史推荐记录
 * @param   {object}                     props props
 * @returns {React.ReactElement}               jsx
 */
const HistoryList = memo((props) => {
    // props
    const { listData = [], header = true, onClick, max = 2 } = props;
    const historyClass = cls(styles['history-list'], { [styles['history-list-bg']]: header });

    // 函数
    /**
     * 更多记录
     */
    const handleMore = () => {
        executeCB(onClick, listData);
    };

    // 组件
    return (
        <div className={historyClass}>
            {header && (
                <h2 className={styles['title']}>
                    <span>推荐记录</span>
                    {Array.isArray(listData) && listData.length > max && (
                        <div className={styles['title-btn']} onClick={handleMore}>
                            <span>更多记录</span>
                            <i className='icon-arrow-next' />
                        </div>
                    )}
                </h2>
            )}
            {Array.isArray(listData) &&
                listData.map((item, index) => {
                    if (index >= max) {
                        return null;
                    }
                    return (
                        <div className={styles['list']} key={item.recommendTime}>
                            <dl className={styles['list-item']}>
                                <dt className={styles['list-item-left']}>
                                    <strong className='f-mr10'>{item.platformName}</strong>
                                    <Tag themeColor={FOLLOW_STATUS_COLOR[item.followStatus]}>
                                        {FOLLOW_STATUS_MAP[item.followStatus]}
                                    </Tag>
                                </dt>
                                <dd className={styles['list-item-time']}>{item.recommendTime}</dd>
                            </dl>
                            <dl className={styles['list-item']}>
                                <dt className={styles['list-item-label']}>出价信息：</dt>
                                <dd className={styles['list-item-price']}>
                                    {(!isEmpty(item.thisBidPrefix) || !isEmpty(item.thisBidPrice)) && (
                                        <span>
                                            {isEmpty(item.thisBidPrefix) ? '' : item.thisBidPrefix}
                                            {isEmpty(item.thisBidPrice) ? '' : `¥${item.thisBidPrice}`}
                                        </span>
                                    )}
                                    {(!isEmpty(item.allBidPrefix) || !isEmpty(item.allBidPrice)) && (
                                        <span className='f-ml10'>
                                            {isEmpty(item.allBidPrefix) ? '' : item.allBidPrefix}
                                            {isEmpty(item.allBidPrice) ? '' : `¥${item.allBidPrice}`}
                                        </span>
                                    )}
                                </dd>
                            </dl>
                        </div>
                    );
                })}
        </div>
    );
});

export default HistoryList;
