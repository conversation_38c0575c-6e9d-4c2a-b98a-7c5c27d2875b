@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';

.history-list {
    padding: (5 / @rem) 0;

    &-bg {
        margin-top: 10 / @rem;
        background: #f4f9fb;
        border-radius: 6 / @rem;
    }

    .title {
        padding: 0 (10 / @rem);
        height: 30 / @rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 400;
        color: @color-gray-3;
        font-size: 14 / @rem;
        line-height: 1.5;

        &-btn {
            font-size: 14 / @rem;
            color: @color-gray-3;
            display: flex;
            align-items: center;
        }
    }

    .list {
        padding: (5 / @rem) (10 / @rem);
        position: relative;

        &:after {
            .setBottomLine(@color-border);
        }

        &:last-child:after {
            border-bottom-width: 0px;
        }

        &-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 30 / @rem;
            font-size: 14 / @rem;

            &-left {
                font-weight: 600;
                color: @color-gray-3;
            }

            &-time,
            &-label {
                color: @color-gray-6;
                line-height: 1.5;
            }

            &-price {
                font-weight: 600;
                color: @color-orange;
                line-height: 1.5;
            }
        }
    }
}
