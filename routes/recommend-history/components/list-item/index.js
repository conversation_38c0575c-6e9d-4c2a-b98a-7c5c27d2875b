// JS
import React, { memo, useMemo } from 'react';
import cls from 'classnames';
import { executeCB } from 'ttp-utils';
import { useToggle } from 'ahooks';
// 组件
import Panel from 'components/panel/index.js';
import CarItem from 'components/car-item/index.js';
import HistoryList from '../history-list/index.js';
// LESS
import styles from './index.less';

/**
 * 我的客户数据ITEM
 * @param   {object}                     props props
 * @returns {React.ReactElement}               jsx
 */
const ListItem = memo((props) => {
    // props
    const { itemData, onClick } = props;
    const { summary = {}, auctionList = [], city, dealerId, dealerName } = itemData;
    // state
    const [open, { toggle }] = useToggle(true);

    // 函数
    /**
     * 车看经销商详情
     */
    const handleDealerClick = () => {
        executeCB(onClick, 'dealer', { dealerId: dealerId, from: 'page' }, itemData);
    };
    /**
     * 点击呼叫
     */
    const handleCallClick = () => {
        executeCB(onClick, 'call', { dealerId: dealerId }, itemData);
    };
    /**
     * 选择车源，展示车源详情
     * @param   {object}               auctionData   车源信息
     */
    const handleCarClick = (auctionData) => {
        const { checkReportUrl, auctionId } = auctionData;
        executeCB(onClick, 'report', { checkReportUrl, auctionId, from: 'page' }, itemData);
    };

    /**
     * 更多车源记录
     */
    const handleAuctionMoreClick = () => {
        executeCB(onClick, 'auctionMore', { dealerId: dealerId }, itemData);
    };

    /**
     * 更多车源推荐记录
     * @param   {Array}                recordList  车源推荐历史记录
     */
    const handleHistoryMoreClick = (recordList) => {
        executeCB(onClick, 'historyMore', { recordList, from: 'page' }, itemData);
    };

    // 组件
    // 经销商名称+ID
    const title = useMemo(() => {
        return (
            <div className={styles['rec-history-panel-title']} onClick={handleDealerClick}>
                <strong>{city}</strong>
                <strong>{dealerName}</strong>
                <span className={styles['text-gray']}>ID:{dealerId}</span>
                <i className='icon-arrow-next' />
            </div>
        );
    }, [city, dealerId, dealerName]);
    // 拨打经销商电话
    const subtitle = useMemo(() => {
        const arrowClass = cls(
            'white-box-arrow',
            { ['white-box-arrow-up']: open },
            { ['white-box-arrow-down']: !open }
        );
        return (
            <div className={styles['rec-history-panel-operation']}>
                <i className='icon-tel-sm' onClick={handleCallClick} />
                <div className={styles['switch']} onClick={toggle}>
                    <i className={arrowClass} />
                </div>
            </div>
        );
    }, [open, toggle, handleCallClick]);

    return (
        <Panel
            button={false}
            classname={styles['rec-history-panel']}
            open={open}
            subtitle={subtitle}
            title={title}>
            {/* 车源列表 */}
            {Array.isArray(auctionList) &&
                auctionList.map((item) => {
                    const keyStr = `${itemData.dealerId}-${item.auctionDetail.auctionId}`;
                    return (
                        <div className={styles['auction-item']} key={keyStr}>
                            <CarItem itemData={item.auctionDetail} onClick={handleCarClick} />
                            <HistoryList listData={item.recordList} onClick={handleHistoryMoreClick} />
                        </div>
                    );
                })}
            {/* 更多车源按钮 */}
            {itemData.moreRecord && (
                <div className={styles['auction-item-more']} onClick={handleAuctionMoreClick}>
                    <span>更多车源</span>
                    <i className='icon-arrow-next' />
                </div>
            )}
            {/* 统计信息 */}
            <div className={styles['rec-history-panel-footer']}>
                已推{summary.recommendCount}次，共{summary.recommendAuctionCount}台车，共出价
                {summary.dealerBidCount}次
                {/* ，有效出价{summary.dealerValidBidCount}次，推荐成交{summary.recommendDealCount}次 */}
            </div>
        </Panel>
    );
});

export default ListItem;
