@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';

.rec-history-panel {
    margin-bottom: 10 / @rem;
    position: relative;

    &-title {
        display: flex;
        align-items: center;

        strong {
            font-size: 16 / @rem;
            font-weight: 600;
            padding-right: 5 / @rem;
            display: block;
            max-width: 120 / @rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .text-gray {
            font-weight: 400;
            font-size: 14 / @rem;
            color: @color-gray-6;
        }
    }

    &-operation {
        display: flex;
        align-items: center;

        :global {
            .icon-tel {
                width: 24 / @rem;
                height: 24 / @rem;
            }
        }

        .switch {
            width: 24 / @rem;
            height: 24 / @rem;
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }
    }

    .auction-item {
        margin-top: 20 / @rem;

        &:first-child {
            margin-top: 0;
        }

        &-more {
            height: 40 / @rem;
            font-size: 14 / @rem;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    &-footer {
        height: 34 / @rem;
        line-height: 34 / @rem;
        color: @color-gray-9;
        font-size: 11 / @rem;
        position: relative;

        &:after {
            .setTopLine(@color-border);
        }
    }
}
