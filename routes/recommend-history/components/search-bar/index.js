// JS
import React, { memo, useState, useRef, useMemo } from 'react';
import { connect } from 'dva';
import { useSetState, useToggle, useUpdateEffect } from 'ahooks';
import { executeCB, isEmpty, formatTime, getUrlQueryStringify, delay } from 'ttp-utils';
import { uploadReport } from 'common/js/utils.js';
import { AUCTION_RESULT_OPTION, AUCTION_RESULT_MAP } from 'common/js/constant.js';
import { PAGE_URL } from 'routes/router-config.js';
import cls from 'classnames';
// 组件
import { Button, DropdownMenu, Calendar, Mmodel } from 'ttp-library';
import SearchBox from 'components/search-box/index.js';
import SearchTagBox from 'components/search-tag-bar/index.js';
import SilderBar from 'components/slider-bar/index.js';
import RadioTags from 'components/radio-tags/index.js';

const { DropdownItem } = DropdownMenu;
// LESS
import styles from './index.less';

/**
 * 下拉筛选标签默认值
 * @type {Array}
 */
const FILTER_LABEL_LIST = [
    {
        itemKey: 'queryDate',
        defaultLabel: '日期'
    },
    {
        itemKey: 'auctionResult',
        defaultLabel: '竞拍结果'
    },
    {
        itemKey: 'brandCode',
        defaultLabel: '品牌车系'
    },
    {
        itemKey: 'ageCode',
        defaultLabel: '车龄'
    }
];

/**
 * 显示全部
 * @type {Array}
 */
const SHOW_ALL = [true, true, true];

/**
 * 车龄(最小值/最大值)
 * @type {Array}
 */
const defaultAutoAge = [0, 10];

/**
 * 今日的时间
 */
const today = formatTime(new Date(), 'yyyy-mm-dd');

/**
 * 下拉筛选底部按钮
 * @param   {object}         props props
 * @returns {object}               jsx
 */
const DropdownFooter = memo(({ classname = styles['dropdown-footer'], onClose, onSubmit }) => (
    <div className={classname}>
        <Button className={styles['footer-button']} onClick={onClose} skin='white'>
            取消
        </Button>
        <Button className={styles['footer-button']} onClick={onSubmit}>
            确认
        </Button>
    </div>
));

/**
 * 我的客户-搜索条件
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const SearchBar = memo((props) => {
    // props
    const { history, dispatch, classname, formData, getHeight, barStyle } = props;
    // state
    const [open, { setLeft: setCloseBrand, setRight: setOpenBrand }] = useToggle(false);
    const [brandKey, setBrandKey] = useState(new Date());
    const [searchData, setSearchData] = useSetState({ ...formData });
    const [autoAge, setAutoAge] = useState([...defaultAutoAge]);

    // 计算属性
    // 车龄选项  - 标题
    const autoAgeTitle = useMemo(() => {
        const [min, max] = autoAge;
        return `车龄：${min}至${max > defaultAutoAge[1] ? `${defaultAutoAge[1]}年以上` : max + '年'}`;
    }, [autoAge]);
    // 选中的tag数据
    const searchTagsList = useMemo(() => {
        const {
            searchValue,
            queryDate,
            auctionResult,
            minAge,
            maxAge,
            brandId,
            brandName = '',
            familyName = '',
            modelName = ''
        } = formData;
        const tags = [];
        // 查询日期, 如: 2025-04-03
        if (!isEmpty(queryDate)) {
            const queryDateStr = formatTime(queryDate, 'yyyy-mm-dd');
            tags.push({
                key: 'queryDate',
                label: today == queryDateStr ? '今日' : queryDateStr,
                clearKey: ['queryDate']
            });
        }
        // 关键字搜索
        if (!isEmpty(searchValue)) {
            tags.push({
                key: 'searchValue',
                label: searchValue,
                clearKey: ['searchValue']
            });
        }
        // 品牌车系车型
        if (!isEmpty(brandId)) {
            tags.push({
                key: 'brandId',
                label: `${brandName}${familyName}${modelName}`,
                clearKey: ['brandId', 'familyId', 'modelId']
            });
        }
        // 竞拍结果
        if (!isEmpty(auctionResult)) {
            tags.push({
                key: 'auctionResult',
                label: AUCTION_RESULT_MAP[auctionResult],
                clearKey: ['auctionResult']
            });
        }
        // 最大车龄
        if (!isEmpty(minAge) || !isEmpty(maxAge)) {
            tags.push({
                key: 'autoAgeRange',
                label: autoAgeTitle,
                clearKey: ['minAge', 'maxAge']
            });
        }

        return tags;
    }, [formData, autoAgeTitle]);

    // 常量
    const searchRef = useRef(null);
    const dropRef = useRef(null);
    const searchClasss = cls(styles['rec-history-search-bar'], classname);

    // 函数请求
    /**
     * 获取搜索框高度
     */
    const getSearchHight = () => {
        const { height } = searchRef.current.getBoundingClientRect();
        executeCB(getHeight, height);
    };

    /**
     * 还原state状态
     * @param   {object}               formData  原始筛选条件
     */
    const resetState = (formData) => {
        setSearchData(formData);
        // 车龄
        setAutoAge([
            isEmpty(formData.minAge) ? defaultAutoAge[0] : Number(formData.minAge),
            isEmpty(formData.maxAge) ? defaultAutoAge[1] + 1 : Number(formData.maxAge)
        ]);
    };

    /**
     * 获取推荐车源信息
     * @param   {object}               data  请求参数
     */
    const getRecommendList = (data = {}) => {
        const searchParamsData = { ...formData, ...data, currentPage: 1 };
        dispatch({
            type: 'recommendHistory/updateFormDataState',
            payload: data
        });
        dispatch({
            type: 'recommendHistory/getHistoryDealerList',
            payload: searchParamsData
        });
        // 修改URL的参数，方便回退查看数据
        const { currentPage, pageSize, queryDate, ...searchParams } = searchParamsData;
        history.replace({
            pathname: PAGE_URL.recommendHistory,
            search: getUrlQueryStringify(
                { queryDate: formatTime(queryDate, 'yyyy-mm-dd'), ...searchParams },
                '?'
            )
        });
    };
    /**
     * 搜索框点击搜索
     * @param {string}         searchValue 当前选中数据
     */
    const handleSearchChange = (searchValue) => {
        getRecommendList({ searchValue });
        // 上报埋点
        uploadReport('recHistorySearch', '历史推荐搜索点击', { searchValue });
    };

    /**
     * 处理关闭事件
     */
    const handleClose = () => {
        // 关闭下拉状态
        dropRef.current?.closeAllDrawer();
        resetState(formData);
    };

    /**
     * 处理搜索提交事件
     */
    const handleSumbit = () => {
        // 关闭下拉状态
        dropRef.current?.closeAllDrawer();
        getRecommendList(searchData);
        // 上报埋点
        uploadReport('recHistoryFilter', '历史推荐筛选点击', { ...searchData });
    };

    /**
     * 日历change
     * @param {string} value   选中的值
     */
    const handleCalendarChange = (value) => {
        console.log('date:', formatTime(value));
        setSearchData({ queryDate: value });
    };

    /**
     * 车龄区间-滑块拖动事件
     * @param {Array<number>}         value 当前选中数据
     */
    const handleauctionResultChange = (value) => {
        console.log('handleauctionResultChange--value', value);
        setSearchData({ auctionResult: value });
    };

    /**
     * 处理品牌车系选择事件
     * @param {object}         data 当前选中数据
     */
    const handleBrandChange = (data) => {
        console.log('handleBrandChange--data', data);
        const stateData = {
            brandId: data.brandId,
            brandName: data.brandName,
            familyId: data.familyId,
            familyName: data.familyName,
            modelId: data.modelId,
            modelName: data.modelName
        };
        setSearchData(stateData);
        uploadReport('recHistoryFilter', '历史推荐筛选点击', { ...stateData });
        delay(1000).then(() => getRecommendList(stateData));
    };

    /**
     * 处理关闭品牌模型的函数
     */
    const handleBrandClose = () => {
        setCloseBrand();
        handleClose();
    };

    /**
     * 车龄区间-滑块拖动事件
     * @param {Array<number>}         value 当前选中数据
     */
    const handleAutoAgeChange = (value) => {
        console.log('handleAutoAgeChange--value', value);
        setAutoAge(value);
        setSearchData({ minAge: value[0], maxAge: value[1] > defaultAutoAge[1] ? '' : value[1] });
    };

    /**
     * 返回当前打开的是哪一项
     * @param {string}         itemKey 当前选中筛选项
     */
    const handleSearchOpenItem = (itemKey) => {
        // 品牌车系
        if (itemKey === 'brandCode') {
            setOpenBrand();
        }
    };

    /**
     * 删除筛选条件
     * @param {Array<string>}         fromDataKeys 筛选条件KEY
     */
    const handleTagBoxClose = (fromDataKeys) => {
        console.log('handleTagBoxClose:', fromDataKeys);
        const resetData = fromDataKeys.reduce((data, cur) => {
            data[cur] = '';
            if (cur == 'queryDate') {
                data.queryDate = new Date();
            }
            if (cur == 'brandId') {
                data.brandName = '';
                data.familyId = '';
                data.familyName = '';
                data.modelId = '';
                data.modelName = '';
                setBrandKey(new Date());
            }
            return data;
        }, {});
        setSearchData(resetData);
        getRecommendList(resetData);
    };

    // useEffect
    useUpdateEffect(() => {
        // 重置状态
        resetState(formData);
        // 获取标签栏高度
        if (searchRef) {
            getSearchHight();
        }
    }, [formData]);

    return (
        <div className={searchClasss} style={barStyle}>
            <div ref={searchRef}>
                <SearchBox
                    onSearch={handleSearchChange}
                    placeholder='请输入车商姓名/ID/手机号精准搜索'
                    search={searchData.searchValue}
                />
                <DropdownMenu
                    className={styles['dropdown']}
                    labelList={FILTER_LABEL_LIST}
                    labelType='light'
                    menuType='tag'
                    onOpenItem={handleSearchOpenItem}
                    ref={dropRef}
                    viewType='tag'>
                    {/* 日期选择 */}
                    <DropdownItem itemKey='queryDate'>
                        <div className={styles['dropdown-content']}>
                            <Calendar
                                defaultView='month'
                                minDetail='month'
                                onChange={handleCalendarChange}
                                value={searchData.queryDate}
                            />
                        </div>
                        <DropdownFooter onClose={handleClose} onSubmit={handleSumbit} />
                    </DropdownItem>
                    {/* 竞拍结果 */}
                    <DropdownItem itemKey='auctionResult'>
                        <div className={styles['dropdown-content']}>
                            <RadioTags
                                list={AUCTION_RESULT_OPTION}
                                onChange={handleauctionResultChange}
                                value={searchData.auctionResult}
                            />
                        </div>
                        <DropdownFooter onClose={handleClose} onSubmit={handleSumbit} />
                    </DropdownItem>
                    {/* 品牌车系 */}
                    <DropdownItem itemKey='brandCode' />
                    {/* 车龄 */}
                    <DropdownItem itemKey='ageCode'>
                        <div className={styles['dropdown-content']}>
                            <SilderBar
                                defaultValue={autoAge}
                                key='autoAge'
                                minAndMax={defaultAutoAge}
                                onChange={handleAutoAgeChange}
                                preTitle={autoAgeTitle}
                                sufTitle='单位：年'
                                value={autoAge}
                            />
                        </div>
                        <DropdownFooter onClose={handleClose} onSubmit={handleSumbit} />
                    </DropdownItem>
                </DropdownMenu>
                {/* 筛选选中项 */}
                <SearchTagBox onClose={handleTagBoxClose} tagList={searchTagsList} />
            </div>
            {/* 品牌车系 */}
            <Mmodel
                brandId={searchData.brandId}
                brandName={searchData.brandName}
                familyId={searchData.familyId}
                familyName={searchData.familyName}
                hasBackBtn
                hasHeader
                isShowAll={SHOW_ALL}
                key={brandKey}
                modelId={searchData.modelId}
                modelName={searchData.modelName}
                onClose={handleBrandClose}
                onSelect={handleBrandChange}
                open={open}
                showType={3}
            />
        </div>
    );
});

export default connect(({ recommendHistory, loading }) => ({ ...recommendHistory, loading }))(SearchBar);
