@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';

.rec-history-search-bar {
    width: 375 / @rem;
    position: fixed;
    z-index: 20;
    left: 50%;
    margin-left: -187 / @rem;
    background-color: @color-white;

    .search-result-info {
        background-color: #f6f6f6;
        padding: 10 / @rem 0 0;
    }

    .search-result-total {
        margin: 0 / @rem auto;
        text-align: center;
        padding-right: 10 / @rem;
        background: #e1f4fc;
        border-radius: 4 / @rem;
        color: #00a2e8;
        font-size: 12 / @rem;
        height: 20 / @rem;
        line-height: 20 / @rem;
        width: 360 / @rem;
    }

    .dropdown {
        padding-bottom: 10 / @rem;

        :global {
            .Saitama-dropdown-menu__bar {
                padding: (4 / @rem) (15 / @rem);
            }

            .Saitama-dropdown-menu__item {
                flex: none;
                color: @color-gray-3;
            }

            .Saitama-dropdown-menu__title-arrow {
                border-color: transparent transparent #222 #222;
            }

            .Saitama-dropdown-menu__item-radius {
                border-radius: 14 / @rem;
                background-color: @color-white;
                border: 1px solid #f2f2f2;
                height: 28 / @rem;
                flex-grow: 1;
            }

            .Saitama-dropdown-item .Saitama-list-group-tag .Saitama-list-group-box {
                .Saitama-tag.Saitama-radio {
                    width: 105 / @rem;
                    padding: (6 / @rem) (10 / @rem);
                    margin-bottom: 15 / @rem;
                }
            }
        }

        &-content {
            position: relative;
            min-height: 100 / @rem;
            padding: (10 / @rem) 0;

            &:after {
                .setTopLine(@color-border);
            }
        }

        &-footer {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding: (10 / @rem) (20 / @rem);
            position: relative;

            &:after {
                .setTopLine(@color-border);
            }

            .footer-button {
                width: 156 / @rem;
                height: 44 / @rem;
            }
        }
    }
}

// 更多筛选
.drawer {
    &-content {
        position: relative;
        min-height: 100 / @rem;
        padding: (10 / @rem) 10 / @rem;

        &__flex {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        &:after {
            .setTopLine(@color-border);
        }

        .label-code-title,
        .equity-nember-title {
            font-weight: bold;
            font-size: 14 / @rem;
            line-height: 1.75;
            padding: 0 (10 / @rem);
        }

        .equity-nember-title {
            margin-top: 10 / @rem;
        }

        .radio-tag {
            width: 70 / @rem;
            height: 34 / @rem;
            text-align: center;
            border-color: #f5f8fa;
            background: #f5f8fa;
            margin-right: 10 / @rem;
            display: inline-block;
        }

        :global {
            .Saitama-radio.Saitama-radio--checked.Saitama-tag {
                border-color: @color-blue;
                background: rgba(0, 162, 232, 0.1);
            }

            .Saitama-tag--radius {
                margin-right: 10 / @rem;
            }
        }

        .icon-arrow-right {
            .arr-next(10 /@rem, 1/@rem, #999);
        }

        .city-text {
            margin-right: 5 / @rem;
            vertical-align: middle;
        }

        .select-city {
            padding-right: 10 / @rem;
        }
    }

    &-footer {
        position: absolute;
        background: @color-white;
        width: 375 / @rem;
        z-index: 10;
        bottom: 0;
        left: 50%;
        margin-left: -187 / @rem;
        padding: (10 / @rem) (20 / @rem);
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        &:after {
            .setTopLine(@color-border);
        }

        .footer-button {
            width: 156 / @rem;
            height: 44 / @rem;
        }
    }
}
