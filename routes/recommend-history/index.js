/* eslint-disable no-undef */
// JS
import React, { memo, useEffect, useState, useMemo } from 'react';
import { connect } from 'dva';
import { useToggle, useScroll, useThrottleEffect, useSetState } from 'ahooks';
import { getUrlQueryStringify, getUrlQueryObject, isEmpty } from 'ttp-utils';
import { uploadReport } from 'common/js/utils.js';
import { RECOMMEND_TAB, PORT_URL } from 'common/js/constant.js';
import { PAGE_URL } from 'routes/router-config.js';
// 组件
import { Loader, Empty, OutCall, message } from 'ttp-library';
import WebViewDrawer from 'components/web-view/index.js';
import TabNav from 'components/tab-nav/index.js';
import BottomLine from 'components/bottom-line/index.js';
import SearchBar from './components/search-bar/index.js';
import ListItem from './components/list-item/index.js';
import HistoryDrawer from './components/history-drawer/index.js';
import AuctionDrawer from './components/auction-drawer/index.js';
// LESS
import styles from './index.less';
// 获取视口的高度
const viewportHeight = window.innerHeight;

/**
 * 车源智推-历史推荐
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const RecommendHistory = memo((props) => {
    // props
    const { history, dispatch, loading, dataList, noNextDataList, formData } = props;
    // 筛选条件
    const { currentPage } = formData;
    const listLoading = loading.effects['recommendHistory/getHistoryDealerList'];
    // state
    const [navHeight, setNavHeight] = useState(0);
    const [searchHeight, setSearchHeight] = useState(0);
    // 拨号：弹窗
    const [openCall, { setLeft: setCloseCall, setRight: setOpenCall }] = useToggle(false);
    const [outCallPort, setOutCallPort] = useSetState({ url: PORT_URL.outCall, method: 'POST' });
    // 检测报告/车商详情：弹窗
    const [openPageWebview, { setLeft: setClosePageWebview, setRight: setOpenPageWebview }] =
        useToggle(false);
    const [openDialogWebview, { setLeft: setCloseDialogWebview, setRight: setOpenDialogWebview }] =
        useToggle(false);
    const [webViewUrl, setWebViewUrl] = useState('');
    const [webViewTitle, setWebViewTitle] = useState('检测报告');
    // 车源列表：弹窗
    const [openAuction, { setLeft: setCloseAuction, setRight: setOpenAuction }] = useToggle(false);
    // 车源历史推荐记录：弹窗
    const [openPageHistory, { setLeft: setClosePageHistory, setRight: setOpenPagegHistory }] =
        useToggle(false);
    const [openDialogHistory, { setLeft: setCloseDialogHistory, setRight: setOpenDialogHistory }] =
        useToggle(false);
    const [historyList, setHistoryList] = useState([]);
    // scroll
    const scroll = useScroll(document);

    // 计算属性
    // 搜索栏样式
    const searchBarStyle = useMemo(() => ({ top: navHeight }), [navHeight]);
    const bodyStyle = useMemo(
        () => ({ marginTop: navHeight + searchHeight + 10 }),
        [navHeight, searchHeight]
    );

    /**
     * 获取历史车源数据-经销商维度
     * @param   {object}               data  请求参数
     */
    const getHistoryDealerList = (data) => {
        if (!listLoading) {
            dispatch({
                type: 'recommendHistory/getHistoryDealerList',
                payload: { ...formData, ...data }
            });
        }
    };

    /**
     * 导航tab切换点击
     * @param {object}         item 当前选中数据
     */
    const handleNavChange = (item) => {
        history.push(item.url);
    };

    /**
     * 列表点击事件
     * @param  {string}               action   行为
     * @param  {object}               item     行为数据
     * @param  {object}               rowData  行所有数据
     */
    const handleItemClick = (action, item, rowData) => {
        console.log('handleItemClick', action, item, rowData);
        switch (action) {
            // 查看经销商详情
            case 'dealer':
                uploadReport('recHistoryViewDealer', '历史推荐查看经销商详情', { dealerId: item.dealerId });
                // history.push({
                //     pathname: PAGE_URL.myClientDealer,
                //     search: getUrlQueryStringify({ dealerId: item.dealerId, from: 'recommendHistory' }, '?')
                // });
                setWebViewUrl(
                    getUrlQueryStringify(
                        { dealerId: item.dealerId, from: 'recommendHistory' },
                        `${location.protocol}//${location.host}${PAGE_URL.myClientDealer}?`
                    )
                );
                setWebViewTitle('车商详情');
                if (item.from == 'page') {
                    setOpenPageWebview();
                } else {
                    setOpenDialogWebview();
                }
                break;
            // 拨打车商电话
            case 'call':
                // 拨打电话
                setOutCallPort({ data: { dealerId: item.dealerId } });
                setOpenCall();
                uploadReport('recHistoryCall', '历史推荐拨打车商电话', { dealerId: item.dealerId });
                break;
            // 查看车辆检测报告
            case 'report':
                uploadReport('recHistoryReport', '历史推荐查看车辆检测报告', { ...item });
                // h5检测报告路径
                setWebViewUrl(item.checkReportUrl);
                setWebViewTitle('检测报告');
                if (item.from == 'page') {
                    setOpenPageWebview();
                } else {
                    setOpenDialogWebview();
                }
                break;
            // 查看更多车源
            case 'auctionMore':
                uploadReport('recHistoryAuctionMore', '历史推荐查看更多车源', { dealerId: item.dealerId });
                dispatch({
                    type: 'recommendHistory/updateAuctionFormDataState',
                    payload: { ...formData, dealerId: item.dealerId }
                });
                setOpenAuction();
                break;
            // 查看更多车源推荐记录
            case 'historyMore':
                uploadReport('recHistoryAuctionMore', '历史推荐查看更多车源推荐记录', { ...item });
                setHistoryList(item.recordList);
                if (item.from == 'page') {
                    setOpenPagegHistory();
                } else {
                    setOpenDialogHistory();
                }
                break;
            default:
                break;
        }
    };

    /**
     * 拨打电话成功 回调
     */
    const handleOutCallSucess = () => {
        message('拨打电话成功，请注意接听回拨电话。', 3);
    };

    // useEffect
    // 获取推荐车源数据
    useEffect(() => {
        // 获取搜索条件
        const { queryDate, ...otherData } = getUrlQueryObject();
        const searchData = { queryDate: isEmpty(queryDate) ? new Date() : new Date(queryDate), ...otherData };
        if (!isEmpty(searchData)) {
            dispatch({
                type: 'recommendHistory/updateFormDataState',
                payload: searchData
            });
        }
        // 获取推荐车源数据
        getHistoryDealerList({ ...searchData, currentPage: 1 });
        return () => {
            dispatch({ type: 'recommendHistory/resetState' });
        };
    }, []);
    // 监听页面滚动
    useThrottleEffect(
        () => {
            // 获取页面的总高度
            const totalHeight = document.documentElement.scrollHeight;
            // 获取当前滚动的位置
            const { top = 0 } = scroll || {};
            // 判断是否滚动到底部（考虑到可能的浏览器差异）
            if (Math.ceil(top + viewportHeight + 100) >= totalHeight && !noNextDataList && top != 0) {
                // 滚动到底部时执行的代码
                getHistoryDealerList({ currentPage: currentPage + 1 });
            }
        },
        [scroll],
        { wait: 500 }
    );

    return (
        <>
            <Loader maskOpacity={0.6} open={listLoading} />
            <div className={styles['recommend-today']}>
                {/* 历史推荐/历史推荐 */}
                <TabNav
                    current={1}
                    getHeight={setNavHeight}
                    list={RECOMMEND_TAB}
                    onChange={handleNavChange}
                />
                {/* 匹配的经销商 */}
                <SearchBar
                    barStyle={searchBarStyle}
                    formData={formData}
                    getHeight={setSearchHeight}
                    history={history}
                />
                {/* 推荐车源列表 */}
                <div className={styles['recommend-today-body']} style={bodyStyle}>
                    {dataList.length ? (
                        dataList.map((item) => (
                            <ListItem itemData={item} key={item.dealerId} onClick={handleItemClick} />
                        ))
                    ) : (
                        <Empty />
                    )}
                </div>
                {noNextDataList && <BottomLine />}
            </div>
            {/* 电话:TODO */}
            <OutCall
                autoCall={false}
                onClose={setCloseCall}
                onFetchData={handleOutCallSucess}
                open={openCall}
                port={outCallPort}
            />
            {/* 车源详情/车商详情 */}
            <WebViewDrawer
                hash='web-view-page'
                iframeURL={webViewUrl}
                onClose={setClosePageWebview}
                open={openPageWebview}
                title={webViewTitle}
            />
            <WebViewDrawer
                hash='web-view-dialog'
                iframeURL={webViewUrl}
                onClose={setCloseDialogWebview}
                open={openDialogWebview}
                title={webViewTitle}
            />
            {/* 查看更多车源 */}
            <AuctionDrawer onClick={handleItemClick} onClose={setCloseAuction} open={openAuction} />

            {/* 查看车源更多推荐记录 */}
            <HistoryDrawer
                from='page'
                listData={historyList}
                onClose={setClosePageHistory}
                open={openPageHistory}
            />
            <HistoryDrawer
                from='dialog'
                listData={historyList}
                onClose={setCloseDialogHistory}
                open={openDialogHistory}
            />
        </>
    );
});

export default connect(({ recommendHistory, loading }) => ({ ...recommendHistory, loading }))(
    RecommendHistory
);
