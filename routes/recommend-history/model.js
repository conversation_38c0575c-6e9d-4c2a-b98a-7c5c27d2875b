/**
 * 车源智推-历史推荐
 */
import * as api from './api.js';

/**
 * 默认请求参数
 * @returns {object}   默认参数
 */
const defaultFormData = () => ({
    // 搜索关键字: 车商名、车商ID、手机号
    searchValue: '',
    // 查询日期, 如: 2025-04-03
    queryDate: new Date(),
    // 竞拍结果, 1-已成交,2-已中标
    auctionResult: '',
    // 品牌车系车型
    brandId: '',
    familyId: '',
    modelId: '',
    brandName: '',
    familyName: '',
    modelName: '',
    // 车龄：最大/最小
    maxAge: '',
    minAge: '',
    // 分页参数
    currentPage: 1,
    pageSize: 10
});
/**
 * 默认参数
 * @returns {object}   默认参数
 */
const initState = () => ({
    //  历史推荐列表-经销商维度 数据列表
    dataList: [],
    // 没有下一页
    noNextDataList: false,
    // 没有下一页:车源
    noNextAuctionList: false,
    // 历史推荐列表-经销商维度 查询条件
    formData: defaultFormData(),
    // 历史推荐列表-经销商名下车源维度 查询条件
    auctionFormData: {
        ...defaultFormData(),
        pageSize: 20,
        // 经销商ID, 经销商维度详情页查询
        dealerId: ''
    },
    totalNum: 0
});

export default {
    namespace: 'recommendHistory',

    state: initState(),

    reducers: {
        /**
         * 重置状态
         * @returns {object}                     更新过的state
         */
        resetState() {
            return initState();
        },

        /**
         * 更新状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateState(state, action) {
            return {
                ...state,
                ...action.payload
            };
        },

        /**
         * 更新状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateFormDataState(state, action) {
            return {
                ...state,
                formData: {
                    ...state.formData,
                    ...action.payload
                }
            };
        },

        /**
         * 更新状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateAuctionFormDataState(state, action) {
            return {
                ...state,
                auctionFormData: {
                    ...state.auctionFormData,
                    ...action.payload
                }
            };
        }
    },

    effects: {
        /**
         * 历史推荐列表-经销商维度
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @param   {Function}             param1.select  select
         * @returns {object}                              用户信息
         */
        *getHistoryDealerList({ payload }, { call, put, select }) {
            const dataState = yield select((state) => state.recommendHistory);
            const { result } = yield call(api.historyDealerList, payload);
            const { currentPage = 1, dataList = [], totalNum = 0 } = result || {};
            yield put({
                type: 'updateState',
                payload: {
                    dataList: Array.isArray(dataList)
                        ? currentPage == 1
                            ? dataList
                            : dataState.dataList.concat(dataList)
                        : [],
                    noNextDataList: dataList.length < dataState.formData.pageSize,
                    formData: {
                        ...dataState.formData,
                        currentPage: payload.currentPage
                    },
                    totalNum
                }
            });
        },

        /**
         * 历史推荐列表-经销商名下车源维度
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @param   {Function}             param1.select  select
         * @returns {object}                              用户信息
         */
        *getHistoryAuctionList({ payload }, { call, put, select }) {
            const { result } = yield call(api.historyAuctionList, payload);
            yield put({
                type: 'updateState',
                payload: {
                    auctionFormData: {
                        ...payload,
                        currentPage: result.currentPage
                    }
                }
            });
            return result;
        }
    }
};
