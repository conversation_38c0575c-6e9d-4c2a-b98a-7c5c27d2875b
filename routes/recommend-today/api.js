/**
 * @file 我的客户-我的客户
 */

/* requires JS */
import { postRequest } from 'common/js/fetch.js';
import { PORT_URL } from 'common/js/constant.js';
import { formatPostData } from 'common/js/utils.js';

/**
 * 获取商户签到信息数据
 * <AUTHOR>
 * @param    {object}                 data          请求参数对象
 * @returns  {Promise}                              返回一个Feach的Promise对象
 */
export function getDealerLabelList(data) {
    let opts = {
        url: PORT_URL.labelCode,
        data
    };
    return postRequest(opts);
}

/**
 * 获取拍品标签信息数据
 * <AUTHOR>
 * @param    {object}                 data          请求参数对象
 * @returns  {Promise}                              返回一个Feach的Promise对象
 */
export function getAuctionTagList(data) {
    let opts = {
        url: PORT_URL.getAuctionTag,
        data
    };
    const defaultData = [
        {
            labelName: '不限',
            labelCode: ''
        }
    ];
    return postRequest(opts).then(({ result }) => {
        console.log('auction:', result);
        return Array.isArray(result)
            ? [...defaultData, ...result].map((item) => ({
                  label: item.labelName,
                  value: item.labelCode
              }))
            : [];
    });
}

/**
 * 获取推荐列表数据
 * <AUTHOR>
 * @param    {object}                 data          请求参数对象
 * @returns  {Promise}                              返回一个Feach的Promise对象
 */
export function getTodayRecommendList(data) {
    let opts = {
        url: PORT_URL.getTodayRecommendList,
        data: formatPostData(data)
    };
    return postRequest(opts);
}

/**
 * 获取推荐列表数据
 * <AUTHOR>
 * @param    {object}                 data          请求参数对象
 * @returns  {Promise}                              返回一个Feach的Promise对象
 */
export function getDealerList(data) {
    let opts = {
        url: PORT_URL.getDealerList,
        data: formatPostData(data)
    };
    return postRequest(opts);
}

/**
 * 推荐给车商
 * <AUTHOR>
 * @param    {object}                 data          请求参数对象
 * @returns  {Promise}                              返回一个Feach的Promise对象
 */
export function pushToDealer(data) {
    let opts = {
        url: PORT_URL.pushToDealer,
        data
    };
    return postRequest(opts);
}
