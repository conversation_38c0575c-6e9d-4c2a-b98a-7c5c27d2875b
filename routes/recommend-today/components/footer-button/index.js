// JS
import React, { memo } from 'react';
// 组件
import { Button } from 'ttp-library';
// LESS
import styles from './index.less';

const FooterButton = memo((props) => {
    // props
    const {
        loading,
        handleCopyClick,
        handleMarchClick,
        goBack,
        handleRecommendClick,
        isDealer = false
    } = props;

    return (
        <div className={styles['recommend-today-footer']}>
            {isDealer ? (
                <>
                    <Button block className={styles['goback-btn-dealer']} onClick={goBack} skin='white'>
                        返回
                    </Button>

                    <Button
                        block
                        className={styles['recommend-btn']}
                        loading={loading}
                        onClick={handleRecommendClick}>
                        推荐
                    </Button>
                </>
            ) : (
                <>
                    <Button block className={styles['goback-btn']} onClick={goBack} skin='white'>
                        返回
                    </Button>
                    <Button
                        block
                        className={styles['share-btn']}
                        loading={loading}
                        onClick={handleCopyClick}
                        skin='white'>
                        复制口令
                    </Button>
                    <Button
                        block
                        className={styles['matching-btn']}
                        loading={loading}
                        onClick={handleMarchClick}>
                        去匹配
                    </Button>
                </>
            )}
        </div>
    );
});

export default FooterButton;
