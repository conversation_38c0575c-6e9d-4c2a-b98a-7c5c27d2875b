// JS
import React, { memo } from 'react';
import { executeCB, isEmpty } from 'ttp-utils';
// 组件
import { List, Checkbox, Tag } from 'ttp-library';
// LESS
import styles from './index.less';

/**
 * 我的客户数据ITEM
 * @param   {object}                     props props
 * @returns {React.ReactElement}               jsx
 */
const ListItem = memo((props) => {
    // props
    const { itemData, onFollowUp, onItemClick } = props;
    // 计算属性
    /**
     * 跳转详情
     */
    const handleClick = () => {
        executeCB(onItemClick, itemData);
    };
    /**
     * 拨打电话
     */
    const handleCallClick = () => {
        executeCB(onFollowUp, itemData);
    };

    return (
        <div className={styles['list-item']}>
            <List className={styles['list-box']}>
                <List.Item className={styles['list-box-item']}>
                    <div className={styles['list-box-item-content']}>
                        <div className={styles['list-box-item-radio']}>
                            <Checkbox value={itemData.dealerId} />
                        </div>
                        <div className={styles['list-box-item-car-info']}>
                            <div
                                className={styles['list-box-item-car-info-item-title']}
                                onClick={handleClick}>
                                <span className={styles['list-box-item-name']}>
                                    [{itemData.city}] {itemData.dealerName}
                                </span>
                                <span className={styles['list-box-item-id']}>ID:{itemData.dealerId}</span>
                            </div>
                            <div className={styles['list-box-item-car-info-item']}>
                                <div className={styles['list-box-item-info']}>
                                    <div className={styles['list-box-item-info-label']}>
                                        {Array.isArray(itemData.labels) &&
                                            itemData.labels.map((item) => {
                                                return (
                                                    <Tag bg='#fff' color='#00A2E8' key={item}>
                                                        {item}
                                                    </Tag>
                                                );
                                            })}
                                    </div>

                                    <div className={styles['list-box-item-price-content']}>
                                        {(!isEmpty(itemData.thisBidTips) ||
                                            !isEmpty(itemData.thisBidPrice)) && (
                                            <span className='f-mr5'>
                                                {isEmpty(itemData.thisBidTips) ? '' : itemData.thisBidTips}
                                                {isEmpty(itemData.thisBidPrice)
                                                    ? ''
                                                    : `¥${itemData.thisBidPrice}`}
                                            </span>
                                        )}
                                        {(!isEmpty(itemData.historyBidTips) ||
                                            !isEmpty(itemData.historyBidPrice)) && (
                                            <span>
                                                {isEmpty(itemData.historyBidTips)
                                                    ? ''
                                                    : itemData.historyBidTips}
                                                {isEmpty(itemData.historyBidPrice)
                                                    ? ''
                                                    : `¥${itemData.historyBidPrice}`}
                                            </span>
                                        )}
                                    </div>
                                </div>
                                <div className={styles['list-box-item-call-info']}>
                                    <span className='icon-tel' onClick={handleCallClick} />
                                    {/* 推荐提示 */}
                                    <span className={styles['list-box-item-call-info-text']}>
                                        {itemData.recommendTips}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </List.Item>
            </List>
        </div>
    );
});

export default ListItem;
