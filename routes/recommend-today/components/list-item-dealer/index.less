@import '../../../../common/less/validation.less';
@import '../../../../common/less/mixins.less';
@import '../../../../common/less/list.less';

.list-item {
    margin-bottom: 10 / @rem;
    position: relative;
    background-color: #fff;
    border-radius: 9 / @rem;

    &-title {
        display: flex;
        align-items: center;
    }

    &-title-prev {
        display: inline-block;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        max-width: 100 / @rem;
        text-align: right;
    }

    &-icon-red {
        width: 10 / @rem;
        height: 10 / @rem;
        background: @color-red;
        border: 1px solid @color-white;
        border-radius: 5 / @rem;
        position: absolute;
        left: 0 / @rem;
        top: 0 / @rem;
        z-index: 10;
    }

    .list-box {
        margin: 0;
        position: relative;

        &:after {
            .setTopLine(@color-border);
        }

        &-item {
            padding: (10 / @rem) (5 / @rem);
            min-height: 100 / @rem;
            overflow: inherit;
            position: relative;

            &:after {
                .setBottomLine(@color-border);
            }
            &-content {
                display: flex;
                align-items: center;
            }

            &:last-child:after {
                border-bottom-width: 0px;
            }
            &-radio {
                width: 30 / @rem;
                margin-left: 10 / @rem;
            }
            &-car-info {
                // padding-right: 6 / @rem;
                flex: 1;
            }
            &-car-pic {
                width: 120 / @rem;
                height: 80 / @rem;
                margin-right: 10 / @rem;
                border-radius: 4 / @rem;

                img {
                    width: 100%;
                    height: 100%;
                    border-radius: 4 / @rem;
                }
            }
            &-car-info-item {
                display: flex;
                white-space: wrap;
                align-items: start;
                justify-content: space-between;
            }
            &-car-info-item-title {
                font-size: 14 / @rem;
                color: #222;
                line-height: 20 / @rem;
                font-weight: 600;
                margin-bottom: 5 / @rem;
            }
            &-name {
                vertical-align: middle;
                max-width: 215 / @rem;
                height: 22 / @rem;
                overflow: hidden;
                text-overflow: ellipsis;
                display: inline-block;
                vertical-align: middle;
            }
            &-id {
                font-weight: 400;
                font-size: 14 / @rem;
                color: @color-gray-6;
                vertical-align: middle;
                margin-left: 10 / @rem;
            }
            &-car-info-arrow {
                .arr-next(10 /@rem, 1/@rem, #ddd);
                position: absolute;
                right: 15 / @rem;
                top: 50 / @rem;
            }
            &-info {
                color: #808a8f;
                font-weight: 600;
                font-size: 11 / @rem;
                &-label {
                    display: flex;
                    flex-wrap: wrap;
                }
            }
            &-price-content {
                display: flex;
                flex-wrap: wrap;
                flex: 1;
                overflow: hidden;

                font-weight: 600;
                font-size: 12 / @rem;
                color: #ff5e39;
            }

            &-call-info {
                text-align: center;
                min-width: 80 / @rem;
            }
            &-call-info-text {
                color: #999999;
                line-height: 14 / @rem;
                font-size: 10 / @rem;
                text-align: center;
                display: block;
                margin-top: 5 / @rem;
            }
            &-have-price {
                margin-right: 5 / @rem;
            }
        }
        :global {
            .Saitama-tag {
                border-radius: 3 / @rem;
                padding: 2 / @rem 3 / @rem;
                font-weight: 400;
                color: #00a2e8;
                font-size: 12 / @rem;
                margin-right: 5 / @rem;
                margin-bottom: 5 / @rem;
                line-height: 1;
            }

            .Saitama-tag-box {
                font-size: 12 / @rem;
            }
        }
    }

    .tag-btn {
        margin-left: 5 / @rem;
        padding: 0 (5 / @rem);
    }

    &-footer {
        width: 100%;
        text-align: center;
        font-weight: 400;
        font-size: 14 / @rem;
        line-height: 1.75;
        color: @color-gray-3;
    }
}
