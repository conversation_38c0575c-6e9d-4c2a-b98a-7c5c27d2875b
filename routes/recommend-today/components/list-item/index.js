// JS
import React, { memo } from 'react';
import { executeCB } from 'ttp-utils';
// 组件
import { List, Radio } from 'ttp-library';
import CarItem from 'components/car-item/index.js';
// LESS
import styles from './index.less';

/**
 * 我的客户数据ITEM
 * @param   {object}                     props props
 * @returns {React.ReactElement}               jsx
 */
const ListItem = memo((props) => {
    // props
    const { itemData, onSelect, onItemClick } = props;

    /**
     * 选择车源
     */
    const handleSelectCar = () => {
        executeCB(onSelect, itemData);
    };

    return (
        <div className={styles['rec-today-item']}>
            <List className={styles['list-box']}>
                <List.Item
                    arrow='right'
                    className={styles['list-box-item']}
                    icon={<Radio onChange={handleSelectCar} value={itemData.auctionId} />}>
                    <CarItem classname={styles['list-box-car']} itemData={itemData} onClick={onItemClick} />
                </List.Item>
            </List>
        </div>
    );
});

export default ListItem;
