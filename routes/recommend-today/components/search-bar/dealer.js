// JS
import React, { memo, useState, useRef, useMemo, useEffect } from 'react';
import { connect } from 'dva';
import { useSetState, useUpdateEffect } from 'ahooks';
import { executeCB, isEmpty } from 'ttp-utils';
import cls from 'classnames';
import { uploadReport } from 'common/js/utils.js';
// 组件
import { Button, DropdownMenu } from 'ttp-library';
import SearchBox from 'components/search-box/index.js';
import SearchTagBox from 'components/search-tag-bar/index.js';
import SilderBar from 'components/slider-bar/index.js';
import RadioTags from 'components/radio-tags/index.js';
const { DropdownItem } = DropdownMenu;
// LESS
import styles from './index.less';

/**
 * 下拉筛选标签默认值
 * @type {Array}W
 */
const FILTER_LABEL_LIST = [
    {
        itemKey: 'systemRecommend',
        defaultLabel: '系统推荐'
    },
    {
        itemKey: 'noPriceRange',
        defaultLabel: '未出价天数'
    },
    {
        itemKey: 'equityMember',
        defaultLabel: '是否权益包会员'
    }
];

const FILTER_SOURCE_LIST = {
    systemRecommend: [
        {
            label: '系统推荐',
            value: 1
        }
    ],

    equityMember: [
        {
            label: '权益包会员',
            value: 1
        },
        {
            label: '非权益包会员',
            value: 0
        }
    ]
};

/**
 * 价格区间(最小值/最大值)
 * @type {Array}
 */
const defaultMoneyRange = [0, 50];

/**
 * 下拉筛选底部按钮
 * @param   {object}         props props
 * @returns {object}               jsx
 */
const DropdownFooter = memo(({ classname = styles['dropdown-footer'], onClose, onSubmit }) => (
    <div className={classname}>
        <Button className={styles['footer-button']} onClick={onClose} skin='white'>
            取消
        </Button>
        <Button className={styles['footer-button']} onClick={onSubmit}>
            确认
        </Button>
    </div>
));

/**
 * 我的客户-搜索条件
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const DealerSearchBar = memo((props) => {
    // props
    const { dispatch, classname, dealerFormData, getHeight, barStyle, totalDealerNum } = props;
    // state
    const [searchData, setSearchData] = useSetState({ ...dealerFormData });
    const [noPriceRange, setNoPriceRange] = useState([...defaultMoneyRange]);

    // 计算属性
    // 未出价天数标题
    const noPriceRangeTitle = useMemo(() => {
        const [min, max] = noPriceRange;
        if (isEmpty(max) || max > defaultMoneyRange[1]) {
            return `未出价天数：${min}天以上`;
        }
        return `未出价天数：${min}至${max}天`;
    }, [noPriceRange]);
    // 选中的tag数据
    const searchTagsList = useMemo(() => {
        const { queryText, noBidDaysMax, noBidDaysMin, systemRecommend, equityMember } = dealerFormData;
        console.log('dealerFormData:', dealerFormData);
        const tags = [];
        if (!isEmpty(queryText)) {
            tags.push({
                key: 'queryText',
                label: queryText,
                clearKey: ['queryText']
            });
        }
        // 是否系统推荐
        if (!isEmpty(systemRecommend)) {
            tags.push({
                key: 'systemRecommend',
                label: '系统推荐',
                clearKey: ['systemRecommend']
            });
        }
        // 是否权益包会员
        if (!isEmpty(equityMember)) {
            tags.push({
                key: 'equityMember',
                label: equityMember == 1 ? '权益包会员' : '非权益包会员',
                clearKey: ['equityMember']
            });
        }
        // 未出价天数最大值
        if (!isEmpty(noBidDaysMin) || !isEmpty(noBidDaysMax)) {
            tags.push({
                key: 'noPriceRange',
                label: noPriceRangeTitle,
                clearKey: ['noBidDaysMin', 'noBidDaysMax']
            });
        }
        return tags;
    }, [dealerFormData]);

    // 常量
    const searchRef = useRef(null);
    const dropRef = useRef(null);
    const searchClasss = cls(styles['search-bar'], classname);

    // 函数请求
    /**
     * 获取搜索框高度
     */
    const getSearchHight = () => {
        const { height } = searchRef.current.getBoundingClientRect();
        console.log('getSearchHight', height);
        executeCB(getHeight, height);
    };

    /**
     * 还原state状态
     * @param   {object}               dealerFormData  原始筛选条件
     */
    const resetState = (dealerFormData) => {
        setSearchData(dealerFormData);
        setNoPriceRange([
            isEmpty(dealerFormData.noBidDaysMin) ? defaultMoneyRange[0] : Number(dealerFormData.noBidDaysMin),
            isEmpty(dealerFormData.noBidDaysMax) ? '' : Number(dealerFormData.noBidDaysMax)
        ]);
    };

    /**
     * 获取推荐车源信息
     * @param   {object}               data  请求参数
     */
    const getDealerList = (data = {}) => {
        const searchParamsData = { ...dealerFormData, ...data, currentPage: 1 };
        dispatch({
            type: 'recommendToday/updateDealerFormDataState',
            payload: data
        });
        dispatch({
            type: 'recommendToday/getDealerList',
            payload: searchParamsData
        });
    };

    /**
     * 车商筛选
     * @param   {object}               data 当前选中数据
     */
    const dealerFilter = (data = {}) => {
        console.log('dealerFilter--data', data, { ...searchData, ...data });
        uploadReport('recDealerFilter', '今日推荐车商列表筛选', { ...searchData, ...data });

        // 关闭下拉状态
        dropRef.current?.closeAllDrawer();
        getDealerList({ ...searchData, ...data });
    };
    /**
     * 处理搜索提交事件
     */
    const handleSumbit = () => {
        dealerFilter();
    };

    /**
     * 搜索框点击搜索
     * @param   {string}               queryText 当前选中数据
     */
    const handleSearchChange = (queryText) => {
        uploadReport('recDealerSearch', '今日推荐车商列表筛选', { queryText });

        getDealerList({ queryText });
    };

    /**
     * 处理关闭事件
     */
    const handleClose = () => {
        // 关闭下拉状态
        dropRef.current?.closeAllDrawer();
        resetState(dealerFormData);
    };

    /**
     * 删除筛选条件
     * @param   {Array<string>}               fromDataKeys 筛选条件KEY
     */
    const handleTagBoxClose = (fromDataKeys) => {
        console.log('handleTagBoxClose:', fromDataKeys);
        const resetData = fromDataKeys.reduce((data, cur) => {
            data[cur] = '';
            return data;
        }, {});
        setSearchData(resetData);
        dealerFilter(resetData);
        console.log('dealerForm:', dealerFormData);
    };

    /**
     * 未出价天数区间-滑块拖动事件
     * @param   {Array<number>}               value 当前选中数据
     */
    const handleNoPriceRangeChange = (value) => {
        console.log('handleNoPriceRangeChange--value', value);
        setNoPriceRange(value);
        setSearchData({
            noBidDaysMin: value[0],
            noBidDaysMax: value[1] > defaultMoneyRange[1] ? '' : value[1]
        });
    };

    /**
     * 系统推荐点击
     * @param   {string}               value 选中的值
     */
    const handleRecommendChange = (value) => {
        setSearchData({
            systemRecommend: value
        });
    };

    /**
     * 是否权益会员
     * @param   {string}                value  选中值
     */
    const handleIsEquityChange = (value) => {
        console.log('handleIsEquityChange--value', value);
        setSearchData({
            equityMember: value
        });
    };

    // useEffect
    useUpdateEffect(() => {
        // 重置状态
        resetState(dealerFormData);
        // 获取标签栏高度
        if (searchRef) {
            // 等待弹框关闭后再获取高度
            setTimeout(() => {
                getSearchHight();
            }, 200);
        }
    }, [dealerFormData]);

    useEffect(() => {
        // 获取标签栏高度
        if (searchRef) {
            getSearchHight();
        }
    }, [searchRef]);

    return (
        <div className={searchClasss} style={barStyle}>
            <div ref={searchRef}>
                <SearchBox
                    onSearch={handleSearchChange}
                    placeholder='请输入车商姓名/ID/手机号'
                    search={searchData.queryText}
                />
                <DropdownMenu
                    className={styles['dropdown-dealer']}
                    labelList={FILTER_LABEL_LIST}
                    labelType='light'
                    menuType='tag'
                    ref={dropRef}
                    viewType='tag'>
                    <DropdownItem itemKey='systemRecommend'>
                        <div className={styles['dropdown-content']}>
                            <RadioTags
                                list={FILTER_SOURCE_LIST.systemRecommend}
                                onChange={handleRecommendChange}
                                value={searchData.systemRecommend}
                            />
                            <DropdownFooter onClose={handleClose} onSubmit={handleSumbit} />
                        </div>
                    </DropdownItem>
                    {/* 未出价天数 */}
                    <DropdownItem itemKey='noPriceRange'>
                        <div className={styles['dropdown-content']}>
                            <SilderBar
                                key='moneyRangeSilderBar'
                                onChange={handleNoPriceRangeChange}
                                preTitle={noPriceRangeTitle}
                                sufTitle='单位：天'
                                value={noPriceRange}
                            />
                        </div>
                        <DropdownFooter onClose={handleClose} onSubmit={handleSumbit} />
                    </DropdownItem>

                    {/* 是否权益包会员 */}
                    <DropdownItem itemKey='equityMember'>
                        <div className={styles['dropdown-content']}>
                            <RadioTags
                                list={FILTER_SOURCE_LIST.equityMember}
                                onChange={handleIsEquityChange}
                                value={searchData.equityMember}
                            />
                            <DropdownFooter onClose={handleClose} onSubmit={handleSumbit} />
                        </div>
                    </DropdownItem>
                </DropdownMenu>

                {/* 筛选选中项 */}
                <SearchTagBox onClose={handleTagBoxClose} tagList={searchTagsList} />
                <div className={styles['search-result-info']}>
                    <div className={styles['search-result-total']}>共{totalDealerNum}条记录</div>
                </div>
            </div>
        </div>
    );
});

export default connect(({ recommendToday, loading }) => ({ ...recommendToday, loading }))(DealerSearchBar);
