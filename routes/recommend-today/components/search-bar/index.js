// JS
import React, { memo, useState, useRef, useMemo, useEffect } from 'react';
import { connect } from 'dva';
import { useSetState, useUpdateEffect } from 'ahooks';
import { executeCB, isEmpty } from 'ttp-utils';
import { uploadReport } from 'common/js/utils.js';
import cls from 'classnames';
// 组件
import { Button, DropdownMenu, Drawer, Mmodel, CityList } from 'ttp-library';
import SearchBox from 'components/search-box/index.js';
import SearchTagBox from 'components/search-tag-bar/index.js';
import SilderBar from 'components/slider-bar/index.js';
import RadioTags from 'components/radio-tags/index.js';
const { DropdownItem } = DropdownMenu;
// LESS
import styles from './index.less';

/**
 * 下拉筛选标签默认值
 * @type {Array}W
 */
const FILTER_LABEL_LIST = [
    {
        itemKey: 'systemRecommend',
        defaultLabel: '系统推荐'
    },
    {
        itemKey: 'auctionTag',
        defaultLabel: '拍品标签'
    },
    {
        itemKey: 'brandCode',
        defaultLabel: '品牌车系'
    },
    {
        itemKey: 'more',
        defaultLabel: '更多'
    }
];

const FILTER_SOURCE_LIST = {
    systemRecommend: [
        {
            label: '系统推荐',
            value: 1
        }
    ]
};

/**
 * 显示全部
 * @type {Array}
 */
const SHOW_ALL = [true, true, true];

/**
 * 价格区间(最小值/最大值)
 * @type {Array}
 */
const defaultMoneyRange = [0, 50];

/**
 * 车龄(最小值/最大值)
 * @type {Array}
 */
const defaultAutoAge = [0, 10];

/**
 * 下拉筛选底部按钮
 * @param   {object}         props props
 * @returns {object}               jsx
 */
const DropdownFooter = memo(({ classname = styles['dropdown-footer'], onClose, onSubmit }) => (
    <div className={classname}>
        <Button className={styles['footer-button']} onClick={onClose} skin='white'>
            取消
        </Button>
        <Button className={styles['footer-button']} onClick={onSubmit}>
            确认
        </Button>
    </div>
));

/**
 * 我的客户-搜索条件
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const SearchBar = memo((props) => {
    // props
    const { dispatch, classname, formData, getHeight, barStyle, totalNum, auctionTagList } = props;
    // state
    const [open, setOpen] = useState(false);
    const [openBrandModel, setOpenBrandModel] = useState(false);
    const [searchData, setSearchData] = useSetState({ ...formData });
    const [moneyRange, setMoneyRange] = useState([...defaultMoneyRange]);
    const [autoAge, setAutoAge] = useState([...defaultAutoAge]);
    const [openCity, setOpenCity] = useState(false);
    const [brandKey, setBrandKey] = useState(Date.now());
    // 计算属性
    // 价格区间选项 - 标题
    const moneyRangeTitle = useMemo(() => {
        const [min, max] = moneyRange;
        if (isEmpty(max) || max > defaultMoneyRange[1]) {
            return `价格：${min}至${defaultMoneyRange[1]}万元以上`;
        }
        return `价格：${min}至${max}万元`;
    }, [moneyRange]);
    // 车龄选项  - 标题
    const autoAgeTitle = useMemo(() => {
        const [min, max] = autoAge;
        if (isEmpty(max) || max > defaultAutoAge[1]) {
            return `车龄：${min}至${defaultAutoAge[1]}年以上`;
        }
        return `车龄：${min}至${max}年`;
    }, [autoAge]);
    // 选中的tag数据
    const searchTagsList = useMemo(() => {
        const {
            searchKeyword,
            minPrice,
            maxPrice,
            cityId,
            cityName,
            minAge,
            maxAge,
            brandName,
            brandId,
            auctionTag,
            auctionTagLabel,
            systemRecommend,
            familyName,
            modelName
        } = formData;
        const tags = [];
        if (!isEmpty(searchKeyword)) {
            tags.push({
                key: 'searchKeyword',
                label: searchKeyword,
                clearKey: ['searchKeyword']
            });
        }
        if (!isEmpty(auctionTag)) {
            tags.push({
                key: 'auctionTag',
                label: auctionTagLabel,
                clearKey: ['auctionTag', 'auctionTagLabel']
            });
        }

        if (!isEmpty(systemRecommend)) {
            tags.push({
                key: 'systemRecommend',
                label: '系统推荐',
                clearKey: ['systemRecommend']
            });
        }
        // 最大车价
        if (!isEmpty(minPrice) || !isEmpty(maxPrice)) {
            tags.push({
                key: 'moneyRange',
                label: moneyRangeTitle,
                clearKey: ['minPrice', 'maxPrice']
            });
        }
        // 最大车龄
        if (!isEmpty(minAge) || !isEmpty(maxAge)) {
            tags.push({
                key: 'autoAgeRange',
                label: autoAgeTitle,
                clearKey: ['minAge', 'maxAge']
            });
        }
        if (!isEmpty(cityId)) {
            tags.push({
                key: 'cityId',
                label: cityName,
                clearKey: ['cityId', 'cityName']
            });
        }
        if (!isEmpty(brandId)) {
            tags.push({
                key: 'brandId',
                label:
                    brandName +
                    `${familyName ? '-' + familyName : ''}` +
                    `${modelName ? '-' + modelName : ''}`,
                clearKey: ['brandId', 'brandName', 'familyId', 'familyName', 'modelId', 'modelName']
            });
        }
        return tags;
    }, [formData, moneyRangeTitle]);

    // 常量
    const searchRef = useRef(null);
    const dropRef = useRef(null);
    const searchClasss = cls(styles['search-bar'], classname);
    const drawerContent = cls(styles['drawer-content'], styles['drawer-content__flex']);

    // 函数请求

    /**
     * 获取搜索框高度
     */
    const getSearchHight = () => {
        const { height } = searchRef.current.getBoundingClientRect();
        console.log('getSearchHight', height);
        executeCB(getHeight, height);
    };

    /**
     * 还原state状态
     * @param   {object}               formData  原始筛选条件
     */
    const resetState = (formData) => {
        setSearchData(formData);
        setMoneyRange([
            isEmpty(formData.minPrice) ? defaultMoneyRange[0] : Number(formData.minPrice),
            isEmpty(formData.maxPrice) ? '' : Number(formData.maxPrice)
        ]);
        setAutoAge([
            isEmpty(formData.minAge) ? defaultMoneyRange[0] : Number(formData.minAge),
            isEmpty(formData.maxAge) ? '' : Number(formData.maxAge)
        ]);
    };

    /**
     * 获取推荐车源信息
     * @param   {object}               data  请求参数
     */
    const getRecommendList = (data = {}) => {
        const searchParamsData = { ...formData, ...data, currentPage: 1 };
        dispatch({
            type: 'recommendToday/updateFormDataState',
            payload: data
        });
        dispatch({
            type: 'recommendToday/getTodayRecommendList',
            payload: searchParamsData
        });
    };
    /**
     * 搜索框点击搜索
     * @param {string}         searchKeyword 当前选中数据
     */
    const handleSearchChange = (searchKeyword) => {
        uploadReport('recTodaySearch', '今日推荐车源列表搜索', { searchKeyword });
        getRecommendList({ searchKeyword });
    };

    /**
     * 处理关闭事件
     */
    const handleClose = () => {
        // 关闭下拉状态
        dropRef.current?.closeAllDrawer();
        resetState(formData);
    };

    /**
     * 处理拍品标签筛选
     * @param {object}         data 当前选中数据
     */
    const auctionFilter = (data = {}) => {
        console.log('dealerFilter--data', data, { ...searchData, ...data });
        uploadReport('recTodayFilter', '今日推荐车源列表筛选', { ...searchData, ...data });
        // 关闭下拉状态
        dropRef.current?.closeAllDrawer();
        getRecommendList({ ...searchData, ...data });
    };

    /**
     * 处理搜索提交事件
     */
    const handleSumbit = () => {
        auctionFilter();
    };

    /**
     * 处理品牌车系选择事件
     * @param {object}         data 当前选中数据
     */
    const handleBrandChange = (data) => {
        console.log('handleBrandChange--data', data);
        const stateData = {
            brandId: data.brandId || '',
            brandName: data.brandName || '',
            familyId: data.familyId || '',
            familyName: data.familyName || '',
            modelId: data.modelId || '',
            modelName: data.modelName || ''
        };
        setSearchData(stateData);
        if (!open) {
            // getRecommendList(stateData);
            auctionFilter(stateData);
        }
    };

    /**
     * 处理关闭品牌模型的函数
     */
    const handleBrandClose = () => {
        setOpenBrandModel(false);
        // 关闭下拉状态
        dropRef.current?.closeAllDrawer();
    };

    /**
     * 价格区间-滑块拖动事件
     * @param {Array<number>}         value 当前选中数据
     */
    const handleMoneyRangeChange = (value) => {
        console.log('handleMoneyRangeChange--value', value, defaultMoneyRange);
        setMoneyRange(value);
        setSearchData({ minPrice: value[0], maxPrice: value[1] > defaultMoneyRange[1] ? '' : value[1] });
    };

    /**
     * 车龄区间-滑块拖动事件
     * @param {Array<number>}         value 当前选中数据
     */
    const handleAutoAgeChange = (value) => {
        console.log('handleAutoAgeChange--value', value);
        setAutoAge(value);
        setSearchData({ minAge: value[0], maxAge: value[1] > defaultAutoAge[1] ? '' : value[1] });
    };

    /**
     * 更多筛选条件关闭事件
     */
    const handleDrawerClose = () => {
        setOpen(false);
        handleClose();
    };

    /**
     * 更多筛选条件事件
     */
    const handleDrawerSumbit = () => {
        setOpen(false);
        handleSumbit();
    };

    /**
     * 返回当前打开的是哪一项
     * @param {string}         itemKey 当前选中筛选项
     */
    const handleSearchOpenItem = (itemKey) => {
        // 品牌车系
        if (itemKey === 'brandCode') {
            setOpenBrandModel(true);
        }
        // 更多
        if (itemKey === 'more') {
            setOpen(true);
        }
    };

    /**
     * 删除筛选条件
     * @param {Array<string>}         fromDataKeys 筛选条件KEY
     */
    const handleTagBoxClose = (fromDataKeys) => {
        console.log('handleTagBoxClose:', fromDataKeys);
        const resetData = fromDataKeys.reduce((data, cur) => {
            data[cur] = '';
            return data;
        }, {});
        console.log('resetData:', resetData);
        if (fromDataKeys.includes('brandId')) {
            setBrandKey(Date.now());
        }
        setSearchData(resetData);
        auctionFilter(resetData);
        // getRecommendList(resetData);
    };

    /**
     * 打开城市选择
     */
    const handleCityOpen = () => {
        setOpenCity(true);
    };

    /**
     * 选择后的回调
     * @param  {string}               value 选择后的值
     * @param  {object}               row   行数据
     */
    const handleCitySelect = (value, row) => {
        // setCityId(row.id);
        setSearchData({
            cityId: row.id,
            cityName: row.cityName
        });
        setOpenCity(false);
    };

    /**
     * 系统推荐点击
     * @param   {string}               value 选中的值
     */
    const handleRecommendChange = (value) => {
        setSearchData({
            systemRecommend: value
        });
    };
    /**
     * 拍品标签点击
     * @param   {string}               value 选中的值
     * @param   {object}               row   选中的数据
     */
    const handleAuctionTagChange = (value, row) => {
        setSearchData({
            auctionTag: value,
            auctionTagLabel: row.label
        });
    };

    /**
     * 城市关闭
     */
    const handleCityClose = () => {
        console.log('close');
        setOpenCity(false);
    };

    // useEffect
    useUpdateEffect(() => {
        // 重置状态
        resetState(formData);
        // 获取标签栏高度
        if (searchRef) {
            // 等待弹框关闭后再获取高度
            setTimeout(() => {
                getSearchHight();
            }, 200);
        }
    }, [formData]);

    useEffect(() => {
        // 获取标签栏高度
        if (searchRef) {
            getSearchHight();
        }
    }, [searchRef]);

    return (
        <div className={searchClasss} style={barStyle}>
            <div ref={searchRef}>
                <SearchBox
                    onSearch={handleSearchChange}
                    placeholder='请输入品牌进行模糊搜索'
                    search={searchData.searchKeyword}
                />
                <DropdownMenu
                    className={styles['dropdown']}
                    labelList={FILTER_LABEL_LIST}
                    labelType='light'
                    menuType='tag'
                    onOpenItem={handleSearchOpenItem}
                    ref={dropRef}
                    viewType='tag'>
                    <DropdownItem itemKey='systemRecommend'>
                        <div className={styles['dropdown-content']}>
                            <RadioTags
                                list={FILTER_SOURCE_LIST.systemRecommend}
                                onChange={handleRecommendChange}
                                value={searchData.systemRecommend}
                            />
                            <DropdownFooter onClose={handleClose} onSubmit={handleSumbit} />
                        </div>
                    </DropdownItem>
                    <DropdownItem itemKey='auctionTag'>
                        <div className={styles['dropdown-content']}>
                            <RadioTags
                                list={auctionTagList}
                                onChange={handleAuctionTagChange}
                                value={searchData.auctionTag}
                            />
                            <DropdownFooter onClose={handleClose} onSubmit={handleSumbit} />
                        </div>
                    </DropdownItem>
                    <DropdownItem itemKey='brandCode' />
                    <DropdownItem itemKey='more' />
                </DropdownMenu>

                {/* 筛选选中项 */}
                <SearchTagBox onClose={handleTagBoxClose} tagList={searchTagsList} />
                <div className={styles['search-result-info']}>
                    <div className={styles['search-result-total']}>共{totalNum}条记录</div>
                </div>
            </div>
            {/* 更多筛选弹窗 */}
            <Drawer destroy={false} full hasHeader={false} hash='drawer-more' mode='right' open={open}>
                <div className={styles['drawer-main']}>
                    <div className={styles['drawer-content']}>
                        <h2 className={styles['label-code-title']}>系统推荐</h2>
                        <RadioTags
                            list={FILTER_SOURCE_LIST.systemRecommend}
                            onChange={handleRecommendChange}
                            value={searchData.systemRecommend}
                        />
                    </div>
                    <div className={styles['drawer-content']}>
                        <h2 className={styles['label-code-title']}>拍品标签</h2>
                        <RadioTags
                            list={auctionTagList}
                            onChange={handleAuctionTagChange}
                            value={searchData.auctionTag}
                        />
                    </div>
                    <div className={drawerContent}>
                        <span className={styles['equity-nember-title']}>品牌车型</span>
                        <span className={styles['select-city']} onClick={() => setOpenBrandModel(true)}>
                            <span className={styles['city-text']}>
                                {searchData.brandId
                                    ? searchData.brandName + searchData.familyName + searchData.modelName
                                    : '请选择品牌车型'}
                            </span>
                            <i className={styles['icon-arrow-right']} />
                        </span>
                    </div>
                    <div className={styles['drawer-content']}>
                        <SilderBar
                            // defaultValue={moneyRange}
                            key='moneyRangeSilderBar'
                            onChange={handleMoneyRangeChange}
                            preTitle={moneyRangeTitle}
                            sufTitle='单位：万元'
                            value={moneyRange}
                        />
                    </div>
                    <div className={styles['drawer-content']}>
                        <SilderBar
                            key='autoAge'
                            minAndMax={defaultAutoAge}
                            onChange={handleAutoAgeChange}
                            preTitle={autoAgeTitle}
                            sufTitle='单位：年'
                            value={autoAge}
                        />
                    </div>
                    <div className={drawerContent}>
                        <span className={styles['equity-nember-title']}>城市</span>
                        <span className={styles['select-city']} onClick={handleCityOpen}>
                            <span className={styles['city-text']}>
                                {searchData.cityId ? searchData.cityName : '选择城市'}
                            </span>
                            <i className={styles['icon-arrow-right']} />
                        </span>
                    </div>
                </div>
                <DropdownFooter
                    classname={styles['drawer-footer']}
                    onClose={handleDrawerClose}
                    onSubmit={handleDrawerSumbit}
                />
                <CityList
                    hasHot={false}
                    hasLocation={false}
                    hash='selectCity'
                    isGlobal
                    onChange={handleCitySelect}
                    onClear={handleCityClose}
                    open={openCity}
                    type='cell'
                    value={searchData.cityId}
                />
            </Drawer>

            {/* 品牌车系 */}
            <Mmodel
                brandId={searchData.brandId}
                brandName={searchData.brandName}
                familyId={searchData.familyId}
                familyName={searchData.familyName}
                hasBackBtn
                hasHeader
                isShowAll={SHOW_ALL}
                key={brandKey}
                modelId={searchData.modelId}
                modelName={searchData.modelName}
                onClose={handleBrandClose}
                onSelect={handleBrandChange}
                open={openBrandModel}
                showType={3}
            />
        </div>
    );
});

export default connect(({ recommendToday, loading }) => ({ ...recommendToday, loading }))(SearchBar);
