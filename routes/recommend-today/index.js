// JS
import React, { memo, useEffect, useState } from 'react';
import { connect } from 'dva';
import { useToggle, useScroll, useThrottleEffect, useSetState } from 'ahooks';
import { getUrlQueryStringify } from 'ttp-utils';
import { initWx, getWxInitSate } from 'common/js/wx.js';
import { uploadReport } from 'common/js/utils.js';
import { PAGE_URL } from 'routes/router-config.js';
import { RECOMMEND_TAB, PORT_URL } from 'common/js/constant.js';
// 组件
import { Loader, Empty, message, Radio, Checkbox, OutCall } from 'ttp-library';
import WebViewDrawer from 'components/web-view/index.js';
import TabNav from 'components/tab-nav/index.js';
import BottomLine from 'components/bottom-line/index.js';
import SearchBar from './components/search-bar/index.js';
import ListItem from './components/list-item/index.js';
import FooterButton from './components/footer-button/index.js';
import DealerSearchBar from './components/search-bar/dealer.js';
import ListItemDealer from './components/list-item-dealer/index.js';
// LESS
import styles from './index.less';
const Group = Radio.Group;
const CheckGroup = Checkbox.Group;
// 获取视口的高度
const viewportHeight = window.innerHeight;

/**
 * 默认分享配置
 * @returns {object} 默认分享配置
 */
const defautlShareConfig = () => ({
    title: document.title,
    desc: document.querySelector('meta[name="description"]').getAttribute('content'),
    link: location.href,
    imgUrl: 'https://cdn01.ttpaicdn.com/ttpai/appimages/cons8241/sharelogo.png'
});

/**
 * 车源智推-今日推荐
 * <AUTHOR>
 * @param   {object}                    props 父组件传入的状态
 * @returns {React.ReactElement}              jsx
 */
const RecommendToday = memo((props) => {
    // props
    const {
        history,
        dispatch,
        loading,
        dataList,
        noNextDataList,
        formData,
        dealerFormData,
        dealerList,
        noNextDealerList,
        auctionTagList
    } = props;
    // 筛选条件
    // const { currentPage } = formData;
    const listLoading = loading.effects['recommendToday/getTodayRecommendList'];
    const dealerLoading = loading.effects['recommendToday/getDealerList'];
    const pushLoading = loading.effects['recommendToday/pushToDealer'];

    const [outCallPort, setOutCallPort] = useSetState({ url: PORT_URL.outCall, method: 'POST' });
    // 弹窗开关
    const [openCall, { setLeft: setCloseCall, setRight: setOpenCall }] = useToggle(false);
    // state
    const [navHeight, setNavHeight] = useState(0);
    const [searchHeight, setSearchHeight] = useState(0);
    const [currentItem, setCurrentItem] = useState({});
    const [showDealer, setShowDealer] = useState(false);
    const [selectedDealers, setDealers] = useState([]);
    // webviewDrawer
    const [openWebview, { setLeft: setCloseWebview, setRight: setOpenWebview }] = useToggle(false);
    const [webViewUrl, setWebViewUrl] = useState('');
    const [webViewTitle, setWebViewTitle] = useState('检测报告');

    // scroll
    const scroll = useScroll(document);

    /**
     * 获取推荐车源数据
     * @param   {object}               data  请求参数
     */
    const getRecommendList = (data) => {
        if (!listLoading) {
            dispatch({
                type: 'recommendToday/getTodayRecommendList',
                payload: { ...formData, ...data }
            });
        }
    };

    /**
     * 获取经销商数据
     * @param  {object}  data  请求参数
     */
    const getDealerList = (data) => {
        if (!dealerLoading) {
            dispatch({
                type: 'recommendToday/getDealerList',
                payload: { ...dealerFormData, ...data }
            });
        }
    };

    /**
     * 导航tab切换点击
     * @param {object}         item 当前选中数据
     */
    const handleNavChange = (item) => {
        history.push(item.url);
    };

    /**
     * 获取分享文案
     * @param   {object}               itemData  当前选中数据
     * @returns {string}                         分享文案
     */
    function getShareText(itemData) {
        const shareTextMap = {
            0: '「顾问优选」帮您匹配优质车源！',
            2: '一口价车源，一拍即中，让您无忧拍车'
        };
        return itemData.paiType == 2 ? shareTextMap[itemData.paiType] : shareTextMap[0];
    }

    /**
     * 选择车源
     *
     * @param   {object}               itemData  当前选中数据
     */
    const handleSelect = (itemData) => {
        setCurrentItem(itemData);
        const { auctionId, checkReportUrl, leftFrontImageUrl } = itemData;
        const randomShareText = getShareText(itemData);
        const descText = `精选好车，车况透明，享极速提车服务，即刻拥有，购车无忧。`;

        if (getWxInitSate()) {
            // 分享到微信,分享小程序链接
            wx.onMenuShareWechat({
                title: randomShareText,
                desc: descText,
                link: checkReportUrl, // h5页面的检测报告链接
                imgUrl: leftFrontImageUrl,
                // eslint-disable-next-line jsdoc/require-jsdoc
                success: function () {
                    message('分享成功！');
                    uploadReport('recTodayShareWX', '分享到微信朋友', { auctionId });
                }
            });
            // 分享到朋友圈
            wx.onMenuShareTimeline({
                title: randomShareText,
                link: checkReportUrl, // h5页面的检测报告链接
                imgUrl: leftFrontImageUrl,
                // eslint-disable-next-line jsdoc/require-jsdoc
                success: function () {
                    message('分享到朋友圈成功！');
                    uploadReport('recTodayShareWXQ', '分享到微信朋友圈', { auctionId });
                }
            });
            // 转发车源详
            wx.onMenuShareAppMessage({
                title: randomShareText,
                desc: descText,
                link: checkReportUrl, // h5页面的检测报告链接
                imgUrl: leftFrontImageUrl,
                // eslint-disable-next-line jsdoc/require-jsdoc
                success: function () {
                    message('转发车源详情成功！');
                    uploadReport('recTodayShareAppMessage', '转发到企业微信', { auctionId });
                }
            });
        }
    };

    /**
     * 去匹配
     */
    const handleMarchClick = () => {
        if (!currentItem.auctionId) {
            message('请先选择一个车源再去匹配！');
            return;
        }
        uploadReport('recTodayMarch', '今日推荐-去匹配', {
            auctionId: currentItem.auctionId
        });

        setShowDealer(true);
        dispatch({
            type: 'recommendToday/updateDealerFormDataState',
            payload: { ...dealerFormData, auctionId: currentItem.auctionId }
        });
        getDealerList({
            auctionId: currentItem.auctionId
        });
    };

    /**
     * 复制链接
     */
    const handleCopyClick = () => {
        if (!currentItem.auctionId) {
            message('请先选择一个车源再复制口令！');
            return;
        }
        uploadReport('recTodayClipboard', '今日推荐-复制口令', {
            auctionId: currentItem.auctionId,
            encryptedAuctionId: currentItem.encryptedAuctionId
        });
        wx.setClipboardData({
            data: `给您推荐一辆好车源，您的专属商拓推荐，复制打开经销商APP查看%ttp%RD&${currentItem.encryptedAuctionId}&dealer_admin_rec%ttp%`,
            // eslint-disable-next-line jsdoc/require-jsdoc
            success: () => {
                message('复制成功');
            }
        });
    };

    /**
     * 点击列表项跳转到检测报告页面
     * @param  {object}   item   item
     */
    const handleItemClick = (item) => {
        console.log('handleItemClick', item.checkReportUrl, item);
        // h5检测报告路径
        setWebViewUrl(item.checkReportUrl);
        setWebViewTitle('检测报告');
        setOpenWebview();
    };

    /**
     * 返回上一页面
     */
    const handleGoBack = () => {
        if (showDealer) {
            setShowDealer(false);
            return;
        }
        history.goBack();
    };

    /**
     * 经销商选择change
     * @param {Array}  values   选中的值
     */
    const handleDealerChange = (values) => {
        setDealers(values);
    };

    /**
     * 给经销商打电话
     * @param {object} item   当前数据
     */
    const handleCallDealer = (item) => {
        uploadReport('recDealerCall', '今日推荐-拨打电话', {
            dealerId: item.dealerId
        });
        setOutCallPort({ data: { dealerId: item.dealerId } });
        setOpenCall();
    };

    /**
     * 拨打电话成功 回调
     */
    const handleOutCallSucess = () => {
        message('拨打电话成功，请注意接听回拨电话。', 3);
    };

    /**
     * 推荐给经销商
     * @returns {Promise}  promise
     */
    const handleRecommendClick = () => {
        if (!selectedDealers.length) {
            message('请先选择经销商再推荐！');
            return;
        }
        uploadReport('recDealerPush', '今日推荐-推荐给经销商', {
            auctionId: currentItem.auctionId,
            dealerIds: selectedDealers,
            paiId: currentItem.paiId
        });
        return dispatch({
            type: 'recommendToday/pushToDealer',
            payload: {
                auctionId: currentItem.auctionId,
                dealerIds: selectedDealers,
                paiId: currentItem.paiId
            }
        }).then(() => {
            message('推荐成功');
            setDealers([]);
        });
    };

    /**
     * 查看车商详情
     * @param {object} item  当前车商数据
     */
    const handleDealerItemClick = (item) => {
        // h5车商详情路径
        const url = getUrlQueryStringify(
            { dealerId: item.dealerId, from: 'recommendToday' },
            `${location.protocol}//${location.host}${PAGE_URL.myClientDealer}?`
        );
        setWebViewUrl(url);
        setWebViewTitle('车商详情');
        setOpenWebview();
    };

    // useEffect
    useEffect(() => {
        // 初始化企业微信jssdk
        initWx(false).then(() => {
            // 显示右上角分享菜单内容
            wx.showMenuItems({
                menuList: [
                    'menuItem:share:wechat',
                    'menuItem:share:timeline',
                    'menuItem:share:appMessage',
                    'menuItem:refresh'
                ]
            });
        });

        // 获取推荐车源数据
        getRecommendList({ ...formData, currentPage: 1 });
        // 获取标签列表
        if (auctionTagList.length == 0) {
            dispatch({ type: 'recommendToday/getAuctionTagList' });
        }
        return () => {
            dispatch({ type: 'recommendToday/resetState' });
            // 重置分享
            if (getWxInitSate()) {
                // 分享到微信,分享小程序链接
                wx.onMenuShareWechat(defautlShareConfig());
                // 分享到朋友圈
                wx.onMenuShareTimeline(defautlShareConfig());
                // 转发车源详
                wx.onMenuShareAppMessage(defautlShareConfig());
            }
        };
    }, []);

    // 监听页面滚动
    useThrottleEffect(
        () => {
            // 获取页面的总高度
            const totalHeight = document.documentElement.scrollHeight;
            // 获取当前滚动的位置
            const { top = 0 } = scroll || {};
            const noData = showDealer ? noNextDealerList : noNextDataList;
            // 判断是否滚动到底部（考虑到可能的浏览器差异）
            if (Math.ceil(top + viewportHeight + 100) >= totalHeight && !noData && top != 0) {
                // 滚动到底部时执行的代码
                if (showDealer) {
                    getDealerList({ currentPage: dealerFormData.currentPage + 1 });
                } else {
                    getRecommendList({ currentPage: formData.currentPage + 1 });
                }
            }
        },
        [scroll],
        { wait: 500 }
    );

    return (
        <>
            <Loader maskOpacity={0.6} open={(showDealer ? dealerLoading : listLoading) || pushLoading} />
            <div className={styles['recommend-today']}>
                {/* 今日推荐/历史推荐 */}
                <TabNav
                    current={0}
                    getHeight={setNavHeight}
                    list={RECOMMEND_TAB}
                    onChange={handleNavChange}
                />
                {/* 匹配的经销商 */}
                {showDealer ? (
                    <div>
                        <DealerSearchBar
                            barStyle={{ top: navHeight }}
                            dealerFormData={dealerFormData}
                            getHeight={setSearchHeight}
                        />
                        {/* 推荐车源列表 */}
                        <div
                            className={styles['recommend-today-body']}
                            style={{ marginTop: navHeight + searchHeight + 10 }}>
                            {dealerList.length ? (
                                <CheckGroup onChange={handleDealerChange} value={selectedDealers}>
                                    {dealerList.map((item, index) => (
                                        <ListItemDealer
                                            index={index}
                                            itemData={item}
                                            key={item.dealerId}
                                            onFollowUp={handleCallDealer}
                                            onItemClick={handleDealerItemClick}
                                        />
                                    ))}
                                </CheckGroup>
                            ) : (
                                <Empty />
                            )}
                        </div>
                        {noNextDataList && <BottomLine />}
                        {/* 电话:TODO */}
                        <OutCall
                            autoCall={false}
                            onClose={setCloseCall}
                            onFetchData={handleOutCallSucess}
                            open={openCall}
                            port={outCallPort}
                        />
                    </div>
                ) : (
                    <div>
                        <SearchBar
                            barStyle={{ top: navHeight }}
                            formData={formData}
                            getHeight={setSearchHeight}
                        />
                        {/* 推荐车源列表 */}
                        <div
                            className={styles['recommend-today-body']}
                            style={{ marginTop: navHeight + searchHeight + 10 }}>
                            {dataList.length ? (
                                <Group defaultValue={currentItem.auctionId}>
                                    {dataList.map((item, index) => (
                                        <ListItem
                                            index={index}
                                            itemData={item}
                                            key={item.auctionId}
                                            onItemClick={handleItemClick}
                                            onSelect={handleSelect}
                                        />
                                    ))}
                                </Group>
                            ) : (
                                <Empty />
                            )}
                        </div>
                        {noNextDataList && <BottomLine />}
                    </div>
                )}
            </div>
            {/* 底部按钮：分享/匹配 */}
            <FooterButton
                goBack={handleGoBack}
                handleCopyClick={handleCopyClick}
                handleMarchClick={handleMarchClick}
                handleRecommendClick={handleRecommendClick}
                isDealer={showDealer}
                loading={showDealer ? dealerLoading : listLoading}
            />
            {/* 车源详情 */}
            <WebViewDrawer
                iframeURL={webViewUrl}
                onClose={setCloseWebview}
                open={openWebview}
                title={webViewTitle}
            />
        </>
    );
});

export default connect(({ recommendToday, loading }) => ({ ...recommendToday, loading }))(RecommendToday);
