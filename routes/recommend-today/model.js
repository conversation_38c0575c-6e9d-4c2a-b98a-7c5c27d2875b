/**
 * 车源智推-今日推荐
 */
import * as api from './api.js';

/**
 * 默认参数
 * @returns {object}   默认参数
 */
const initState = () => ({
    tagList: [],
    auctionTagList: [],
    dataList: [],
    // 没有下一页
    noNextDataList: false,
    // 车源推荐列表查询条件数据
    formData: {
        // 搜索关键字
        searchKeyword: '',
        // 拍品标签
        auctionTag: '',
        auctionTagLabel: '',
        // 品牌车系车型
        brandName: '',
        familyName: '',
        modelName: '',
        brandId: '',
        familyId: '',
        modelId: '',
        cityId: '',
        // 最大/最小车龄
        maxAge: '',
        minAge: '',
        // 最大车价(最小值/最大值)
        maxPrice: '',
        minPrice: '',
        // 保证金(最小值/最大值)
        marginMin: '',
        marginMax: '',
        // 系统推荐标识 1 是， 0: 否
        systemRecommend: 1,
        // 分页参数
        currentPage: 1,
        pageSize: 20
    },
    // 车商列表筛选项
    dealerFormData: {
        auctionId: '',
        // 搜索关键字
        queryText: '',
        // // 品牌车系车型
        // brandId: '',
        // familyId: '',
        // modelId: '',
        // brandName: '',
        // familyName: '',
        // modelName: '',
        // cityId: '',
        // // 提车量最大/最小值
        // buyCarMax: '',
        // buyCarMin: '',
        // // 价格区间(最小值/最大值)
        // priceRangeMax: '',
        // priceRangeMin: '',
        // // 保证金(最小值/最大值)
        // marginMin: '',
        // marginMax: '',
        // 是否权益包会员 1 是， 0: 否
        equityMember: '',
        // 未出价天数（最大/最小）
        noBidDaysMax: '',
        noBidDaysMin: '',
        // 是否系统推荐 1 是， 0: 否
        systemRecommend: 1,
        // parentLabelCode: '',
        // childLabelCode: '',
        // 分页参数
        currentPage: 1,
        pageSize: 20
    },
    totalNum: 0,
    dealerList: [],
    totalDealerNum: 0,
    noNextDealerList: false
});

export default {
    namespace: 'recommendToday',

    state: initState(),

    reducers: {
        /**
         * 重置状态
         * @param   {object}               state 当前state
         * @returns {object}                     更新过的state
         */
        resetState(state) {
            return {
                ...initState(),
                tagList: state.tagList,
                auctionTagList: state.auctionTagList
            };
        },

        /**
         * 更新状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateState(state, action) {
            return {
                ...state,
                ...action.payload
            };
        },

        /**
         * 更新状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateFormDataState(state, action) {
            return {
                ...state,
                formData: {
                    ...state.formData,
                    ...action.payload
                }
            };
        },

        /**
         * 更新状态
         *
         * @param   {object}               state          当前state
         * @param   {object}               action         入参
         * @param   {object}               action.payload 传入需要合并的state
         * @returns {object}                              更新过的state
         */
        updateDealerFormDataState(state, action) {
            return {
                ...state,
                dealerFormData: {
                    ...state.dealerFormData,
                    ...action.payload
                }
            };
        }
    },

    effects: {
        // /**
        //  * 获取客户类型标签数据
        //  *
        //  * @param   {object}               param0         入参
        //  * @param   {object}               param0.payload 请求参数
        //  * @param   {object}               param1         redux-saga/effects
        //  * @param   {Function}             param1.call    call
        //  * @param   {Function}             param1.put     put
        //  * @returns {object}                              用户信息
        //  */
        // *getDealerTagList({ payload }, { call, put }) {
        //     const { result } = yield call(api.getDealerLabelList, payload);
        //     const tagList = Array.isArray(result) ? result : [];
        //     yield put({
        //         type: 'updateState',
        //         payload: {
        //             tagList
        //         }
        //     });
        // },
        /**
         * 获取拍品标签数据
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @returns {object}                              用户信息
         */
        *getAuctionTagList({ payload }, { call, put }) {
            const result = yield call(api.getAuctionTagList, payload);
            const tagList = Array.isArray(result) ? result : [];
            yield put({
                type: 'updateState',
                payload: {
                    auctionTagList: tagList
                }
            });
        },
        /**
         * 获取推荐车源数据
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @param   {Function}             param1.select  select
         * @returns {object}                              用户信息
         */
        *getTodayRecommendList({ payload }, { call, put, select }) {
            const dataState = yield select((state) => state.recommendToday);
            const { result } = yield call(api.getTodayRecommendList, payload);
            const { currentPage = 1, dataList = [], totalNum = 0 } = result || {};
            // 车源去重
            const combinedList = Array.isArray(dataList)
                ? currentPage == 1
                    ? dataList
                    : dataState.dataList.concat(dataList)
                : [];
            // 使用 Map 进行去重
            const idMap = new Map();
            const list = combinedList.filter((car) => {
                if (idMap.has(car.auctionId)) {
                    return false;
                }
                idMap.set(car.auctionId, true);
                return true;
            });

            yield put({
                type: 'updateState',
                payload: {
                    dataList: list,
                    noNextDataList: dataList.length < dataState.formData.pageSize,
                    formData: {
                        ...dataState.formData,
                        currentPage: payload.currentPage
                    },
                    totalNum
                }
            });
        },

        /**
         * 获取商家数据
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @param   {Function}             param1.put     put
         * @param   {Function}             param1.select  select
         * @returns {object}                              用户信息
         */
        *getDealerList({ payload }, { call, put, select }) {
            const dataState = yield select((state) => state.recommendToday);
            const { result } = yield call(api.getDealerList, payload);
            const { currentPage = 1, dataList = [], totalNum } = result || {};

            // 车商去重
            const combinedList = Array.isArray(dataList)
                ? currentPage == 1
                    ? dataList
                    : dataState.dealerList.concat(dataList)
                : [];
            // 使用 Map 进行去重
            const idMap = new Map();
            const list = combinedList.filter((item) => {
                if (idMap.has(item.dealerId)) {
                    return false;
                }
                idMap.set(item.dealerId, true);
                return true;
            });

            yield put({
                type: 'updateState',
                payload: {
                    dealerList: list,
                    noNextDealerList: dataList.length < dataState.dealerFormData.pageSize,
                    dealerFormData: {
                        ...dataState.dealerFormData,
                        currentPage: payload.currentPage
                    },
                    totalDealerNum: totalNum
                }
            });
        },

        /**
         * 推荐车源给车商
         *
         * @param   {object}               param0         入参
         * @param   {object}               param0.payload 请求参数
         * @param   {object}               param1         redux-saga/effects
         * @param   {Function}             param1.call    call
         * @returns {object}                              用户信息
         */
        *pushToDealer({ payload }, { call }) {
            return yield call(api.pushToDealer, payload);
        }
    }
};
