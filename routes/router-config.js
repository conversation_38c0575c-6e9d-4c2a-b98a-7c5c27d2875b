/* eslint-disable jsdoc/require-jsdoc */
/**
 * 路由配置文件 module
 * <AUTHOR>
 * @module   routes/router-config
 */

/**
 * PAGE_URL
 *
 * @type {Array}
 */
export const PAGE_URL = {
    // 首页
    home: '/pages/home',
    // 登录
    login: '/pages/login',
    // 我的客户-我的客户
    myClient: '/pages/my/client',
    // 我的客户-商家详情
    myClientDealer: '/pages/my/client/dealer',
    // 我的客户-跟进记录
    myLog: '/pages/my/client/log',
    // 我的客户-跟进记录-详情
    myLogDetail: '/pages/my/client/log/detail',
    // 我的客户-我的工作
    myWork: '/pages/my/work',
    // 我的客户-我的工作轨迹
    myWorkTrace: '/pages/working/track',
    // 我的客户-我的工作绩效历史
    myWorkHistory: '/pages/my/kpi/history',
    // 我的客户-我的工作
    myWorkFollowRecord: '/pages/my/work/followRecord',
    // 我的团队-团队绩效
    managerPanel: '/pages/manager/panel',
    // 我的团队-团队绩效-业绩看板
    managerPerformancePanel: '/pages/manager/performance/panel',
    // 我的团队-绩效任务
    managerKpi: '/pages/manager/kpi',
    // 我的团队-历史绩效
    manageKpiHistory: '/pages/manager/kpi/history',
    // 车源智推-今日推荐
    recommendToday: '/pages/recommend/today',
    // 车源智推-历史推荐
    recommendHistory: '/pages/recommend/history'
};

/**
 * ASYNC_ROUTE_CONFIG 异步路由配置
 *
 * @type {Array}
 */
export const ASYNC_ROUTE_CONFIG = [
    {
        path: 'home',
        models: {
            home: () => import(/* webpackChunkName: "model" */ 'routes/home/<USER>')
        },
        component: () => import(/* webpackChunkName: "cmpt" */ 'routes/home/<USER>'),
        title: '车商CRM-天天拍车',
        isAuth: true
    },
    {
        path: 'login',
        models: {
            // app: () => import(/* webpackChunkName: "model" */ 'models/app.js')
        },
        component: () => import(/* webpackChunkName: "cmpt" */ 'routes/login/index.js'),
        title: '登录-天天拍车',
        isAuth: false
    },
    {
        path: 'myClient',
        models: {
            myClient: () => import(/* webpackChunkName: "model" */ 'routes/my-client/model.js')
        },
        component: () => import(/* webpackChunkName: "cmpt" */ 'routes/my-client/index.js'),
        title: '我的客户-天天拍车',
        isAuth: true
    },
    {
        path: 'myClientDealer',
        models: {
            myClient: () => import(/* webpackChunkName: "model" */ 'routes/my-client/model.js'),
            myClientDealer: () => import(/* webpackChunkName: "model" */ 'routes/my-client-dealer/model.js')
        },
        component: () => import(/* webpackChunkName: "cmpt" */ 'routes/my-client-dealer/index.js'),
        title: '我的客户-天天拍车',
        isAuth: true
    },
    {
        path: 'myLog',
        models: {
            myLog: () => import(/* webpackChunkName: "model" */ 'routes/my-follow-up-log/model.js')
        },
        component: () => import(/* webpackChunkName: "cmpt" */ 'routes/my-follow-up-log/index.js'),
        title: '跟进记录-天天拍车',
        isAuth: true
    },
    {
        path: 'myLogDetail',
        models: {
            myLogDetail: () =>
                import(/* webpackChunkName: "model" */ 'routes/my-follow-up-log-detail/model.js')
        },
        component: () => import(/* webpackChunkName: "cmpt" */ 'routes/my-follow-up-log-detail/index.js'),
        title: '跟进记录详情-天天拍车',
        isAuth: true
    },
    {
        path: 'myWork',
        models: {
            myWork: () => import(/* webpackChunkName: "model" */ 'routes/my-work/model.js')
        },
        component: () => import(/* webpackChunkName: "cmpt" */ 'routes/my-work/index.js'),
        title: '我的业绩-我的工作-天天拍车',
        isAuth: true
    },
    {
        path: 'myWorkTrace',
        models: {
            myWorkTrace: () => import(/* webpackChunkName: "model" */ 'routes/my-work-trace/model.js')
        },
        component: () => import(/* webpackChunkName: "cmpt" */ 'routes/my-work-trace/index.js'),
        title: '我的工作轨迹-我的业绩-天天拍车',
        isAuth: true
    },
    {
        path: 'myWorkHistory',
        models: {
            myWorkHistory: () => import(/* webpackChunkName: "model" */ 'routes/my-work-history/model.js')
        },
        component: () => import(/* webpackChunkName: "cmpt" */ 'routes/my-work-history/index.js'),
        title: '更多绩效-我的业绩-天天拍车',
        isAuth: true
    },
    {
        path: 'myWorkFollowRecord',
        models: {
            myWorkFollowRecord: () =>
                import(/* webpackChunkName: "model" */ 'routes/my-work-follow-record/model.js')
        },
        component: () => import(/* webpackChunkName: "cmpt" */ 'routes/my-work-follow-record/index.js'),
        title: '客户跟进更多记录-我的业绩-天天拍车',
        isAuth: true
    },
    {
        path: 'managerPanel',
        models: {
            managerPanel: () => import(/* webpackChunkName: "model" */ 'routes/manager-panel/model.js')
        },
        component: () => import(/* webpackChunkName: "cmpt" */ 'routes/manager-panel/index.js'),
        title: '团队绩效-我的团队-天天拍车',
        isAuth: true
    },
    {
        path: 'managerPerformancePanel',
        models: {
            managerPerformancePanel: () =>
                import(/* webpackChunkName: "model" */ 'routes/manager-performance-panel/model.js')
        },
        component: () => import(/* webpackChunkName: "cmpt" */ 'routes/manager-performance-panel/index.js'),
        title: '团队绩效-业绩看板-天天拍车',
        isAuth: true
    },
    {
        path: 'managerKpi',
        models: {
            managerKpi: () => import(/* webpackChunkName: "model" */ 'routes/manager-kpi/model.js')
        },
        component: () => import(/* webpackChunkName: "cmpt" */ 'routes/manager-kpi/index.js'),
        title: '团队绩效详情-我的团队-天天拍车',
        isAuth: true
    },
    {
        path: 'manageKpiHistory',
        models: {
            manageKpiHistory: () => import(/* webpackChunkName: "model" */ 'routes/my-work-history/model.js')
        },
        component: () => import(/* webpackChunkName: "cmpt" */ 'routes/my-work-history/index.js'),
        title: '更多绩效-我的团队-天天拍车',
        isAuth: true
    },
    {
        path: 'recommendToday',
        models: {
            recommendToday: () => import(/* webpackChunkName: "model" */ 'routes/recommend-today/model.js')
        },
        component: () => import(/* webpackChunkName: "cmpt" */ 'routes/recommend-today/index.js'),
        title: '今日推荐-车源智推-天天拍车',
        isAuth: true
    },
    {
        path: 'recommendHistory',
        models: {
            recommendHistory: () =>
                import(/* webpackChunkName: "model" */ 'routes/recommend-history/model.js')
        },
        component: () => import(/* webpackChunkName: "cmpt" */ 'routes/recommend-history/index.js'),
        title: '历史推荐-车源智推-天天拍车',
        isAuth: true
    }
];
