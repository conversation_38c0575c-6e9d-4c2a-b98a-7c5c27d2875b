/**
 * 路由文件   module
 * <AUTHOR>
 * @module   routes/router
 */
import React, { lazy, Suspense } from 'react';
import { Route, Switch, routerRedux } from 'dva/router';
import dynamic from 'dva/dynamic';
import { message, Loader } from 'ttp-library';
import { ASYNC_ROUTE_CONFIG, PAGE_URL } from './router-config';
import SetTitle from 'components/set-global-title';
import ErrorBoundary from 'components/error-boundary';
import NoMatch from 'components/no-match';
import PrivateRouter from './private-router';

const { ConnectedRouter } = routerRedux;

/**
 * 异步路由载入失败报错
 * <AUTHOR>
 * @function loadError
 * @param    {object}                 e 错误对象
 * @returns   {void}                     抛出
 */
const loadError = (e) => {
    console.error('router-load-error: ', e);
    message('页面载入失败，请刷新重试。', 60);
    return;
};

/**
 * 判断 model 是否已经载入过
 * <AUTHOR>
 * @function modelNotExisted
 * @param    {object}                 app   dva实例
 * @param    {Array}                  model dva modal配置
 * @returns   {boolean}             返回是否已经载入过
 */
const modelNotExisted = (app, model) => {
    // eslint-disable-next-line
    return !app._models.some(({ namespace }) => {
        return namespace === model.substring(model.lastIndexOf('/') + 1);
    });
};

/**
 * 异步载入 models 和 component
 *
 * @function dynamicWrapper
 * @param    {object}                 app       dva实例
 * @param    {Array}                  models     dva model数组
 * @param    {string}                 component 异步载入模块名
 * @returns   {object}                           Route的component对象
 */
const dynamicWrapper = (app, models, component) => {
    /**
     * 异步渲染组件
     * @returns   {object}              react component
     */
    const AsyncComponent = () => component().catch(loadError);

    return dynamic({
        app,
        // eslint-disable-next-line jsdoc/require-jsdoc
        models: () =>
            Object.keys(models)
                .filter((model) => modelNotExisted(app, model))
                .map((m) => models[m]().catch(loadError)),
        // eslint-disable-next-line jsdoc/require-jsdoc
        component: () => lazy(AsyncComponent)
    });
};

/**
 * 路由配置
 * <AUTHOR>
 * @function RouterConfig
 * @param    {object}                 options         参数对象
 * @param    {Function}               options.history history函数
 * @param    {object}                 options.app     dva实例
 * @returns  {Function}                                    路由配置
 */
const RouterConfig = ({ history, app }) => {
    const routes = ASYNC_ROUTE_CONFIG.map((m) => ({
        ...m,
        key: m.path,
        exact: true,
        path: PAGE_URL[m.path],
        goBanck: PAGE_URL[m.goBanck],
        component: dynamicWrapper(app, m.models, m.component)
    }));
    // const cacheRoute = routes.filter((ele) => ele.isCache === true);
    return (
        <ErrorBoundary>
            <ConnectedRouter history={history}>
                <Switch>
                    <Route exact path='/' render={() => history.push(PAGE_URL.home)} />
                    {routes.map(({ component, key, ...res }) => (
                        <Route
                            {...res}
                            key={key}
                            render={({ location }) => (
                                <Suspense fallback={<Loader open={true} />}>
                                    <PrivateRouter
                                        {...res}
                                        component={component}
                                        history={history}
                                        location={location}
                                    />
                                </Suspense>
                            )}
                        />
                    ))}
                    <Route exact path='/pages/403'>
                        <SetTitle title='403'>
                            <NoMatch type={403} />
                        </SetTitle>
                    </Route>
                    <Route path='*'>
                        <SetTitle title='404'>
                            <NoMatch type={404} />
                        </SetTitle>
                    </Route>
                </Switch>
            </ConnectedRouter>
        </ErrorBoundary>
    );
};

export default RouterConfig;
