/**
 * 总入口
 */
import dva from 'dva';
import createLoading from 'dva-loading';
import { createBrowserHistory } from 'history';
import { isEmpty, isEmptyObject, isFun } from 'ttp-utils';
import { hijackGesture, replaceImgByDef, addMark } from 'common/js/utils.js';
import { PAGE_URL } from 'routes/router-config.js';
// 组件
import { message } from 'ttp-library';
// less
import '../less/index.less';

const BrowserHistory = createBrowserHistory();

/**
 * 初始化app
 */
const initApp = () => {
    // 1. Initialize
    const app = dva({
        /**
         * onError 捕获全局错误，勿删
         * @param {object}            error error
         */
        onError: (error) => {
            console.error('app-onError', error);
            if (!isEmptyObject(error) && /\d/.test(error.code) && !isEmpty(error.message)) {
                // 参考 https://github.com/dvajs/dva/issues/1222
                // dvajs提供的统一的错误处理方式，
                // 没有登录
                if (error.code == 700) {
                    app._store.dispatch({ type: 'app/logout' });
                    // 记录当前URL
                    const from = BrowserHistory.location.pathname;
                    const search = BrowserHistory.location.search;
                    message({
                        text: '您的登录信息失效了，请重新登录。',
                        time: 3,
                        // eslint-disable-next-line jsdoc/require-jsdoc
                        onClose: () => {
                            BrowserHistory.push({ pathname: PAGE_URL.login, state: { from, search } });
                        }
                    });
                }

                // 无接口权限
                if (error.code == 705) {
                    message({
                        text: '您的没有访问权限，请切换用户或重新登录。',
                        time: 3,
                        // eslint-disable-next-line jsdoc/require-jsdoc
                        onClose: () => {
                            BrowserHistory.push({ pathname: '/pages/403' });
                        }
                    });
                }

                // 其他错误提示
                if (error.code !== 700 && error.code !== 705) {
                    message(error.message || '系统异常，请刷新重试！', 3);
                }
            }
            // 错误上报
            if (error instanceof Error && isFun(reportError)) {
                error.reportedBySDK = true;
                reportError(error);
            }
        },
        history: BrowserHistory
    });
    // 2. Plugins
    app.use(createLoading());
    // 3. Register models
    app.model(require('models/app').default);
    // 4. Router
    app.router(require('routes/router').default);
    // 5. Start
    app.start('#app');
};

// 页面初始化
window.addEventListener('load', () => {
    // 替换图片
    replaceImgByDef();
    // 劫持ios的侧滑返回
    hijackGesture();
    // 添加 uuid
    addMark();
    // 初始化app
    initApp();
});
