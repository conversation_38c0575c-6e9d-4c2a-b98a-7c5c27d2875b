@import '../../common/less/validation.less';
@import '../../common/less/mixins.less';
@import '../../common/less/setrem.less';
@import '../../common/less/icon.less';
@import '../../common/less/public.less';
@import '../../common/less/dropdown.less';

:global {
    body {
        background: @color-page-bg;
        max-width: 750px;
        margin: 0 auto;
        font-size: @def-font-size;
        line-height: @def-line-height;
    }

    // 给弹框内容一个最小高度，好看一点
    .Saitama-alert {
        .Saitama-modal-body {
            min-height: 2rem;
        }
    }

    .init-loading {
        position: fixed;
        z-index: 1;
        top: 46%;
        left: 50%;
        transform: translate(-50%, -46%);

        background: url('../images/loading-page.gif') no-repeat center center;
        background-repeat: no-repeat;
        background-attachment: scroll;
        background-position: 0 0;
        width: (160 / @rem);
        height: (62 / @rem);
        background-size: contain;
    }

    .init-loading-txt {
        position: fixed;
        z-index: 1;
        top: 54%;
        left: 50%;
        transform: translate(-50%, -54%);

        text-align: center;
        font-size: (12 / @rem);
        color: #333;
    }
}
