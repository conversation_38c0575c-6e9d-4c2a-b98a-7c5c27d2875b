/* eslint-disable jsdoc/require-jsdoc */
var getWebpackConfig = require('ttpai-config');
var { merge } = require('webpack-merge');
// 在的设置将覆盖 package.json 中 ttpai-config 的配置
var option = {
    runMode: 'build',
    // plugin: {
    //     analyzer: true
    // },
    loaderOptions: {
        'css-loader': {
            modules: {
                auto: (resourcePath) => !/node_modules/.test(resourcePath),
                mode: 'local',
                localIdentName: '[local]_[hash:8]'
            }
        }
    }
};
module.exports = merge(getWebpackConfig(option), {
    // 可自定义配置
});
