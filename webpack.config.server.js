var getWebpackConfig = require('ttpai-config');
var option = {
    runMode: 'server',
    devServer: {
        host: '127.0.0.1',
        port: 9997,
        mockConfig: {
            '67c55f45e78ba70abbbd35d0/mockapi': ['/api', '/file']
        },
        open: '/pages/login'
    },
    loaderOptions: {
        'css-loader': {
            modules: {
                // eslint-disable-next-line jsdoc/require-jsdoc
                auto: (resourcePath) => !/node_modules/.test(resourcePath),
                mode: 'local',
                localIdentName: '[local]_[hash:8]'
            }
        }
    },
    hbsData: {
        sdk: false
    }
};
module.exports = getWebpackConfig(option);
